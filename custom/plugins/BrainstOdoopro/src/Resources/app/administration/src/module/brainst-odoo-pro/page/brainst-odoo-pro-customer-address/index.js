import template from './brainst-odoo-pro-customer-address.html.twig';

const Criteria = Shopware.Data.Criteria;

Shopware.Component.register('brainst-odoo-pro-customer-address', {
    template,

    inject: ['repositoryFactory'],

    data() {
        return {
            odooUrl: null,
            page: 1,
            limit: 25,
            brainstOdooEntries: null,
            total: 0,
            isLoading: true,
            currentLanguageId: Shopware.Context.api.languageId
        };
    },

    metaInfo() {
        return {
            title: this.$createTitle()
        };
    },

    created() {
        this.fetchPluginUrl();
        this.getList();
    },

    computed: {
        brainstOdooRepository() {
            return this.repositoryFactory.create('brainst_odoo');
        },
        systemConfigRepository() {
            return this.repositoryFactory.create('system_config');
        },
        customerAddressRepository() {
            return this.repositoryFactory.create('customer_address');
        },

        columns() {
            return [
                {
                    allowResize: true,
                    dataIndex: "customerAddress.firstName",
                    label: this.$tc('brainst-odoo-pro.customer-address.table.name'),
                    primary: true,
                    property: "customerAddress.firstName",
                },
                {
                    allowResize: true,
                    dataIndex: "odooId",
                    label: this.$tc('brainst-odoo-pro.customer-address.table.odoo-id'),
                    property: "odooId",
                    naturalSorting: true
                }
            ];
        }
    },

    methods: {
        getList() {
            this.isLoading = true;
            const criteria = new Criteria(this.page, this.limit);
            criteria.addFilter(Criteria.equals('module', 'customer_address'));
            criteria.addAssociation('customerAddress');
            criteria.addFields('recordId', 'odooId', 'customerAddress.firstName', 'customerAddress.lastName', 'customerAddress.street', 'customerAddress.customerId');
            criteria.addSorting(Criteria.sort('customerAddress.firstName', 'ASC', false));

            return this.brainstOdooRepository.search(criteria, Shopware.Context.api)
                .then((result) => {
                    this.total = result.total;
                    this.brainstOdooEntries = result;
                    this.isLoading = false;
                })
                .catch(error => {
                    console.error('Failed to fetch Data:', error);
                });
        },
        fetchPluginUrl() {
            const criteria = new Criteria();
            criteria.addFilter(Criteria.equals('configurationKey', 'BrainstOdooPro.config.serverUrl'));

            this.systemConfigRepository.search(criteria, Shopware.Context.api)
                .then((result) => {
                    this.odooUrl = result.first().configurationValue;
                })
                .catch(error => {
                    console.error('Failed to fetch the URL:', error);
                });
        }
    }
});
