import template from './brainst-odoo-pro-gauge-chart.html.twig';
import './brainst-odoo-pro-gauge-chart.scss';

Shopware.Component.register('brainst-odoo-pro-gauge-chart', {
    template,

    props: {
        value: {
            type: Number,
            required: true,
            default: 0
        },
        maxValue: {
            type: Number,
            required: true,
            default: 100
        },
        title: {
            type: String,
            required: true,
            default: ''
        },
        unit: {
            type: String,
            required: false,
            default: ''
        },
        displayValue: {
            type: String,
            required: false,
            default: null
        },
        isLoading: {
            type: Boolean,
            required: false,
            default: false
        }
    },

    computed: {
        percentage() {
            return Math.min((this.value / this.maxValue) * 100, 100);
        },

        needleAngle() {
            // Gauge spans 180 degrees (180 to 0, so 0 is on left and max is on right)
            // Convert percentage to angle for vertical semicircle
            return 180 - (this.percentage / 100) * 180;
        },

        gaugeSegments() {
            const segments = [];
            const totalSegments = 5; // 5 colored segments
            const segmentAngle = 180 / totalSegments; // 36 degrees each

            const colors = [
                '#ef4444', // Red (0-20%)
                '#f59e0b', // Orange (20-40%)
                '#eab308', // Yellow (40-60%)
                '#84cc16', // Light green (60-80%)
                '#22c55e'  // Green (80-100%)
            ];

            for (let i = 0; i < totalSegments; i++) {
                // Start from 180 degrees (left) to 0 degrees (right) for vertical semicircle
                // So 0 value is on left, max value is on right
                const startAngle = 180 - (i * segmentAngle);
                const endAngle = startAngle - segmentAngle;

                segments.push({
                    startAngle: endAngle,
                    endAngle: startAngle,
                    color: colors[i],
                    path: this.createArcPath(endAngle, startAngle, 30, 45),
                    isActive: this.percentage >= (i * 20) // Each segment represents 20%
                });
            }

            return segments;
        },

        scaleMarks() {
            const marks = [];
            const totalMarks = 6; // 0, 20, 40, 60, 80, 100 (or max value)

            for (let i = 0; i < totalMarks; i++) {
                const percentage = (i / (totalMarks - 1)) * 100;
                // Reverse the angle so 0% is at 180° (left) and 100% is at 0° (right)
                const angle = 180 - (percentage / 100) * 180;
                const value = Math.round((percentage / 100) * this.maxValue);

                // Position marks further outside the colored segments
                const radius = 55; // Good distance from gauge with expanded viewBox
                const angleRad = angle * Math.PI / 180;
                const textX = 50 + radius * Math.cos(angleRad);
                const textY = 50 - radius * Math.sin(angleRad); // Negative sin for upward semicircle

                marks.push({
                    value,
                    angle,
                    textX,
                    textY,
                    percentage
                });
            }

            return marks;
        },

        needlePath() {
            const angle = this.needleAngle;
            const centerX = 50;
            const centerY = 50;
            const needleLength = 28;
            const needleWidth = 0.8;

            const angleRad = angle * Math.PI / 180;
            const tipX = centerX + needleLength * Math.cos(angleRad);
            const tipY = centerY - needleLength * Math.sin(angleRad); // Negative sin for upward semicircle

            const baseAngle1 = (angle - 90) * Math.PI / 180;
            const baseAngle2 = (angle + 90) * Math.PI / 180;

            const base1X = centerX + needleWidth * Math.cos(baseAngle1);
            const base1Y = centerY - needleWidth * Math.sin(baseAngle1);
            const base2X = centerX + needleWidth * Math.cos(baseAngle2);
            const base2Y = centerY - needleWidth * Math.sin(baseAngle2);

            return `M ${base1X} ${base1Y} L ${tipX} ${tipY} L ${base2X} ${base2Y} Z`;
        },

        displayText() {
            return this.displayValue || this.value.toString();
        },

        activeSegmentColor() {
            if (!this.gaugeSegments || this.gaugeSegments.length === 0) {
                return '#6b7280';
            }

            // Find the last active segment (highest value)
            let activeSegment = null;
            for (let i = this.gaugeSegments.length - 1; i >= 0; i--) {
                if (this.gaugeSegments[i].isActive) {
                    activeSegment = this.gaugeSegments[i];
                    break;
                }
            }

            return activeSegment ? activeSegment.color : '#6b7280';
        }
    },

    methods: {

        createArcPath(startAngle, endAngle, innerRadius, outerRadius) {
            const centerX = 50;
            const centerY = 50;

            const startAngleRad = startAngle * Math.PI / 180;
            const endAngleRad = endAngle * Math.PI / 180;

            // For vertical semicircle, use negative sin to flip upward
            const x1 = centerX + innerRadius * Math.cos(startAngleRad);
            const y1 = centerY - innerRadius * Math.sin(startAngleRad);
            const x2 = centerX + outerRadius * Math.cos(startAngleRad);
            const y2 = centerY - outerRadius * Math.sin(startAngleRad);

            const x3 = centerX + outerRadius * Math.cos(endAngleRad);
            const y3 = centerY - outerRadius * Math.sin(endAngleRad);
            const x4 = centerX + innerRadius * Math.cos(endAngleRad);
            const y4 = centerY - innerRadius * Math.sin(endAngleRad);

            const largeArcFlag = endAngle - startAngle > 180 ? 1 : 0;

            return `M ${x1} ${y1}
                    L ${x2} ${y2}
                    A ${outerRadius} ${outerRadius} 0 ${largeArcFlag} 1 ${x3} ${y3}
                    L ${x4} ${y4}
                    A ${innerRadius} ${innerRadius} 0 ${largeArcFlag} 0 ${x1} ${y1} Z`;
        }
    }
});
