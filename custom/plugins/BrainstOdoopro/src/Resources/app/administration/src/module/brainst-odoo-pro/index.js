import './page/brainst-odoo-pro-list';
import './page/brainst-odoo-pro-sales-channel';
import './page/brainst-odoo-pro-category';
import './page/brainst-odoo-pro-product';
import './page/brainst-odoo-pro-property';
import './page/brainst-odoo-pro-attribute-value';
import './page/brainst-odoo-pro-customer';
import './page/brainst-odoo-pro-customer-address';
import './page/brainst-odoo-pro-order';
import './page/brainst-odoo-pro-delivery';
import './page/brainst-odoo-pro-transaction';
import './component/brainst-odoo-pro-synchronize-button'
import './component/brainst-odoo-pro-progress-bar'
import './component/brainst-odoo-pro-back-button'
import './component/brainst-odoo-pro-gauge-chart'

import deDE from './snippet/de-DE';
import enGB from './snippet/en-GB';

const {Module} = Shopware;

Module.register('brainst-odoo-pro', {
    type: 'plugin',
    name: 'brainst-odoo-pro.title',
    title: 'brainst-odoo-pro.title',
    description: 'brainst-odoo-pro.description',
    color: '#9AA8B5',
    icon: 'regular-cog',

    snippets: {
        'de-DE': deDE,
        'en-GB': enGB
    },

    routes: {
        list: {
            component: 'brainst-odoo-pro-list',
            path: 'list',
            meta: {
                parentPath: 'sw.settings.index.plugins'
            }
        },
        category: {
            component: 'brainst-odoo-pro-category',
            path: 'category',
            meta: {
                parentPath: 'brainst.odoo.pro.list'
            }
        },
        product: {
            component: 'brainst-odoo-pro-product',
            path: 'product',
            meta: {
                parentPath: 'brainst.odoo.pro.list'
            }
        },
        property: {
            component: 'brainst-odoo-pro-property',
            path: 'property',
            meta: {
                parentPath: 'brainst.odoo.pro.list'
            }
        },
        customer: {
            component: 'brainst-odoo-pro-customer',
            path: 'customer',
            meta: {
                parentPath: 'brainst.odoo.pro.list'
            }
        },
        order: {
            component: 'brainst-odoo-pro-order',
            path: 'order',
            meta: {
                parentPath: 'brainst.odoo.pro.list'
            }
        },
        'sales-channel': {
            component: 'brainst-odoo-pro-sales-channel',
            path: 'sales-channel',
            meta: {
                parentPath: 'brainst.odoo.pro.list'
            }
        },

        'attribute-value': {
            component: 'brainst-odoo-pro-attribute-value',
            path: 'attribute-value',
            meta: {
                parentPath: 'brainst.odoo.pro.list'
            }
        },
        'customer-address': {
            component: 'brainst-odoo-pro-customer-address',
            path: 'customer-address',
            meta: {
                parentPath: 'brainst.odoo.pro.list'
            }
        },
        delivery: {
            component: 'brainst-odoo-pro-delivery',
            path: 'delivery',
            meta: {
                parentPath: 'brainst.odoo.pro.list'
            }
        },
        transaction: {
            component: 'brainst-odoo-pro-transaction',
            path: 'transaction',
            meta: {
                parentPath: 'brainst.odoo.pro.list'
            }
        }
    },

    settingsItem: [
        {
            group: 'plugins',
            to: 'brainst.odoo.pro.list',
            iconComponent: 'brainst-odoo-pro-icon',
            backgroundEnabled: true,
            label: 'brainst-odoo-pro.title'
        }
    ],

    extensionEntryRoute: {
        extensionName: 'BrainstOdooPro',
        route: 'brainst.odoo.pro.list'
    }
});
