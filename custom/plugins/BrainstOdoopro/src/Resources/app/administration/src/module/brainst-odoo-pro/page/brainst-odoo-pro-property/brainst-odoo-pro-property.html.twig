<sw-page>
    <template #smart-bar-back>
        {% block sw_page_slot_smart_bar_back %}
            <brainst-odoo-pro-back-button />
        {% endblock %}
    </template>

    <template #smart-bar-header>
        {% block brainst_odoo_table_property_smart_bar_header_title %}
            <h2>{{ $tc('brainst-odoo-pro.property.title') }}</h2>
        {% endblock %}
    </template>

    <template #content>
        {% block brainst_odoo_table_property_list_content %}
            <sw-alert variant="info" class="sw-alert--no-icon" style="margin-bottom: 20px;">
                <div class="sw-alert__message">
                    <strong>{{ $tc('brainst-odoo-pro.property.label') }}:</strong> {{ $tc('brainst-odoo-pro.property.note') }}
                </div>
            </sw-alert>
            <brainst-odoo-pro-progress-bar entity="property_group" :value="total"/>
            <sw-entity-listing
                    v-if="brainstOdooEntries"
                    :items="brainstOdooEntries"
                    :repository="brainstOdooRepository"
                    :showSelection="false"
                    :columns="columns"
                    :allowDelete="false">
                <template #column-recordId="{ item, column }">
                    <router-link
                            class="sw-data-grid__cell-value"
                            :to="{ name: 'sw.property.detail', params: { id: item.recordId } }"
                    >
                        {{ item.property?.name || item.recordId }}
                    </router-link>
                </template>
                <template #column-odooId="{ item, column }">
                    <a :href="`${odooUrl}/web#id=${item.odooId}&view_type=form&model=product.attribute`"
                       class="sw-data-grid__cell-value" rel="nofollow noopener"
                       target="_blank">{{ item.odooId }}</a>
                </template>
            </sw-entity-listing>
        {% endblock %}
    </template>
</sw-page>
