import template from './brainst-odoo-pro-customer.html.twig';

const Criteria = Shopware.Data.Criteria;

Shopware.Component.register('brainst-odoo-pro-customer', {
    template,

    inject: ['repositoryFactory'],

    data() {
        return {
            odooUrl: null,
            page: 1,
            limit: 25,
            brainstOdooEntries: null,
            total: 0,
            isLoading: true,
            currentLanguageId: Shopware.Context.api.languageId
        };
    },

    metaInfo() {
        return {
            title: this.$createTitle()
        };
    },

    created() {
        this.fetchPluginUrl();
        this.getList();
    },

    computed: {
        brainstOdooRepository() {
            return this.repositoryFactory.create('brainst_odoo');
        },
        systemConfigRepository() {
            return this.repositoryFactory.create('system_config');
        },
        customerRepository() {
            return this.repositoryFactory.create('customer');
        },

        columns() {
            return [
                {
                    allowResize: true,
                    dataIndex: "customer.firstName",
                    label: this.$tc('brainst-odoo-pro.customer.table.name'),
                    primary: true,
                    property: "customer.firstName",
                },
                {
                    allowResize: true,
                    dataIndex: "odooId",
                    label: this.$tc('brainst-odoo-pro.customer.table.odoo-id'),
                    property: "odooId",
                    naturalSorting: true
                }
            ];
        }
    },

    methods: {
        getList() {
            this.isLoading = true;
            const criteria = new Criteria(this.page, this.limit);
            criteria.addFilter(Criteria.equals('module', 'customer'));
            criteria.addAssociation('customer');
            criteria.addFields('recordId', 'odooId', 'customer.firstName', 'customer.lastName', 'customer.email');
            criteria.addSorting(Criteria.sort('customer.firstName', 'ASC', false));

            return this.brainstOdooRepository.search(criteria, Shopware.Context.api)
                .then((result) => {
                    this.total = result.total;
                    this.brainstOdooEntries = result;
                    this.isLoading = false;
                })
                .catch(error => {
                    console.error('Failed to fetch Data:', error);
                });
        },
        fetchPluginUrl() {
            const criteria = new Criteria();
            criteria.addFilter(Criteria.equals('configurationKey', 'BrainstOdooPro.config.serverUrl'));

            this.systemConfigRepository.search(criteria, Shopware.Context.api)
                .then((result) => {
                    this.odooUrl = result.first().configurationValue;
                })
                .catch(error => {
                    console.error('Failed to fetch the URL:', error);
                });
        }
    }
});
