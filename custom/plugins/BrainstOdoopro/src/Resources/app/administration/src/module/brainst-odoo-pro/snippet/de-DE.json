{"brainst-odoo-pro": {"title": "Brainst Odoo Pro", "description": "Shopware Odoo Synchronisierungsdaten von <PERSON>, Vertriebskanälen, Lieferung, Eigentum, Produkten, Kunden und Bestellungen.", "list": {"title": "Brainst Odoo Pro", "sales-channel": "Vertriebskanaltabelle", "category": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "product": "Produkttabelle", "property": "Eigenschaftstabelle", "attribute-value": "Eigenschaftswerttabelle", "customer": "Kundentabelle", "customer-address": "Kundenadresstabelle", "order": "Bestelltabelle", "delivery": "Liefertabell<PERSON>", "transaction": "Transaktionstabelle"}, "category": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "table": {"id": "Shopware-Kategorie-ID", "odoo-id": "Odoo-Kategorie-ID", "name": "<PERSON><PERSON><PERSON><PERSON>"}}, "product": {"title": "Produkttabelle", "tabs": {"template": "Produkte", "product": "<PERSON><PERSON><PERSON>"}, "notes": {"template": "Dies sind Hauptprodukte, die als Vorlagen in Odoo gespeichert werden.", "product": "Dies umfasst alle Varianten von Produkten und Produkte, die keine Varianten haben. Odoo erstellt für jedes Produkt eine Variante. Badge-Indikatoren: P = Hauptprodukt, V = Variante."}, "badges": {"parent": "Hauptprodukt", "variant": "<PERSON><PERSON><PERSON>"}, "table": {"id": "Shopware-Produkt-ID", "odoo-id": "Odoo-Produkt-ID", "name": "Produktname"}, "labels": {"id": "Produktvarianten-Identifikator in Shopware", "odoo-id": "Entsprechende Produktvarianten-ID im Odoo-System", "name": "Anzeigename mit Variantenoptionen (P = Hauptprodukt, V = Variante)"}}, "template": {"title": "Vorlagentabelle", "table": {"id": "Shopware-Produkt-ID", "odoo-id": "Odoo-Vorlage-ID", "name": "Produktname"}, "labels": {"id": "Hauptprodukt-Identifikator in Shopware", "odoo-id": "Entsprechende Vorlagen-ID im Odoo-System", "name": "Anzeigename des Hauptprodukts"}}, "property": {"title": "Eigenschaftstabelle", "label": "Eigenschaft", "note": "Shopware-Eigenschaften werden in Odoo als Attribute synchronisiert.", "table": {"id": "Shopware-Eigenschaft-ID", "odoo-id": "Odoo-Attribut-ID", "name": "Eigenschaftsname"}}, "customer": {"title": "Kundentabelle", "table": {"id": "Shopware-Kunden-ID", "odoo-id": "Odoo-Kunden-ID", "name": "Kundenname"}}, "order": {"title": "Bestelltabelle", "table": {"id": "Shopware-Bestell-ID", "odoo-id": "Odoo-Bestell-ID", "name": "Bestellnummer"}}, "sales-channel": {"title": "Vertriebskanaltabelle", "table": {"id": "Shopware-Vertriebskanal-ID", "odoo-id": "Odoo-Unternehmen-ID", "name": "Vertriebskanalname"}}, "attribute-value": {"title": "Eigenschaftswerttabelle", "label": "Eigenschaftswert", "note": "Shopware-Eigenschaftswerte werden in Odoo als Attributwerte synchronisiert.", "table": {"id": "Shopware-Eigenschaftswert-ID", "odoo-id": "Odoo-Attributwert-ID", "name": "Eigenschaftswertname"}}, "customer-address": {"title": "Kundenadresstabelle", "label": "Kundenadresse", "note": "Kundenadressen werden als untergeordnete Kontakte in Odoo synchronisiert, wobei der Shopware-Kunde als übergeordneter Kontakt fungiert.", "table": {"id": "Shopware-Adress-ID", "odoo-id": "Odoo-Kontakt-ID", "name": "Adressname"}}, "delivery": {"title": "Liefertabell<PERSON>", "table": {"id": "Shopware-Liefer-ID", "odoo-id": "Odoo-Liefer-ID", "name": "Bestellnummer"}}, "transaction": {"title": "Transaktionstabelle", "table": {"id": "Shopware-Transaktions-ID", "odoo-id": "Odoo-Zahlungs-ID", "name": "Bestellnummer"}}, "api-verify-button": {"title": "API-Test", "buttonLabel": "Überprüfen Sie die API-Verbindung", "success": "Verbindung mit Erfolg getestet", "error": "Verbindung konnte nicht hergestellt werden. Bitte überprüfen Sie die Zugangsdaten", "error_400": "<PERSON>s ist ein Fehler aufgetreten. Bitte überprüfen Sie die Zugangsdaten", "error_401": "Authentifizierung fehlgeschlagen. Bitte überprüfen Sie die Anmeldeinformationen.", "error_422": "<PERSON><PERSON> Version von Odoo wird nicht unterstützt, unterstützt nur Odoo 17 und 18.", "error_424": "Einige Module sind nicht installiert, bitte installieren", "error_500": "<PERSON><PERSON><PERSON>"}, "synchronize-button": {"title": "Synchroni<PERSON><PERSON>", "description": "Die Verarbeitung der Warteschlange wird einige Zeit in Anspruch nehmen. Führen Sie dies daher bitte aus, wenn die Nutzung Ihrer Website für einige Zeit minimal ist.<br/>Die geschätzte Zeit beträgt 1000 Produkte pro Stunde*.", "success": "Alle Entitäten werden zur Synchronisierung in die Warteschlange gestellt", "error_1": "<PERSON>te richten Sie die Odoo-Konfiguration ein und versuchen Sie es dann erneut", "error_2": "Bei der Synchronisierung ist ein Fehler aufgetreten. Bitte versuchen Sie es erneut", "error_3": "Sie haben die erste Synchronisierung bereits durchgeführt oder sind dabei", "confirmation": {"title": "Synchronisierung bestätigen", "message": "<PERSON><PERSON> <PERSON><PERSON>, dass Sie den Synchronisierungsprozess starten möchten? <PERSON><PERSON> gestartet, kann diese Aktion nicht rückgängig gemacht werden und synchronisiert alle Daten zwischen Shopware und Odoo.", "warning": "Dieser Prozess kann erhebliche Zeit in Anspruch nehmen und sollte während verkehrsarmer Zeiten durchgeführt werden.", "confirm": "<PERSON><PERSON>, Synchronisie<PERSON> starten", "cancel": "Abbrechen", "syncOrderFromDate": {"label": "Bestellungen synchronisieren ab Datum", "helpText": "Wählen Sie das Datum aus, ab dem Bestellungen synchronisiert werden sollen. Standard ist der 1. Januar des Vorjahres.", "placeholder": "Datum ausw<PERSON>hlen..."}}}, "progressBar": {"text": "Synchronisierungsfortschritt", "idle": "Bereit", "syncing": "Synchronisiert", "completed": "Abgeschlossen", "error": "<PERSON><PERSON>", "remaining": "verbleibend"}, "automation-modal": {"title": "Automatisierungskonfiguration", "description": "Konfigurieren Sie Automatisierungseinstellungen für die bidirektionale Synchronisierung zwischen Shopware und Odoo.", "toggleLabel": "Bidirektionale Synchronisierung aktivieren", "toggleHelpText": "<PERSON><PERSON> aktiv<PERSON>, werden Änderungen in Odoo automatisch mit Shopware synchronisiert", "saveButton": "Speichern", "success": "Automatisierungskonfiguration erfolgreich gespeichert", "error": "Fehler beim Erstellen der Automatisierungsregeln. Bitte überprüfen Sie Ihre Odoo-Verbindung und versuchen Sie es erneut."}, "dashboard": {"refresh": "Aktualisieren", "title": "Synchronisierungs-Dashboard", "totalSynced": "Gesamt Synchronisiert", "entityTypes": "Entitätstypen", "entitySynced": "Entität Synchronisiert", "syncStatus": "Synchronisierungsstatus", "overallProgress": "Gesamtfortschritt", "synced": "Synchronisiert", "pending": "<PERSON><PERSON><PERSON><PERSON>", "total": "Gesamt", "records": "Datensät<PERSON>", "gauge": {"title": "Sync-Rate", "recordsPerHour": "Datensätze/Stunde", "recordsPerMinute": "Datensätze/Minute", "hour": "Stunde", "minute": "Minute", "lastHour": "Letzte Stunde", "lastMinute": "Letzte Minute", "switchToHour": "Zur Stundenansicht wechseln", "switchToMinute": "Zur Minutenansicht wechseln"}, "entityNames": {"salesChannels": "Vertriebskanäle", "categories": "<PERSON><PERSON><PERSON>", "productVariants": "Produktvarianten", "products": "Produkte", "propertyGroups": "Eigenschaftsgruppen", "propertyGroupOptions": "Eigenschaftsgruppen-Optionen", "customers": "<PERSON><PERSON>", "customerAddresses": "Kundenadressen", "orders": "Bestellungen", "deliveries": "Lieferungen", "transactions": "Transaktionen"}, "tooltips": {"complete": "Alle Datensätze sind synchronisiert", "partial": "Einige Datensätze sind synchronisiert", "pending": "Noch keine Datensätze synchronisiert", "noData": "<PERSON><PERSON> ve<PERSON>ü<PERSON>"}}}}