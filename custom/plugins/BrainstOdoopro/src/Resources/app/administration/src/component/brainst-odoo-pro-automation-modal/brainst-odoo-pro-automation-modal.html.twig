{% block brainst_odoo_pro_automation_modal %}
    <sw-modal
            class="brainst-odoo-pro-automation-modal"
            :title="$tc('brainst-odoo-pro.automation-modal.title')"
            variant="small"
            @modal-close="onCloseModal"
    >
        {% block brainst_odoo_pro_automation_modal_body %}
            {% block brainst_odoo_pro_automation_modal_content %}
                <div class="brainst-odoo-pro-automation-modal__content">
                    {% block brainst_odoo_pro_automation_modal_description %}
                        <p class="brainst-odoo-pro-automation-modal__description">
                            {{ $tc('brainst-odoo-pro.automation-modal.description') }}
                        </p>
                    {% endblock %}

                    {% block brainst_odoo_pro_automation_modal_toggle %}
                        <div class="brainst-odoo-pro-automation-modal__toggle-container">
                            <sw-switch-field
                                    class="brainst-odoo-pro-automation-modal__toggle"
                                    :label="$tc('brainst-odoo-pro.automation-modal.toggleLabel')"
                                    v-model:value="enableTwoWaySync"
                                    :helpText="$tc('brainst-odoo-pro.automation-modal.toggleHelpText')"
                            />
                        </div>
                    {% endblock %}
                </div>
            {% endblock %}
        {% endblock %}

        {% block brainst_odoo_pro_automation_modal_footer %}
            <template #modal-footer>
                {% block brainst_odoo_pro_automation_modal_footer_cancel %}
                    <sw-button
                            size="small"
                            @click="onCloseModal"
                            :disabled="isLoading"
                    >
                        {{ $tc('global.default.cancel') }}
                    </sw-button>
                {% endblock %}

                {% block brainst_odoo_pro_automation_modal_footer_save %}
                    <sw-button-process
                            class="brainst-odoo-pro-automation-modal__save-button"
                            :isLoading="isLoading"
                            variant="primary"
                            size="small"
                            @click="onSave"
                    >
                        {{ $tc('brainst-odoo-pro.automation-modal.saveButton') }}
                    </sw-button-process>
                {% endblock %}
            </template>
        {% endblock %}
    </sw-modal>
{% endblock %}
