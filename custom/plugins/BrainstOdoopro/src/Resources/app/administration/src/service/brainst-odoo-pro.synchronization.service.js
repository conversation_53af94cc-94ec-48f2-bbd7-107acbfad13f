const ApiService = Shopware.Classes.ApiService;
class BrainstOdooProSynchronizationService extends ApiService {
    constructor(httpClient, loginService, apiEndpoint = 'brainst-odoo-pro') {
        super(httpClient, loginService, apiEndpoint);
    }

    synchronise(syncOrderFromDate = null) {
        const headers = this.getBasicHeaders({});
        const data = {};

        if (syncOrderFromDate) {
            data.syncOrderFromDate = syncOrderFromDate.toISOString().split('T')[0]; // Format as YYYY-MM-DD
        }

        return this.httpClient
            .post(
                `${this.getApiBasePath()}/sync`,
                data,
                {headers}
            )
            .then((response) => {
                return ApiService.handleResponse(response);
            });
    }
}

export default BrainstOdooProSynchronizationService;
