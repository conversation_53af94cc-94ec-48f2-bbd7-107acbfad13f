@import "~scss/variables";

.brainst-odoo-pro-sync-confirmation-modal {
    .brainst-odoo-pro-sync-confirmation-modal__content {
        padding: 20px 0;
    }

    .brainst-odoo-pro-sync-confirmation-modal__message {
        display: flex;
        flex-direction: column;
        align-items: center;
        text-align: center;
        gap: 16px;

        .warning-icon {
            color: #f59e0b;
            margin-bottom: 8px;
        }

        .confirmation-text {
            font-size: 16px;
            font-weight: 500;
            color: #374151;
            line-height: 1.5;
            margin: 0;
        }

        .warning-text {
            font-size: 14px;
            color: #6b7280;
            line-height: 1.4;
            margin: 0;
            font-style: italic;
        }
    }

    .brainst-odoo-pro-sync-confirmation-modal__date-field {
        margin-top: 24px;
        padding-top: 20px;
        border-top: 1px solid #e5e7eb;

        .sw-field--datepicker {
            margin-bottom: 0;
        }
    }

    .brainst-odoo-pro-sync-confirmation-modal__confirm-button {
        margin-left: 8px;
    }

    // Modal styling overrides
    .sw-modal__dialog {
        max-width: 500px;
    }

    .sw-modal__header {
        border-bottom: 1px solid #e5e7eb;
        padding-bottom: 16px;

        .sw-modal__title {
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
        }
    }

    .sw-modal__body {
        padding: 24px;
    }

    .sw-modal__footer {
        border-top: 1px solid #e5e7eb;
        padding-top: 16px;
        display: flex;
        justify-content: flex-end;
        gap: 8px;
    }
}

// Responsive design for smaller screens
@media (max-width: 768px) {
    .brainst-odoo-pro-sync-confirmation-modal {
        .sw-modal__dialog {
            max-width: 90vw;
            margin: 20px;
        }

        .brainst-odoo-pro-sync-confirmation-modal__message {
            .confirmation-text {
                font-size: 14px;
            }

            .warning-text {
                font-size: 13px;
            }
        }

        .sw-modal__footer {
            flex-direction: column-reverse;
            gap: 12px;

            .brainst-odoo-pro-sync-confirmation-modal__confirm-button {
                margin-left: 0;
                width: 100%;
            }

            .sw-button {
                width: 100%;
            }
        }
    }
}
