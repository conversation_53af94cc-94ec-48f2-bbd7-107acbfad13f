(function(){var e,t,o,r,n,a,s,i,l,d,c={55:function(){},923:function(){},364:function(){},162:function(){},324:function(){},377:function(){let{Component:e,Mixin:t,Filter:o}=Shopware;t.register("brainst-odoo-pro-helper",{computed:{assetFilter(){return Shopware.Filter.getByName("asset")}}})},192:function(e,t,o){var r=o(55);r.__esModule&&(r=r.default),"string"==typeof r&&(r=[[e.id,r,""]]),r.locals&&(e.exports=r.locals),o(346).Z("2c811542",r,!0,{})},841:function(e,t,o){var r=o(923);r.__esModule&&(r=r.default),"string"==typeof r&&(r=[[e.id,r,""]]),r.locals&&(e.exports=r.locals),o(346).Z("9e02aed2",r,!0,{})},91:function(e,t,o){var r=o(364);r.__esModule&&(r=r.default),"string"==typeof r&&(r=[[e.id,r,""]]),r.locals&&(e.exports=r.locals),o(346).Z("07d125ec",r,!0,{})},451:function(e,t,o){var r=o(162);r.__esModule&&(r=r.default),"string"==typeof r&&(r=[[e.id,r,""]]),r.locals&&(e.exports=r.locals),o(346).Z("7374bfd0",r,!0,{})},171:function(e,t,o){var r=o(324);r.__esModule&&(r=r.default),"string"==typeof r&&(r=[[e.id,r,""]]),r.locals&&(e.exports=r.locals),o(346).Z("38a8f328",r,!0,{})},346:function(e,t,o){"use strict";function r(e,t){for(var o=[],r={},n=0;n<t.length;n++){var a=t[n],s=a[0],i={id:e+":"+n,css:a[1],media:a[2],sourceMap:a[3]};r[s]?r[s].parts.push(i):o.push(r[s]={id:s,parts:[i]})}return o}o.d(t,{Z:function(){return h}});var n="undefined"!=typeof document;if("undefined"!=typeof DEBUG&&DEBUG&&!n)throw Error("vue-style-loader cannot be used in a non-browser environment. Use { target: 'node' } in your Webpack config to indicate a server-rendering environment.");var a={},s=n&&(document.head||document.getElementsByTagName("head")[0]),i=null,l=0,d=!1,c=function(){},p=null,u="data-vue-ssr-id",m="undefined"!=typeof navigator&&/msie [6-9]\b/.test(navigator.userAgent.toLowerCase());function h(e,t,o,n){d=o,p=n||{};var s=r(e,t);return b(s),function(t){for(var o=[],n=0;n<s.length;n++){var i=a[s[n].id];i.refs--,o.push(i)}t?b(s=r(e,t)):s=[];for(var n=0;n<o.length;n++){var i=o[n];if(0===i.refs){for(var l=0;l<i.parts.length;l++)i.parts[l]();delete a[i.id]}}}}function b(e){for(var t=0;t<e.length;t++){var o=e[t],r=a[o.id];if(r){r.refs++;for(var n=0;n<r.parts.length;n++)r.parts[n](o.parts[n]);for(;n<o.parts.length;n++)r.parts.push(y(o.parts[n]));r.parts.length>o.parts.length&&(r.parts.length=o.parts.length)}else{for(var s=[],n=0;n<o.parts.length;n++)s.push(y(o.parts[n]));a[o.id]={id:o.id,refs:1,parts:s}}}}function g(){var e=document.createElement("style");return e.type="text/css",s.appendChild(e),e}function y(e){var t,o,r=document.querySelector("style["+u+'~="'+e.id+'"]');if(r){if(d)return c;r.parentNode.removeChild(r)}if(m){var n=l++;t=v.bind(null,r=i||(i=g()),n,!1),o=v.bind(null,r,n,!0)}else t=_.bind(null,r=g()),o=function(){r.parentNode.removeChild(r)};return t(e),function(r){r?(r.css!==e.css||r.media!==e.media||r.sourceMap!==e.sourceMap)&&t(e=r):o()}}var f=function(){var e=[];return function(t,o){return e[t]=o,e.filter(Boolean).join("\n")}}();function v(e,t,o,r){var n=o?"":r.css;if(e.styleSheet)e.styleSheet.cssText=f(t,n);else{var a=document.createTextNode(n),s=e.childNodes;s[t]&&e.removeChild(s[t]),s.length?e.insertBefore(a,s[t]):e.appendChild(a)}}function _(e,t){var o=t.css,r=t.media,n=t.sourceMap;if(r&&e.setAttribute("media",r),p.ssrId&&e.setAttribute(u,t.id),n&&(o+="\n/*# sourceURL="+n.sources[0]+" */\n/*# sourceMappingURL=data:application/json;base64,"+btoa(unescape(encodeURIComponent(JSON.stringify(n))))+" */"),e.styleSheet)e.styleSheet.cssText=o;else{for(;e.firstChild;)e.removeChild(e.firstChild);e.appendChild(document.createTextNode(o))}}}},p={};function u(e){var t=p[e];if(void 0!==t)return t.exports;var o=p[e]={id:e,exports:{}};return c[e](o,o.exports,u),o.exports}u.m=c,t=Object.getPrototypeOf?function(e){return Object.getPrototypeOf(e)}:function(e){return e.__proto__},u.t=function(o,r){if(1&r&&(o=this(o)),8&r||"object"==typeof o&&o&&(4&r&&o.__esModule||16&r&&"function"==typeof o.then))return o;var n=Object.create(null);u.r(n);var a={};e=e||[null,t({}),t([]),t(t)];for(var s=2&r&&o;"object"==typeof s&&!~e.indexOf(s);s=t(s))Object.getOwnPropertyNames(s).forEach(function(e){a[e]=function(){return o[e]}});return a.default=function(){return o},u.d(n,a),n},u.d=function(e,t){for(var o in t)u.o(t,o)&&!u.o(e,o)&&Object.defineProperty(e,o,{enumerable:!0,get:t[o]})},u.f={},u.e=function(e){return Promise.all(Object.keys(u.f).reduce(function(t,o){return u.f[o](e,t),t},[]))},u.u=function(e){return"static/js/d785a992501d378782bc.js"},u.miniCssF=function(e){return"static/css/"+(74===e?"brainst-odoo-pro":e)+".css"},u.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},o={},r="administration:",u.l=function(e,t,n,a){if(o[e]){o[e].push(t);return}if(void 0!==n)for(var s,i,l=document.getElementsByTagName("script"),d=0;d<l.length;d++){var c=l[d];if(c.getAttribute("src")==e||c.getAttribute("data-webpack")==r+n){s=c;break}}s||(i=!0,(s=document.createElement("script")).charset="utf-8",s.timeout=120,u.nc&&s.setAttribute("nonce",u.nc),s.setAttribute("data-webpack",r+n),s.src=e),o[e]=[t];var p=function(t,r){s.onerror=s.onload=null,clearTimeout(m);var n=o[e];if(delete o[e],s.parentNode&&s.parentNode.removeChild(s),n&&n.forEach(function(e){return e(r)}),t)return t(r)},m=setTimeout(p.bind(null,void 0,{type:"timeout",target:s}),12e4);s.onerror=p.bind(null,s.onerror),s.onload=p.bind(null,s.onload),i&&document.head.appendChild(s)},u.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},u.p="bundles/brainstodoopro/",n=function(e,t,o,r){var n=document.createElement("link");return n.rel="stylesheet",n.type="text/css",n.onerror=n.onload=function(a){if(n.onerror=n.onload=null,"load"===a.type)o();else{var s=a&&("load"===a.type?"missing":a.type),i=a&&a.target&&a.target.href||t,l=Error("Loading CSS chunk "+e+" failed.\n("+i+")");l.code="CSS_CHUNK_LOAD_FAILED",l.type=s,l.request=i,n.parentNode.removeChild(n),r(l)}},n.href=t,document.head.appendChild(n),n},a=function(e,t){for(var o=document.getElementsByTagName("link"),r=0;r<o.length;r++){var n=o[r],a=n.getAttribute("data-href")||n.getAttribute("href");if("stylesheet"===n.rel&&(a===e||a===t))return n}for(var s=document.getElementsByTagName("style"),r=0;r<s.length;r++){var n=s[r],a=n.getAttribute("data-href");if(a===e||a===t)return n}},s={74:0},u.f.miniCss=function(e,t){s[e]?t.push(s[e]):0!==s[e]&&({461:1})[e]&&t.push(s[e]=new Promise(function(t,o){var r=u.miniCssF(e),s=u.p+r;if(a(r,s))return t();n(e,s,t,o)}).then(function(){s[e]=0},function(t){throw delete s[e],t}))},i={74:0},u.f.j=function(e,t){var o=u.o(i,e)?i[e]:void 0;if(0!==o){if(o)t.push(o[2]);else{var r=new Promise(function(t,r){o=i[e]=[t,r]});t.push(o[2]=r);var n=u.p+u.u(e),a=Error();u.l(n,function(t){if(u.o(i,e)&&(0!==(o=i[e])&&(i[e]=void 0),o)){var r=t&&("load"===t.type?"missing":t.type),n=t&&t.target&&t.target.src;a.message="Loading chunk "+e+" failed.\n("+r+": "+n+")",a.name="ChunkLoadError",a.type=r,a.request=n,o[1](a)}},"chunk-"+e,e)}}},l=function(e,t){var o,r,n=t[0],a=t[1],s=t[2],l=0;if(n.some(function(e){return 0!==i[e]})){for(o in a)u.o(a,o)&&(u.m[o]=a[o]);s&&s(u)}for(e&&e(t);l<n.length;l++)r=n[l],u.o(i,r)&&i[r]&&i[r][0](),i[r]=0},(d=window["webpackJsonpPluginbrainst-odoo-pro"]=window["webpackJsonpPluginbrainst-odoo-pro"]||[]).forEach(l.bind(null,0)),d.push=l.bind(null,d.push.bind(d)),window?.__sw__?.assetPath&&(u.p=window.__sw__.assetPath+"/bundles/brainstodoopro/"),function(){"use strict";let e=Shopware.Classes.ApiService;var t=class extends e{constructor(e,t,o="brainst-odoo-pro"){super(e,t,o)}verifyConfig(t){let o=this.getBasicHeaders({});return this.httpClient.post(`${this.getApiBasePath()}/verify`,t,{headers:o}).then(t=>e.handleResponse(t))}recordCount(t,o){let r=this.getBasicHeaders({}),n=t?`/${t}`:"";return o&&(n+=`/${o}`),this.httpClient.get(`${this.getApiBasePath()}/count${n}`,{headers:r}).then(t=>e.handleResponse(t))}getSyncRate(){let t=this.getBasicHeaders({});return this.httpClient.get(`${this.getApiBasePath()}/sync-rate`,{headers:t}).then(t=>e.handleResponse(t))}removeAutomation(){let t=this.getBasicHeaders({});return this.httpClient.post(`${this.getApiBasePath()}/automation/remove`,{},{headers:t}).then(t=>e.handleResponse(t))}createAutomation(){let t=this.getBasicHeaders({});return this.httpClient.post(`${this.getApiBasePath()}/automation/create`,{},{headers:t}).then(t=>e.handleResponse(t))}};let o=Shopware.Classes.ApiService;var r=class extends o{constructor(e,t,o="brainst-odoo-pro"){super(e,t,o)}synchronise(){let e=this.getBasicHeaders({});return this.httpClient.post(`${this.getApiBasePath()}/sync`,{},{headers:e}).then(e=>o.handleResponse(e))}};let{Application:n}=Shopware;n.addServiceProvider("brainstOdooProApiService",e=>new t(n.getContainer("init").httpClient,e.loginService)),n.addServiceProvider("brainstOdooProSynchronizationService",e=>new r(n.getContainer("init").httpClient,e.loginService)),Shopware.Component.override("sw-data-grid",{template:'{% block sw_data_grid_columns_render_router_link %}\n    <template v-if="column.routerLink">\n        <router-link\n                v-if="column.routerLink !== null && typeof column.routerLink === \'object\' && column.routerLink.id"\n                class="sw-data-grid__cell-value"\n                :to="{ name: column.routerLink.name, params: { id: getRouteId(item, column.routerLink.id) } }"\n        >\n            {{ renderColumn(item, column) }}\n        </router-link>\n        <router-link\n                v-else\n                class="sw-data-grid__cell-value"\n                :to="{ name: column.routerLink, params: { id: item.id } }"\n        >\n            {{ renderColumn(item, column) }}\n        </router-link>\n    </template>\n{% endblock %}',methods:{getRouteId(e,t){return t.split(".").reduce((e,t)=>e?.[t]??null,e)}}}),u(377),u.e(461).then(u.t.bind(u,461,23)),Shopware.Component.register("brainst-odoo-pro-list",{template:'{% block brainst_odoo_list %}\n\n    <sw-page class="brainst-odoo-list sw-settings-index">\n        {% block brainst_odoo_list_smart_bar_header %}\n            <template #smart-bar-header>\n                {% block brainst_odoo_list_smart_bar_header_title %}\n                    <h2>\n                        {% block brainst_odoo_list_smart_bar_header_title_text %}\n                            {{ $tc(\'brainst-odoo-pro.list.title\') }}\n                        {% endblock %}\n                    </h2>\n                {% endblock %}\n            </template>\n            <template #smart-bar-actions>\n                {% block brainst_odoo_list_smart_bar_action %}\n                    <sw-button\n                        variant="ghost"\n                        size="small"\n                        @click="getAllData"\n                        :disabled="isLoading"\n                        class="dashboard-refresh-btn"\n                    >\n                        <sw-icon name="regular-redo" small></sw-icon>\n                        {{ $tc(\'brainst-odoo-pro.dashboard.refresh\') }}\n                    </sw-button>\n                    <brainst-odoo-pro-synchronize-button/>\n                {% endblock %}\n            </template>\n        {% endblock %}\n\n        {% block brainst_odoo_list_content %}\n            <template #content>\n                {% block brainst_odoo_list_content_card_view %}\n                    <sw-card-view>\n                        \n                        <sw-card class="brainst-odoo-dashboard__card" positionIdentifier="brainst-odoo-dashboard-card">\n                            <div class="brainst-odoo-dashboard">\n                                <div class="dashboard-header">\n                                    <div class="dashboard-title-section">\n                                        <h3>{{ $tc(\'brainst-odoo-pro.dashboard.title\') }}</h3>\n                                    </div>\n                                    <div class="dashboard-stats">\n                                        <div class="stat-item">\n                                            <span class="stat-value">{{ syncedCount }}</span>\n                                            <span class="stat-label">{{ $tc(\'brainst-odoo-pro.dashboard.totalSynced\') }}</span>\n                                        </div>\n                                        <div class="stat-item">\n                                            <span class="stat-value">{{ Object.keys(entityCounts).length }}</span>\n                                            <span class="stat-label">{{ $tc(\'brainst-odoo-pro.dashboard.entityTypes\') }}</span>\n                                        </div>\n                                    </div>\n                                </div>\n\n                                \n                                <div class="dashboard-progress">\n                                    <brainst-odoo-pro-progress-bar\n                                        :value="syncedCount"\n                                        :totalRecords="totalRecords"\n                                        :animated="true"\n                                    />\n                                </div>\n\n                                \n                                <div class="gauge-chart-container">\n                                    <div class="gauge-header">\n                                        <div class="gauge-header-left">\n                                            <h4>{{ $tc(\'brainst-odoo-pro.dashboard.gauge.title\') }}</h4>\n                                            <div class="gauge-controls">\n                                                <sw-button\n                                                    :variant="selectedTimePeriod === \'hour\' ? \'primary\' : \'ghost\'"\n                                                    size="small"\n                                                    @click="switchTimePeriod(\'hour\')"\n                                                    :title="$tc(\'brainst-odoo-pro.dashboard.gauge.switchToHour\')"\n                                                    class="gauge-control-btn"\n                                                >\n                                                    {{ $tc(\'brainst-odoo-pro.dashboard.gauge.lastHour\') }}\n                                                </sw-button>\n                                                <sw-button\n                                                    :variant="selectedTimePeriod === \'minute\' ? \'primary\' : \'ghost\'"\n                                                    size="small"\n                                                    @click="switchTimePeriod(\'minute\')"\n                                                    :title="$tc(\'brainst-odoo-pro.dashboard.gauge.switchToMinute\')"\n                                                    class="gauge-control-btn"\n                                                >\n                                                    {{ $tc(\'brainst-odoo-pro.dashboard.gauge.lastMinute\') }}\n                                                </sw-button>\n                                            </div>\n                                        </div>\n                                        <div class="gauge-header-right">\n                                            <sw-button\n                                                variant="ghost"\n                                                size="small"\n                                                @click="refreshSyncRate"\n                                                :disabled="isLoading"\n                                                class="gauge-refresh-btn"\n                                                :title="$tc(\'brainst-odoo-pro.dashboard.refresh\')"\n                                            >\n                                                <sw-icon name="regular-redo" small class="refresh-icon"></sw-icon>\n                                            </sw-button>\n                                        </div>\n                                    </div>\n\n                                    \n                                    <brainst-odoo-pro-gauge-chart\n                                        :value="currentSyncRate.value"\n                                        :max-value="currentSyncRate.max"\n                                        :title="currentSyncRate.label"\n                                        :unit="currentSyncRate.unit"\n                                        :display-value="currentSyncRate.displayValue"\n                                        :is-loading="isLoading"\n                                    />\n                                </div>\n\n                                \n                                <div class="dashboard-tables-section">\n                                    <h4>{{ $tc(\'brainst-odoo-pro.list.title\') }}</h4>\n                                    <div class="sw-settings__content-grid">\n                                        <template v-for="odooTable in odooTables" :key="odooTable.path">\n                                            <div class="brainst-odoo-table-item"\n                                                 :class="\'status-\' + getEntitySyncStatus(odooTable.entityKey)"\n                                                 :title="getEntityTooltip(odooTable.entityKey)"\n                                                 @click="$router.push({ name: odooTable.path })"\n                                                 v-tooltip="getEntityTooltip(odooTable.entityKey)">\n                                                <div class="table-item-header">\n                                                    <div class="table-item-icon"\n                                                         :style="{ backgroundColor: odooTable.color }">\n                                                        <sw-icon :name="getEntityIcon(odooTable.icon)" size="20px"></sw-icon>\n                                                    </div>\n                                                    <div class="table-item-status">\n                                                        <sw-label\n                                                            :variant="getStatusBadgeVariant(getEntitySyncStatus(odooTable.entityKey))"\n                                                            size="small"\n                                                            class="status-badge">\n                                                            {{ getEntityStats(odooTable.entityKey).percentage }}%\n                                                        </sw-label>\n                                                    </div>\n                                                </div>\n                                                <div class="table-item-content">\n                                                    <h4 class="table-item-title">{{ odooTable.title }}</h4>\n                                                    <div class="table-item-stats" v-if="!isLoading">\n                                                        <div class="stat-row">\n                                                            <span class="stat-label">{{ $tc(\'brainst-odoo-pro.dashboard.synced\') }}:</span>\n                                                            <span class="stat-value synced">{{ getEntityStats(odooTable.entityKey).synced }}</span>\n                                                        </div>\n                                                        <div class="stat-row">\n                                                            <span class="stat-label">{{ $tc(\'brainst-odoo-pro.dashboard.total\') }}:</span>\n                                                            <span class="stat-value total">{{ getEntityStats(odooTable.entityKey).total }}</span>\n                                                        </div>\n                                                    </div>\n                                                    <div class="table-item-progress" v-if="!isLoading">\n                                                        <div class="progress-bar">\n                                                            <div class="progress-fill"\n                                                                 :style="{\n                                                                     width: getEntityStats(odooTable.entityKey).percentage + \'%\',\n                                                                     backgroundColor: odooTable.color\n                                                                 }"></div>\n                                                        </div>\n                                                    </div>\n                                                    <div class="table-item-loading" v-if="isLoading">\n                                                        <sw-loader size="16px"></sw-loader>\n                                                    </div>\n                                                </div>\n                                                <div class="table-item-arrow">\n                                                    <sw-icon name="regular-chevron-right" size="12px"></sw-icon>\n                                                </div>\n                                            </div>\n                                        </template>\n                                    </div>\n                                </div>\n\n                                \n                                <div class="chart-container chart-container-full-width">\n                                    <h4>{{ $tc(\'brainst-odoo-pro.dashboard.entitySynced\') }}</h4>\n                                    <div class="bar-chart" v-if="!isLoading">\n                                        <div\n                                            v-for="item in chartData"\n                                            :key="item.name"\n                                            class="bar-item"\n                                        >\n                                            <div class="bar-label">{{ item.name }}</div>\n                                            <div class="bar-wrapper">\n                                                <div\n                                                    class="bar-fill"\n                                                    :style="{\n                                                        width: (item.synced / item.value) * 100 + \'%\',\n                                                        backgroundColor: item.color\n                                                    }"\n                                                ></div>\n                                                <span class="bar-value">{{ item.synced }}/{{ item.value }}</span>\n                                            </div>\n                                        </div>\n                                    </div>\n                                </div>\n\n                                \n                                <div class="charts-grid">\n\n                                    \n                                    <div class="chart-container donut-chart-container">\n                                        <h4>{{ $tc(\'brainst-odoo-pro.dashboard.syncStatus\') }}</h4>\n                                        <div class="donut-chart" v-if="!isLoading">\n                                            <div class="donut-svg-wrapper">\n                                                <svg viewBox="0 0 100 100" class="donut-svg">\n                                                    <circle\n                                                        v-for="(item, index) in syncStatusData"\n                                                        :key="item.name"\n                                                        cx="50"\n                                                        cy="50"\n                                                        r="35"\n                                                        :stroke="item.color"\n                                                        stroke-width="12"\n                                                        fill="transparent"\n                                                        :stroke-dasharray="(item.value / (syncStatusData[0].value + syncStatusData[1].value)) * 220 + \' 220\'"\n                                                        :stroke-dashoffset="index === 0 ? 0 : -(syncStatusData[0].value / (syncStatusData[0].value + syncStatusData[1].value)) * 220"\n                                                        class="donut-segment"\n                                                    />\n                                                </svg>\n                                                <div class="donut-center">\n                                                    <span class="donut-percentage">\n                                                        {{ Math.round((syncStatusData[0].value / (syncStatusData[0].value + syncStatusData[1].value)) * 100) }}%\n                                                    </span>\n                                                </div>\n                                            </div>\n                                            <div class="donut-legend">\n                                                <div\n                                                    v-for="item in syncStatusData"\n                                                    :key="item.name"\n                                                    class="legend-item"\n                                                >\n                                                    <span class="legend-color" :style="{ backgroundColor: item.color }"></span>\n                                                    <span class="legend-text">{{ item.name }}: {{ item.value }}</span>\n                                                </div>\n                                            </div>\n                                        </div>\n                                    </div>\n\n                                    \n                                    <div class="chart-container progress-chart-container">\n                                        <h4>{{ $tc(\'brainst-odoo-pro.dashboard.overallProgress\') }}</h4>\n                                        <div class="progress-pie" v-if="!isLoading">\n                                            <div class="pie-svg-wrapper">\n                                                <svg viewBox="0 0 100 100" class="pie-svg">\n                                                    \n                                                    <circle\n                                                        cx="50"\n                                                        cy="50"\n                                                        r="40"\n                                                        fill="#f3f4f6"\n                                                        class="pie-background"\n                                                    />\n                                                    \n                                                    <path\n                                                        :d="getSyncedPath()"\n                                                        fill="#10b981"\n                                                        class="pie-segment pie-segment-synced"\n                                                        @mouseenter="showHover(\'synced\')"\n                                                        @mouseleave="hideHover"\n                                                    />\n                                                    \n                                                    <path\n                                                        :d="getPendingPath()"\n                                                        fill="#f59e0b"\n                                                        class="pie-segment pie-segment-pending"\n                                                        @mouseenter="showHover(\'pending\')"\n                                                        @mouseleave="hideHover"\n                                                    />\n                                                </svg>\n                                                <div class="pie-center">\n                                                    <span class="pie-percentage" v-show="!hoverInfo.show">\n                                                        {{ totalRecords > 0 ? Math.round((syncedCount / totalRecords) * 100) : 0 }}%\n                                                    </span>\n                                                    <div class="pie-hover-info" v-show="hoverInfo.show">\n                                                        <span class="pie-hover-label">{{ hoverInfo.label }}</span>\n                                                        <span class="pie-hover-percentage">{{ hoverInfo.percentage }}%</span>\n                                                    </div>\n                                                </div>\n                                            </div>\n                                            <div class="progress-legend">\n                                                <div class="progress-stats">\n                                                    <div class="progress-stat-item">\n                                                        <span class="progress-stat-color" style="background-color: #10b981;"></span>\n                                                        <span class="progress-stat-text">\n                                                            {{ $tc(\'brainst-odoo-pro.dashboard.synced\') }}: {{ totalRecords > 0 ? Math.round((syncedCount / totalRecords) * 100) : 0 }}%\n                                                        </span>\n                                                    </div>\n                                                    <div class="progress-stat-item">\n                                                        <span class="progress-stat-color" style="background-color: #f59e0b;"></span>\n                                                        <span class="progress-stat-text">\n                                                            {{ $tc(\'brainst-odoo-pro.dashboard.pending\') }}: {{ totalRecords > 0 ? Math.round(((totalRecords - syncedCount) / totalRecords) * 100) : 0 }}%\n                                                        </span>\n                                                    </div>\n                                                    <div class="progress-stat-item">\n                                                        <span class="progress-stat-color" style="background-color: #6b7280;"></span>\n                                                        <span class="progress-stat-text">\n                                                            {{ $tc(\'brainst-odoo-pro.dashboard.total\') }}: {{ totalRecords }} {{ $tc(\'brainst-odoo-pro.dashboard.records\') }}\n                                                        </span>\n                                                    </div>\n                                                </div>\n                                            </div>\n                                        </div>\n                                    </div>\n                                </div>\n                            </div>\n                        </sw-card>\n                    </sw-card-view>\n                {% endblock %}\n            </template>\n        {% endblock %}\n    </sw-page>\n{% endblock %}\n\n',inject:["brainstOdooProApiService"],data(){return{syncedCount:0,totalRecords:0,entityCounts:{},entitySynced:{},syncProgress:{},isLoading:!0,hoverInfo:{show:!1,label:"",percentage:0},syncRate:{recordsPerHour:0,recordsPerMinute:0,displayRecordsPerHour:"0",displayRecordsPerMinute:"0",timestamp:null},selectedTimePeriod:"hour",odooTables:[{title:this.$tc("brainst-odoo-pro.list.sales-channel"),path:"brainst.odoo.pro.sales-channel",icon:"regular-storefront",entityKey:"sales_channel",color:"#3b82f6"},{title:this.$tc("brainst-odoo-pro.list.category"),path:"brainst.odoo.pro.category",icon:"regular-folder",entityKey:"category",color:"#8b5cf6"},{title:this.$tc("brainst-odoo-pro.list.customer"),path:"brainst.odoo.pro.customer",icon:"regular-user",entityKey:"customer",color:"#ef4444"},{title:this.$tc("brainst-odoo-pro.list.customer-address"),path:"brainst.odoo.pro.customer-address",icon:"regular-map",entityKey:"customer_address",color:"#ec4899"},{title:this.$tc("brainst-odoo-pro.list.property"),path:"brainst.odoo.pro.property",icon:"regular-cog",entityKey:"attribute",color:"#f59e0b"},{title:this.$tc("brainst-odoo-pro.list.attribute-value"),path:"brainst.odoo.pro.attribute-value",icon:"regular-tag",entityKey:"attribute_value",color:"#f97316"},{title:this.$tc("brainst-odoo-pro.list.product"),path:"brainst.odoo.pro.product",icon:"regular-products",entityKey:"product",color:"#10b981"},{title:this.$tc("brainst-odoo-pro.list.order"),path:"brainst.odoo.pro.order",icon:"regular-shopping-cart",entityKey:"order",color:"#84cc16"},{title:this.$tc("brainst-odoo-pro.list.delivery"),path:"brainst.odoo.pro.delivery",icon:"regular-truck",entityKey:"delivery",color:"#14b8a6"},{title:this.$tc("brainst-odoo-pro.list.transaction"),path:"brainst.odoo.pro.transaction",icon:"regular-credit-card",entityKey:"transaction",color:"#a855f7"}],timeoutId:null,syncRateTimeoutId:null}},metaInfo(){return{title:this.$createTitle()}},created(){this.getAllData(),this.startSyncRateAutoRefresh()},computed:{chartData(){return this.entityCounts&&0!==Object.keys(this.entityCounts).length?Object.entries(this.entityCounts).map(([e,t])=>({name:this.getEntityDisplayName(e),value:t||0,synced:this.entitySynced[e]||0,color:this.getEntityColor(e)})):[]},syncStatusData(){if(this.syncProgress&&void 0!==this.syncProgress.synced)return[{name:this.$tc("brainst-odoo-pro.dashboard.synced"),value:this.syncProgress.synced,color:"#10b981"},{name:this.$tc("brainst-odoo-pro.dashboard.pending"),value:this.syncProgress.pending,color:"#f59e0b"}];let e=Object.values(this.entityCounts).reduce((e,t)=>e+t,0),t=this.syncedCount;return[{name:this.$tc("brainst-odoo-pro.dashboard.synced"),value:t,color:"#10b981"},{name:this.$tc("brainst-odoo-pro.dashboard.pending"),value:Math.max(0,e-t),color:"#f59e0b"}]},currentSyncRate(){return"hour"===this.selectedTimePeriod?{value:this.syncRate.recordsPerHour,displayValue:this.syncRate.displayRecordsPerHour,max:999,label:this.$tc("brainst-odoo-pro.dashboard.gauge.recordsPerHour"),unit:this.$tc("brainst-odoo-pro.dashboard.gauge.hour")}:{value:this.syncRate.recordsPerMinute,displayValue:this.syncRate.displayRecordsPerMinute,max:99,label:this.$tc("brainst-odoo-pro.dashboard.gauge.recordsPerMinute"),unit:this.$tc("brainst-odoo-pro.dashboard.gauge.minute")}}},methods:{getAllData(){this.isLoading=!0,Promise.all([this.brainstOdooProApiService.recordCount(),this.brainstOdooProApiService.getSyncRate()]).then(([e,t])=>{e.isValid&&(this.syncedCount=e.syncedRecords||0,this.totalRecords=e.totalRecords||0,this.entityCounts=e.detailedCounts,this.entitySynced=e.syncedCounts,this.syncProgress={synced:e.syncedRecords||0,pending:Math.max(0,(e.totalRecords||0)-(e.syncedRecords||0)),total:e.totalRecords||0,percentage:(e.totalRecords||0)>0?Math.round((e.syncedRecords||0)/(e.totalRecords||0)*100):0}),t.isValid&&(this.syncRate={recordsPerHour:t.recordsPerHour||0,recordsPerMinute:t.recordsPerMinute||0,displayRecordsPerHour:t.displayRecordsPerHour||"0",displayRecordsPerMinute:t.displayRecordsPerMinute||"0",timestamp:t.timestamp})}).catch(e=>{console.error("Failed to fetch data:",e),this.syncedCount=0,this.entityCounts={},this.entitySynced={},this.syncProgress={synced:0,pending:0,total:0,percentage:0},this.syncRate={recordsPerHour:0,recordsPerMinute:0,displayRecordsPerHour:"0",displayRecordsPerMinute:"0",timestamp:null}}).finally(()=>{this.isLoading=!1})},getEntityColor(e){return({sales_channel:"#3b82f6",category:"#8b5cf6",product:"#10b981",template:"#06b6d4",attribute:"#f59e0b",attribute_value:"#f97316",customer:"#ef4444",customer_address:"#ec4899",order:"#84cc16",delivery:"#14b8a6",transaction:"#a855f7"})[e]||"#6b7280"},getEntityDisplayName(e){return({sales_channel:this.$tc("brainst-odoo-pro.dashboard.entityNames.salesChannels"),category:this.$tc("brainst-odoo-pro.dashboard.entityNames.categories"),product:this.$tc("brainst-odoo-pro.dashboard.entityNames.productVariants"),template:this.$tc("brainst-odoo-pro.dashboard.entityNames.products"),attribute:this.$tc("brainst-odoo-pro.dashboard.entityNames.propertyGroups"),attribute_value:this.$tc("brainst-odoo-pro.dashboard.entityNames.propertyGroupOptions"),customer:this.$tc("brainst-odoo-pro.dashboard.entityNames.customers"),customer_address:this.$tc("brainst-odoo-pro.dashboard.entityNames.customerAddresses"),order:this.$tc("brainst-odoo-pro.dashboard.entityNames.orders"),delivery:this.$tc("brainst-odoo-pro.dashboard.entityNames.deliveries"),transaction:this.$tc("brainst-odoo-pro.dashboard.entityNames.transactions")})[e]||e.replace("_"," ").replace(/\b\w/g,e=>e.toUpperCase())},getSyncedPath(){if(0===this.totalRecords)return"";let e=this.syncedCount/this.totalRecords*100;return this.createPieSlice(0,e/100*360,40)},getPendingPath(){if(0===this.totalRecords)return"";let e=this.syncedCount/this.totalRecords*100,t=e/100*360;return this.createPieSlice(t,t+(100-e)/100*360,40)},createPieSlice(e,t,o){let r=(e-90)*Math.PI/180,n=(t-90)*Math.PI/180;return t-e>=360?`M 50 50 m -${o} 0 a ${o} ${o} 0 1 1 ${2*o} 0 a ${o} ${o} 0 1 1 -${2*o} 0`:`M 50 50 L ${50+o*Math.cos(r)} ${50+o*Math.sin(r)} A ${o} ${o} 0 ${t-e>180?1:0} 1 ${50+o*Math.cos(n)} ${50+o*Math.sin(n)} Z`},showHover(e){if(0===this.totalRecords)return;let t=Math.round(this.syncedCount/this.totalRecords*100);this.hoverInfo={show:!0,label:"synced"===e?this.$tc("brainst-odoo-pro.dashboard.synced"):this.$tc("brainst-odoo-pro.dashboard.pending"),percentage:"synced"===e?t:100-t}},hideHover(){this.hoverInfo.show=!1},getEntityStats(e){let t=this.entityCounts[e]||0,o=this.entitySynced[e]||0;return{total:t,synced:o,pending:Math.max(0,t-o),percentage:t>0?Math.round(o/t*100):0}},getEntitySyncStatus(e){let t=this.getEntityStats(e);return 0===t.total?"no-data":t.synced===t.total?"complete":t.synced>0?"partial":"pending"},getStatusBadgeVariant(e){return({complete:"success",partial:"warning",pending:"danger","no-data":"neutral"})[e]||"neutral"},getEntityTooltip(e){let t=this.getEntityStats(e),o=this.getEntitySyncStatus(e),r={complete:this.$tc("brainst-odoo-pro.dashboard.tooltips.complete"),partial:this.$tc("brainst-odoo-pro.dashboard.tooltips.partial"),pending:this.$tc("brainst-odoo-pro.dashboard.tooltips.pending"),"no-data":this.$tc("brainst-odoo-pro.dashboard.tooltips.noData")};return`${r[o]||""}
${this.$tc("brainst-odoo-pro.dashboard.synced")}: ${t.synced}/${t.total} (${t.percentage}%)`},getEntityIcon(e){return({"regular-products":"regular-products","regular-map-marker":"regular-map-marker","regular-shopping-bag":"regular-server","regular-home":"regular-home","regular-circle":"regular-circle","regular-folder":"regular-folder","regular-cog":"regular-cog","regular-tag":"regular-tag","regular-user":"regular-user","regular-shopping-cart":"regular-shopping-cart","regular-truck":"regular-truck","regular-credit-card":"regular-credit-card","regular-map":"regular-map","regular-storefront":"regular-storefront"})[e]||"regular-server"},switchTimePeriod(e){this.selectedTimePeriod=e},refreshSyncRate(){this.startSyncRateAutoRefresh(),this._performSyncRateRefresh(!0)},_performSyncRateRefresh(e=!1){if(e){let e=document.querySelector(".gauge-refresh-btn");e&&(e.classList.remove("rotating"),e.offsetHeight,e.classList.add("rotating"),setTimeout(()=>{e.classList.remove("rotating")},300))}this.brainstOdooProApiService.getSyncRate().then(e=>{e.isValid&&(this.syncRate={recordsPerHour:e.recordsPerHour||0,recordsPerMinute:e.recordsPerMinute||0,displayRecordsPerHour:e.displayRecordsPerHour||"0",displayRecordsPerMinute:e.displayRecordsPerMinute||"0",timestamp:e.timestamp})}).catch(e=>{console.error("Failed to fetch sync rate:",e)})},startSyncRateAutoRefresh(){this.stopSyncRateAutoRefresh(),this.syncRateTimeoutId=setInterval(()=>{this._performSyncRateRefresh(!1)},5e3)},stopSyncRateAutoRefresh(){this.syncRateTimeoutId&&(clearInterval(this.syncRateTimeoutId),this.syncRateTimeoutId=null)}},beforeUnmount(){this.timeoutId&&clearTimeout(this.timeoutId),this.stopSyncRateAutoRefresh()}});let a=Shopware.Data.Criteria;Shopware.Component.register("brainst-odoo-pro-sales-channel",{template:'<sw-page>\n    <template #smart-bar-back>\n        {% block sw_page_slot_smart_bar_back %}\n            <brainst-odoo-pro-back-button />\n        {% endblock %}\n    </template>\n\n    <template #smart-bar-header>\n        {% block brainst_odoo_table_sales_channel_smart_bar_header_title %}\n            <h2>{{ $tc(\'brainst-odoo-pro.sales-channel.title\') }}</h2>\n        {% endblock %}\n    </template>\n\n    <template #content>\n        {% block brainst_odoo_table_sales_channel_list_content %}\n            <brainst-odoo-pro-progress-bar entity="sales_channel" :value="total"/>\n            <sw-entity-listing\n                    v-if="brainstOdooEntries"\n                    :items="brainstOdooEntries"\n                    :repository="brainstOdooRepository"\n                    :showSelection="false"\n                    :columns="columns"\n                    :allowDelete="false">\n                <template #column-salesChannel.name="{ item, column }">\n                    <router-link\n                            class="sw-data-grid__cell-value"\n                            :to="{ name: \'sw.sales.channel.detail\', params: { id: item.recordId } }"\n                    >\n                        {{ item.salesChannel?.name || item.recordId }}\n                    </router-link>\n                </template>\n                <template #column-odooId="{ item, column }">\n                    <a :href="`${odooUrl}/web#id=${item.odooId}&view_type=form&model=res.company`"\n                       class="sw-data-grid__cell-value" rel="nofollow noopener"\n                       target="_blank">{{ item.odooId }}</a>\n                </template>\n            </sw-entity-listing>\n        {% endblock %}\n    </template>\n</sw-page>\n',inject:["repositoryFactory"],data(){return{odooUrl:null,page:1,limit:25,brainstOdooEntries:null,total:0,isLoading:!0,currentLanguageId:Shopware.Context.api.languageId}},metaInfo(){return{title:this.$createTitle()}},created(){this.fetchPluginUrl(),this.getList()},computed:{brainstOdooRepository(){return this.repositoryFactory.create("brainst_odoo")},systemConfigRepository(){return this.repositoryFactory.create("system_config")},salesChannelRepository(){return this.repositoryFactory.create("sales_channel")},columns(){return[{allowResize:!0,dataIndex:"salesChannel.name",label:this.$tc("brainst-odoo-pro.sales-channel.table.name"),primary:!0,property:"salesChannel.name"},{allowResize:!0,dataIndex:"odooId",label:this.$tc("brainst-odoo-pro.sales-channel.table.odoo-id"),property:"odooId",naturalSorting:!0}]}},methods:{getList(){this.isLoading=!0;let e=new a(this.page,this.limit);return e.addFilter(a.equals("module","sales_channel")),e.addAssociation("salesChannel"),e.addFields("recordId","odooId","salesChannel.name"),e.addSorting(a.sort("salesChannel.name","ASC",!1)),this.brainstOdooRepository.search(e,Shopware.Context.api).then(e=>{this.total=e.total,this.brainstOdooEntries=e,this.isLoading=!1}).catch(e=>{console.error("Failed to fetch Data:",e)})},fetchPluginUrl(){let e=new a;e.addFilter(a.equals("configurationKey","BrainstOdooPro.config.serverUrl")),this.systemConfigRepository.search(e,Shopware.Context.api).then(e=>{this.odooUrl=e.first().configurationValue}).catch(e=>{console.error("Failed to fetch the URL:",e)})}}});let s=Shopware.Data.Criteria;Shopware.Component.register("brainst-odoo-pro-category",{template:'<sw-page>\n    <template #smart-bar-back>\n        {% block sw_page_slot_smart_bar_back %}\n            <brainst-odoo-pro-back-button />\n        {% endblock %}\n    </template>\n\n    <template #smart-bar-header>\n        {% block brainst_odoo_table_category_smart_bar_header_title %}\n            <h2>{{ $tc(\'brainst-odoo-pro.category.title\') }}</h2>\n        {% endblock %}\n    </template>\n\n    <template #content>\n        {% block brainst_odoo_table_category_list_content %}\n            <brainst-odoo-pro-progress-bar entity="category" :value="total"/>\n            <sw-entity-listing\n                    v-if="brainstOdooEntries"\n                    :items="brainstOdooEntries"\n                    :repository="brainstOdooRepository"\n                    :showSelection="false"\n                    :columns="columns"\n                    :allowDelete="false">\n                <template #column-category.name="{ item, column }">\n                    <router-link\n                            class="sw-data-grid__cell-value"\n                            :to="{ name: \'sw.category.detail\', params: { id: item.recordId } }"\n                    >\n                        {{ item.category?.name || item.recordId }}\n                    </router-link>\n                </template>\n                <template #column-odooId="{ item, column }">\n                    <a :href="`${odooUrl}/web#id=${item.odooId}&view_type=form&model=product.category`"\n                       class="sw-data-grid__cell-value" rel="nofollow noopener"\n                       target="_blank">{{ item.odooId }}</a>\n                </template>\n            </sw-entity-listing>\n        {% endblock %}\n    </template>\n</sw-page>\n',inject:["repositoryFactory"],data(){return{odooUrl:null,page:1,limit:25,brainstOdooEntries:null,total:0,isLoading:!0,currentLanguageId:Shopware.Context.api.languageId}},metaInfo(){return{title:this.$createTitle()}},created(){this.fetchPluginUrl(),this.getList()},computed:{brainstOdooRepository(){return this.repositoryFactory.create("brainst_odoo")},systemConfigRepository(){return this.repositoryFactory.create("system_config")},categoryRepository(){return this.repositoryFactory.create("category")},columns(){return[{allowResize:!0,dataIndex:"category.name",label:this.$tc("brainst-odoo-pro.category.table.name"),primary:!0,property:"category.name"},{allowResize:!0,dataIndex:"odooId",label:this.$tc("brainst-odoo-pro.category.table.odoo-id"),property:"odooId",naturalSorting:!0}]}},methods:{getList(){this.isLoading=!0;let e=new s(this.page,this.limit);return e.addFilter(s.equals("module","category")),e.addAssociation("category"),e.addFields("recordId","odooId","category.name","name"),e.addSorting(s.sort("category.name","ASC",!1)),this.brainstOdooRepository.search(e,Shopware.Context.api).then(e=>{this.total=e.total,this.brainstOdooEntries=e,this.isLoading=!1}).catch(e=>{console.error("Failed to fetch Data:",e)})},fetchPluginUrl(){let e=new s;e.addFilter(s.equals("configurationKey","BrainstOdooPro.config.serverUrl")),this.systemConfigRepository.search(e,Shopware.Context.api).then(e=>{this.odooUrl=e.first().configurationValue}).catch(e=>{console.error("Failed to fetch the URL:",e)})}}}),u(171);let i=Shopware.Data.Criteria;Shopware.Component.register("brainst-odoo-pro-product",{template:'<sw-page>\n    <template #smart-bar-back>\n        {% block sw_page_slot_smart_bar_back %}\n            <brainst-odoo-pro-back-button />\n        {% endblock %}\n    </template>\n\n    <template #smart-bar-header>\n        {% block brainst_odoo_table_product_smart_bar_header_title %}\n            <h2>{{ $tc(\'brainst-odoo-pro.product.title\') }}</h2>\n        {% endblock %}\n    </template>\n\n    <template #content>\n        {% block brainst_odoo_table_product_list_content %}\n            \n            <div class="sw-tabs">\n                <div class="sw-tabs__header">\n                    <button\n                        class="sw-tabs__item"\n                        :class="{ \'sw-tabs__item--active\': activeTab === \'template\' }"\n                        @click="setActiveTab(\'template\')"\n                    >\n                        <sw-icon name="regular-products" size="16"></sw-icon>\n                        {{ $tc(\'brainst-odoo-pro.product.tabs.template\') }}\n                        <span class="sw-tabs__item-badge" v-if="templateTotal > 0">{{ templateTotal }}</span>\n                    </button>\n                    <button\n                        class="sw-tabs__item"\n                        :class="{ \'sw-tabs__item--active\': activeTab === \'product\' }"\n                        @click="setActiveTab(\'product\')"\n                    >\n                        <sw-icon name="regular-file-text" size="16"></sw-icon>\n                        {{ $tc(\'brainst-odoo-pro.product.tabs.product\') }}\n                        <span class="sw-tabs__item-badge" v-if="total > 0">{{ total }}</span>\n                    </button>\n                </div>\n            </div>\n\n            \n            <div v-if="activeTab === \'product\'" class="tab-content">\n                <sw-alert variant="info" class="sw-alert--no-icon" style="margin-bottom: 20px;">\n                    <div class="sw-alert__message">\n                        <strong>{{ $tc(\'brainst-odoo-pro.product.tabs.product\') }}:</strong> {{ $tc(\'brainst-odoo-pro.product.notes.product\') }}\n                    </div>\n                </sw-alert>\n                <brainst-odoo-pro-progress-bar entity="product" module="product" :value="total"/>\n                <sw-entity-listing\n                        v-if="brainstOdooEntries"\n                        :items="brainstOdooEntries"\n                        :repository="brainstOdooRepository"\n                        :showSelection="false"\n                        :columns="columns"\n                        :allowDelete="false"\n                        @update-records="recordUpdate($event)">\n                    <template #column-product.name="{ item, column }">\n                        <router-link\n                                class="sw-data-grid__cell-value"\n                                :to="{ name: \'sw.product.detail\', params: { id: item.recordId } }"\n                        >\n                            <div class="product-name-with-badge">\n                                <span v-if="item.product?.name || !item.product?.parentId">\n                                    {{ item.product?.name }}\n                                </span>\n                                <span v-else>\n                                    {{ item.parentName }}\n                                    (<template v-for="(value, key) in item.product?.options">\n                                        {{ value.group.name }}: {{ value.name }}{{ item?.product?.options.length - 1 > key ? " | " : "" }}\n                                    </template>)\n                                </span>\n                                <span\n                                    class="product-type-badge"\n                                    :class="{ \'badge-parent\': !item.product?.parentId, \'badge-variant\': item.product?.parentId }"\n                                    :title="!item.product?.parentId ? $tc(\'brainst-odoo-pro.product.badges.parent\') : $tc(\'brainst-odoo-pro.product.badges.variant\')"\n                                >\n                                    {{ !item.product?.parentId ? \'P\' : \'V\' }}\n                                </span>\n                            </div>\n                        </router-link>\n                    </template>\n                    <template #column-odooId="{ item, column }">\n                        <a :href="`${odooUrl}/web#id=${item.odooId}&view_type=form&model=product.product`"\n                           class="sw-data-grid__cell-value" rel="nofollow noopener"\n                           target="_blank">{{ item.odooId }}</a>\n                    </template>\n                </sw-entity-listing>\n            </div>\n\n            \n            <div v-if="activeTab === \'template\'" class="tab-content">\n                <sw-alert variant="info" class="sw-alert--no-icon" style="margin-bottom: 20px;">\n                    <div class="sw-alert__message">\n                        <strong>{{ $tc(\'brainst-odoo-pro.product.tabs.template\') }}:</strong> {{ $tc(\'brainst-odoo-pro.product.notes.template\') }}\n                    </div>\n                </sw-alert>\n                <brainst-odoo-pro-progress-bar entity="product" module="template" :value="templateTotal"/>\n                <sw-entity-listing\n                        v-if="templateEntries"\n                        :items="templateEntries"\n                        :repository="brainstOdooRepository"\n                        :showSelection="false"\n                        :columns="templateColumns"\n                        :allowDelete="false">\n                    <template #column-product.name="{ item, column }">\n                        <router-link\n                                class="sw-data-grid__cell-value"\n                                :to="{ name: \'sw.product.detail\', params: { id: item.recordId } }"\n                        >\n                            {{ item.product?.name || item.recordId }}\n                        </router-link>\n                    </template>\n                    <template #column-odooId="{ item, column }">\n                        <a :href="`${odooUrl}/web#id=${item.odooId}&view_type=form&model=product.template`"\n                           class="sw-data-grid__cell-value" rel="nofollow noopener"\n                           target="_blank">{{ item.odooId }}</a>\n                    </template>\n                </sw-entity-listing>\n            </div>\n        {% endblock %}\n    </template>\n</sw-page>\n',inject:["repositoryFactory"],data(){return{odooUrl:null,page:1,limit:25,brainstOdooEntries:null,templateEntries:null,total:0,templateTotal:0,isLoading:!0,isTemplateLoading:!0,currentLanguageId:Shopware.Context.api.languageId,activeTab:"template"}},metaInfo(){return{title:this.$createTitle()}},created(){this.initializeActiveTab(),this.fetchPluginUrl(),this.getList(),this.getTemplateList()},computed:{brainstOdooRepository(){return this.repositoryFactory.create("brainst_odoo")},systemConfigRepository(){return this.repositoryFactory.create("system_config")},productRepository(){return this.repositoryFactory.create("product")},columns(){return[{allowResize:!0,dataIndex:"product.name",label:this.$tc("brainst-odoo-pro.product.table.name"),primary:!0,property:"product.name"},{allowResize:!0,dataIndex:"odooId",label:this.$tc("brainst-odoo-pro.product.table.odoo-id"),property:"odooId",naturalSorting:!0}]},templateColumns(){return[{allowResize:!0,dataIndex:"product.name",label:this.$tc("brainst-odoo-pro.template.table.name"),primary:!0,property:"product.name"},{allowResize:!0,dataIndex:"odooId",label:this.$tc("brainst-odoo-pro.template.table.odoo-id"),property:"odooId",naturalSorting:!0}]}},methods:{recordUpdate(e){let t=[...new Set(e.map(e=>e.product?.parentId).filter(Boolean))];if(t.length){let o=new i(this.page,this.limit);o.addFilter(i.equalsAny("id",t)),o.addFields("id","name"),this.productRepository.search(o,this.context).then(t=>{let o=Object.fromEntries(t.map(e=>[e.id,e.name]));e.forEach(e=>{e.product?.parentId&&(e.parentName=o[e.product.parentId]||null)}),this.brainstOdooEntries=e})}else this.brainstOdooEntries=e},getList(){this.isLoading=!0;let e=new i(this.page,this.limit);return e.addFilter(i.equals("module","product")),e.addAssociation("product"),e.addAssociation("product.parent"),e.addAssociation("product.options"),e.addAssociation("product.options.group"),e.addFields("recordId","odooId","product.name","product.parent.name","product.parentId","product.options.name","product.options.group.name"),e.addSorting(i.sort("product.parent.name","ASC",!1)),e.addSorting(i.sort("product.name","ASC",!1)),this.brainstOdooRepository.search(e,Shopware.Context.api).then(e=>{this.total=e.total,this.isLoading=!1,this.recordUpdate(e)}).catch(e=>{console.error("Failed to fetch Data:",e)})},getTemplateList(){this.isTemplateLoading=!0;let e=new i(this.page,this.limit);return e.addFilter(i.equals("module","template")),e.addAssociation("product"),e.addFields("recordId","odooId","product.name"),e.addSorting(i.sort("product.name","ASC",!1)),this.brainstOdooRepository.search(e,Shopware.Context.api).then(e=>{this.templateTotal=e.total,this.templateEntries=e,this.isTemplateLoading=!1}).catch(e=>{console.error("Failed to fetch Template Data:",e)})},fetchPluginUrl(){let e=new i;e.addFilter(i.equals("configurationKey","BrainstOdooPro.config.serverUrl")),this.systemConfigRepository.search(e,Shopware.Context.api).then(e=>{this.odooUrl=e.first().configurationValue}).catch(e=>{console.error("Failed to fetch the URL:",e)})},setActiveTab(e){this.activeTab=e,this.updateUrlWithTab(e)},initializeActiveTab(){let e=this.$route.query.tab;e&&("template"===e||"product"===e)&&(this.activeTab=e)},updateUrlWithTab(e){let t=this.$route,o={...t.query,tab:e};this.$router.replace({name:t.name,params:t.params,query:o}).catch(()=>{})}}});let l=Shopware.Data.Criteria;Shopware.Component.register("brainst-odoo-pro-property",{template:'<sw-page>\n    <template #smart-bar-back>\n        {% block sw_page_slot_smart_bar_back %}\n            <brainst-odoo-pro-back-button />\n        {% endblock %}\n    </template>\n\n    <template #smart-bar-header>\n        {% block brainst_odoo_table_property_smart_bar_header_title %}\n            <h2>{{ $tc(\'brainst-odoo-pro.property.title\') }}</h2>\n        {% endblock %}\n    </template>\n\n    <template #content>\n        {% block brainst_odoo_table_property_list_content %}\n            <sw-alert variant="info" class="sw-alert--no-icon" style="margin-bottom: 20px;">\n                <div class="sw-alert__message">\n                    <strong>{{ $tc(\'brainst-odoo-pro.property.label\') }}:</strong> {{ $tc(\'brainst-odoo-pro.property.note\') }}\n                </div>\n            </sw-alert>\n            <brainst-odoo-pro-progress-bar entity="property_group" :value="total"/>\n            <sw-entity-listing\n                    v-if="brainstOdooEntries"\n                    :items="brainstOdooEntries"\n                    :repository="brainstOdooRepository"\n                    :showSelection="false"\n                    :columns="columns"\n                    :allowDelete="false">\n                <template #column-recordId="{ item, column }">\n                    <router-link\n                            class="sw-data-grid__cell-value"\n                            :to="{ name: \'sw.property.detail\', params: { id: item.recordId } }"\n                    >\n                        {{ item.property?.name || item.recordId }}\n                    </router-link>\n                </template>\n                <template #column-odooId="{ item, column }">\n                    <a :href="`${odooUrl}/web#id=${item.odooId}&view_type=form&model=product.attribute`"\n                       class="sw-data-grid__cell-value" rel="nofollow noopener"\n                       target="_blank">{{ item.odooId }}</a>\n                </template>\n            </sw-entity-listing>\n        {% endblock %}\n    </template>\n</sw-page>\n',inject:["repositoryFactory"],data(){return{odooUrl:null,page:1,limit:25,brainstOdooEntries:null,total:0,isLoading:!0,currentLanguageId:Shopware.Context.api.languageId}},metaInfo(){return{title:this.$createTitle()}},created(){this.fetchPluginUrl(),this.getList()},computed:{brainstOdooRepository(){return this.repositoryFactory.create("brainst_odoo")},systemConfigRepository(){return this.repositoryFactory.create("system_config")},propertyRepository(){return this.repositoryFactory.create("property_group")},columns(){return[{allowResize:!0,dataIndex:"property.name",label:this.$tc("brainst-odoo-pro.property.table.name"),primary:!0,property:"property.name"},{allowResize:!0,dataIndex:"odooId",label:this.$tc("brainst-odoo-pro.property.table.odoo-id"),property:"odooId",naturalSorting:!0}]}},methods:{getList(){this.isLoading=!0;let e=new l(this.page,this.limit);return e.addFilter(l.equals("module","attribute")),e.addAssociation("property"),e.addFields("recordId","odooId","property.name","name"),e.addSorting(l.sort("property.name","ASC",!1)),this.brainstOdooRepository.search(e,Shopware.Context.api).then(e=>{this.total=e.total,this.brainstOdooEntries=e,this.isLoading=!1}).catch(e=>{console.error("Failed to fetch Data:",e)})},fetchPluginUrl(){let e=new l;e.addFilter(l.equals("configurationKey","BrainstOdooPro.config.serverUrl")),this.systemConfigRepository.search(e,Shopware.Context.api).then(e=>{this.odooUrl=e.first().configurationValue}).catch(e=>{console.error("Failed to fetch the URL:",e)})}}});let d=Shopware.Data.Criteria;Shopware.Component.register("brainst-odoo-pro-attribute-value",{template:'<sw-page>\n    <template #smart-bar-back>\n        {% block sw_page_slot_smart_bar_back %}\n            <brainst-odoo-pro-back-button />\n        {% endblock %}\n    </template>\n\n    <template #smart-bar-header>\n        {% block brainst_odoo_table_attribute_value_smart_bar_header_title %}\n            <h2>{{ $tc(\'brainst-odoo-pro.attribute-value.title\') }}</h2>\n        {% endblock %}\n    </template>\n\n    <template #content>\n        {% block brainst_odoo_table_attribute_value_list_content %}\n            <sw-alert variant="info" class="sw-alert--no-icon" style="margin-bottom: 20px;">\n                <div class="sw-alert__message">\n                    <strong>{{ $tc(\'brainst-odoo-pro.attribute-value.label\') }}:</strong> {{ $tc(\'brainst-odoo-pro.attribute-value.note\') }}\n                </div>\n            </sw-alert>\n            <brainst-odoo-pro-progress-bar entity="property_group_option" :value="total"/>\n            <sw-entity-listing\n                    v-if="brainstOdooEntries"\n                    :items="brainstOdooEntries"\n                    :repository="brainstOdooRepository"\n                    :showSelection="false"\n                    :columns="columns"\n                    :allowDelete="false">\n                <template #column-recordId="{ item, column }">\n                    <router-link\n                            class="sw-data-grid__cell-value"\n                            :to="{ name: \'sw.property.detail\', params: { id: item.recordId } }"\n                    >\n                        {{ item.propertyGroupOption?.name || item.recordId }}\n                    </router-link>\n                </template>\n                <template #column-odooId="{ item, column }">\n                    <a :href="`${odooUrl}/web#id=${item.odooId}&view_type=form&model=product.attribute.value`"\n                       class="sw-data-grid__cell-value" rel="nofollow noopener"\n                       target="_blank">{{ item.odooId }}</a>\n                </template>\n            </sw-entity-listing>\n        {% endblock %}\n    </template>\n</sw-page>\n',inject:["repositoryFactory"],data(){return{odooUrl:null,page:1,limit:25,brainstOdooEntries:null,total:0,isLoading:!0,currentLanguageId:Shopware.Context.api.languageId}},metaInfo(){return{title:this.$createTitle()}},created(){this.fetchPluginUrl(),this.getList()},computed:{brainstOdooRepository(){return this.repositoryFactory.create("brainst_odoo")},systemConfigRepository(){return this.repositoryFactory.create("system_config")},propertyGroupOptionRepository(){return this.repositoryFactory.create("property_group_option")},columns(){return[{allowResize:!0,dataIndex:"propertyGroupOption.name",label:this.$tc("brainst-odoo-pro.attribute-value.table.name"),primary:!0,property:"propertyGroupOption.name"},{allowResize:!0,dataIndex:"odooId",label:this.$tc("brainst-odoo-pro.attribute-value.table.odoo-id"),property:"odooId",naturalSorting:!0}]}},methods:{getList(){this.isLoading=!0;let e=new d(this.page,this.limit);return e.addFilter(d.equals("module","attribute_value")),e.addAssociation("propertyGroupOption"),e.addFields("recordId","odooId","propertyGroupOption.name"),e.addSorting(d.sort("propertyGroupOption.name","ASC",!1)),this.brainstOdooRepository.search(e,Shopware.Context.api).then(e=>{this.total=e.total,this.brainstOdooEntries=e,this.isLoading=!1}).catch(e=>{console.error("Failed to fetch Data:",e)})},fetchPluginUrl(){let e=new d;e.addFilter(d.equals("configurationKey","BrainstOdooPro.config.serverUrl")),this.systemConfigRepository.search(e,Shopware.Context.api).then(e=>{this.odooUrl=e.first().configurationValue}).catch(e=>{console.error("Failed to fetch the URL:",e)})}}});let c=Shopware.Data.Criteria;Shopware.Component.register("brainst-odoo-pro-customer",{template:'<sw-page>\n    <template #smart-bar-back>\n        {% block sw_page_slot_smart_bar_back %}\n            <brainst-odoo-pro-back-button />\n        {% endblock %}\n    </template>\n\n    <template #smart-bar-header>\n        {% block brainst_odoo_table_customer_smart_bar_header_title %}\n            <h2>{{ $tc(\'brainst-odoo-pro.customer.title\') }}</h2>\n        {% endblock %}\n    </template>\n\n    <template #content>\n        {% block brainst_odoo_table_customer_list_content %}\n            <brainst-odoo-pro-progress-bar entity="customer" :value="total"/>\n            <sw-entity-listing\n                    v-if="brainstOdooEntries"\n                    :items="brainstOdooEntries"\n                    :repository="brainstOdooRepository"\n                    :showSelection="false"\n                    :columns="columns"\n                    :allowDelete="false">\n                <template #column-customer.firstName="{ item, column }">\n                    <router-link\n                            class="sw-data-grid__cell-value"\n                            :to="{ name: \'sw.customer.detail\', params: { id: item.recordId } }"\n                    >\n                        {{ item.customer ? `${item.customer.firstName} ${item.customer.lastName}` : item.recordId }}\n                    </router-link>\n                </template>\n                <template #column-odooId="{ item, column }">\n                    <a :href="`${odooUrl}/web#id=${item.odooId}&view_type=form&model=res.partner`"\n                       class="sw-data-grid__cell-value" rel="nofollow noopener"\n                       target="_blank">{{ item.odooId }}</a>\n                </template>\n            </sw-entity-listing>\n        {% endblock %}\n    </template>\n</sw-page>\n',inject:["repositoryFactory"],data(){return{odooUrl:null,page:1,limit:25,brainstOdooEntries:null,total:0,isLoading:!0,currentLanguageId:Shopware.Context.api.languageId}},metaInfo(){return{title:this.$createTitle()}},created(){this.fetchPluginUrl(),this.getList()},computed:{brainstOdooRepository(){return this.repositoryFactory.create("brainst_odoo")},systemConfigRepository(){return this.repositoryFactory.create("system_config")},customerRepository(){return this.repositoryFactory.create("customer")},columns(){return[{allowResize:!0,dataIndex:"customer.firstName",label:this.$tc("brainst-odoo-pro.customer.table.name"),primary:!0,property:"customer.firstName"},{allowResize:!0,dataIndex:"odooId",label:this.$tc("brainst-odoo-pro.customer.table.odoo-id"),property:"odooId",naturalSorting:!0}]}},methods:{getList(){this.isLoading=!0;let e=new c(this.page,this.limit);return e.addFilter(c.equals("module","customer")),e.addAssociation("customer"),e.addFields("recordId","odooId","customer.firstName","customer.lastName","customer.email"),e.addSorting(c.sort("customer.firstName","ASC",!1)),this.brainstOdooRepository.search(e,Shopware.Context.api).then(e=>{this.total=e.total,this.brainstOdooEntries=e,this.isLoading=!1}).catch(e=>{console.error("Failed to fetch Data:",e)})},fetchPluginUrl(){let e=new c;e.addFilter(c.equals("configurationKey","BrainstOdooPro.config.serverUrl")),this.systemConfigRepository.search(e,Shopware.Context.api).then(e=>{this.odooUrl=e.first().configurationValue}).catch(e=>{console.error("Failed to fetch the URL:",e)})}}});let p=Shopware.Data.Criteria;Shopware.Component.register("brainst-odoo-pro-customer-address",{template:'<sw-page>\n    <template #smart-bar-back>\n        {% block sw_page_slot_smart_bar_back %}\n            <brainst-odoo-pro-back-button />\n        {% endblock %}\n    </template>\n\n    <template #smart-bar-header>\n        {% block brainst_odoo_table_customer_address_smart_bar_header_title %}\n            <h2>{{ $tc(\'brainst-odoo-pro.customer-address.title\') }}</h2>\n        {% endblock %}\n    </template>\n\n    <template #content>\n        {% block brainst_odoo_table_customer_address_list_content %}\n            <sw-alert variant="info" class="sw-alert--no-icon" style="margin-bottom: 20px;">\n                <div class="sw-alert__message">\n                    <strong>{{ $tc(\'brainst-odoo-pro.customer-address.label\') }}:</strong> {{ $tc(\'brainst-odoo-pro.customer-address.note\') }}\n                </div>\n            </sw-alert>\n            <brainst-odoo-pro-progress-bar entity="customer_address" :value="total"/>\n            <sw-entity-listing\n                    v-if="brainstOdooEntries"\n                    :items="brainstOdooEntries"\n                    :repository="brainstOdooRepository"\n                    :showSelection="false"\n                    :columns="columns"\n                    :allowDelete="false">\n                <template #column-customerAddress.firstName="{ item, column }">\n                    <router-link\n                            class="sw-data-grid__cell-value"\n                            :to="{ name: \'sw.customer.detail.addresses\', params: { id: item.customerAddress.customerId } }"\n                    >\n                        {{ item.customerAddress ? `${item.customerAddress.firstName} ${item.customerAddress.lastName} - ${item.customerAddress.street}` : item.recordId }}\n                    </router-link>\n                </template>\n                <template #column-odooId="{ item, column }">\n                    <a :href="`${odooUrl}/web#id=${item.odooId}&view_type=form&model=res.partner`"\n                       class="sw-data-grid__cell-value" rel="nofollow noopener"\n                       target="_blank">{{ item.odooId }}</a>\n                </template>\n            </sw-entity-listing>\n        {% endblock %}\n    </template>\n</sw-page>\n',inject:["repositoryFactory"],data(){return{odooUrl:null,page:1,limit:25,brainstOdooEntries:null,total:0,isLoading:!0,currentLanguageId:Shopware.Context.api.languageId}},metaInfo(){return{title:this.$createTitle()}},created(){this.fetchPluginUrl(),this.getList()},computed:{brainstOdooRepository(){return this.repositoryFactory.create("brainst_odoo")},systemConfigRepository(){return this.repositoryFactory.create("system_config")},customerAddressRepository(){return this.repositoryFactory.create("customer_address")},columns(){return[{allowResize:!0,dataIndex:"customerAddress.firstName",label:this.$tc("brainst-odoo-pro.customer-address.table.name"),primary:!0,property:"customerAddress.firstName"},{allowResize:!0,dataIndex:"odooId",label:this.$tc("brainst-odoo-pro.customer-address.table.odoo-id"),property:"odooId",naturalSorting:!0}]}},methods:{getList(){this.isLoading=!0;let e=new p(this.page,this.limit);return e.addFilter(p.equals("module","customer_address")),e.addAssociation("customerAddress"),e.addFields("recordId","odooId","customerAddress.firstName","customerAddress.lastName","customerAddress.street","customerAddress.customerId"),e.addSorting(p.sort("customerAddress.firstName","ASC",!1)),this.brainstOdooRepository.search(e,Shopware.Context.api).then(e=>{this.total=e.total,this.brainstOdooEntries=e,this.isLoading=!1}).catch(e=>{console.error("Failed to fetch Data:",e)})},fetchPluginUrl(){let e=new p;e.addFilter(p.equals("configurationKey","BrainstOdooPro.config.serverUrl")),this.systemConfigRepository.search(e,Shopware.Context.api).then(e=>{this.odooUrl=e.first().configurationValue}).catch(e=>{console.error("Failed to fetch the URL:",e)})}}});let m=Shopware.Data.Criteria;Shopware.Component.register("brainst-odoo-pro-order",{template:'<sw-page>\n    <template #smart-bar-back>\n        {% block sw_page_slot_smart_bar_back %}\n            <brainst-odoo-pro-back-button />\n        {% endblock %}\n    </template>\n\n    <template #smart-bar-header>\n        {% block brainst_odoo_table_order_smart_bar_header_title %}\n            <h2>{{ $tc(\'brainst-odoo-pro.order.title\') }}</h2>\n        {% endblock %}\n    </template>\n\n    <template #content>\n        {% block brainst_odoo_table_order_list_content %}\n            <brainst-odoo-pro-progress-bar entity="order" :value="total"/>\n            <sw-entity-listing\n                    v-if="brainstOdooEntries"\n                    :items="brainstOdooEntries"\n                    :repository="brainstOdooRepository"\n                    :showSelection="false"\n                    :columns="columns"\n                    :allowDelete="false">\n                <template #column-order.orderNumber="{ item, column }">\n                    <router-link\n                            class="sw-data-grid__cell-value"\n                            :to="{ name: \'sw.order.detail\', params: { id: item.recordId } }"\n                    >\n                        {{ item.order?.orderNumber || item.recordId }}\n                    </router-link>\n                </template>\n                <template #column-odooId="{ item, column }">\n                    <a :href="`${odooUrl}/web#id=${item.odooId}&view_type=form&model=sale.order`"\n                       class="sw-data-grid__cell-value" rel="nofollow noopener"\n                       target="_blank">{{ item.odooId }}</a>\n                </template>\n            </sw-entity-listing>\n        {% endblock %}\n    </template>\n</sw-page>\n',inject:["repositoryFactory"],data(){return{odooUrl:null,page:1,limit:25,brainstOdooEntries:null,total:0,isLoading:!0,currentLanguageId:Shopware.Context.api.languageId}},metaInfo(){return{title:this.$createTitle()}},created(){this.fetchPluginUrl(),this.getList()},computed:{brainstOdooRepository(){return this.repositoryFactory.create("brainst_odoo")},systemConfigRepository(){return this.repositoryFactory.create("system_config")},orderRepository(){return this.repositoryFactory.create("order")},columns(){return[{allowResize:!0,dataIndex:"order.orderNumber",label:this.$tc("brainst-odoo-pro.order.table.name"),primary:!0,property:"order.orderNumber"},{allowResize:!0,dataIndex:"odooId",label:this.$tc("brainst-odoo-pro.order.table.odoo-id"),property:"odooId",naturalSorting:!0}]}},methods:{getList(){this.isLoading=!0;let e=new m(this.page,this.limit);return e.addFilter(m.equals("module","order")),e.addAssociation("order"),e.addFields("recordId","odooId","order.orderNumber","order.amountTotal"),e.addSorting(m.sort("order.orderNumber","ASC",!1)),this.brainstOdooRepository.search(e,Shopware.Context.api).then(e=>{this.total=e.total,this.brainstOdooEntries=e,this.isLoading=!1}).catch(e=>{console.error("Failed to fetch Data:",e)})},fetchPluginUrl(){let e=new m;e.addFilter(m.equals("configurationKey","BrainstOdooPro.config.serverUrl")),this.systemConfigRepository.search(e,Shopware.Context.api).then(e=>{this.odooUrl=e.first().configurationValue}).catch(e=>{console.error("Failed to fetch the URL:",e)})}}});let h=Shopware.Data.Criteria;Shopware.Component.register("brainst-odoo-pro-delivery",{template:'<sw-page>\n    <template #smart-bar-back>\n        {% block sw_page_slot_smart_bar_back %}\n            <brainst-odoo-pro-back-button />\n        {% endblock %}\n    </template>\n\n    <template #smart-bar-header>\n        {% block brainst_odoo_table_delivery_smart_bar_header_title %}\n            <h2>{{ $tc(\'brainst-odoo-pro.delivery.title\') }}</h2>\n        {% endblock %}\n    </template>\n\n    <template #content>\n        {% block brainst_odoo_table_delivery_list_content %}\n            <brainst-odoo-pro-progress-bar entity="delivery" :value="total"/>\n            <sw-entity-listing\n                    v-if="brainstOdooEntries"\n                    :items="brainstOdooEntries"\n                    :repository="brainstOdooRepository"\n                    :showSelection="false"\n                    :columns="columns"\n                    :allowDelete="false">\n                <template #column-orderDelivery.order.orderNumber="{ item, column }">\n                    <router-link\n                            class="sw-data-grid__cell-value"\n                            :to="{ name: \'sw.order.detail\', params: { id: item.orderDelivery.orderId } }"\n                    >\n                        {{ item.orderDelivery?.order?.orderNumber || item.recordId }}\n                    </router-link>\n                </template>\n                <template #column-odooId="{ item, column }">\n                    <a :href="`${odooUrl}/web#id=${item.odooId}&view_type=form&model=stock.picking`"\n                       class="sw-data-grid__cell-value" rel="nofollow noopener"\n                       target="_blank">{{ item.odooId }}</a>\n                </template>\n            </sw-entity-listing>\n        {% endblock %}\n    </template>\n</sw-page>\n',inject:["repositoryFactory"],data(){return{odooUrl:null,page:1,limit:25,brainstOdooEntries:null,total:0,isLoading:!0,currentLanguageId:Shopware.Context.api.languageId}},metaInfo(){return{title:this.$createTitle()}},created(){this.fetchPluginUrl(),this.getList()},computed:{brainstOdooRepository(){return this.repositoryFactory.create("brainst_odoo")},systemConfigRepository(){return this.repositoryFactory.create("system_config")},orderDeliveryRepository(){return this.repositoryFactory.create("order_delivery")},columns(){return[{allowResize:!0,dataIndex:"orderDelivery.order.orderNumber",label:this.$tc("brainst-odoo-pro.delivery.table.name"),primary:!0,property:"orderDelivery.order.orderNumber"},{allowResize:!0,dataIndex:"odooId",label:this.$tc("brainst-odoo-pro.delivery.table.odoo-id"),property:"odooId",naturalSorting:!0}]}},methods:{getList(){this.isLoading=!0;let e=new h(this.page,this.limit);return e.addFilter(h.equals("module","delivery")),e.addAssociation("orderDelivery"),e.addAssociation("orderDelivery.order"),e.addFields("recordId","odooId","orderDelivery.orderId","orderDelivery.order.orderNumber"),e.addSorting(h.sort("orderDelivery.order.orderNumber","ASC",!1)),this.brainstOdooRepository.search(e,Shopware.Context.api).then(e=>{this.total=e.total,this.brainstOdooEntries=e,this.isLoading=!1}).catch(e=>{console.error("Failed to fetch Data:",e)})},fetchPluginUrl(){let e=new h;e.addFilter(h.equals("configurationKey","BrainstOdooPro.config.serverUrl")),this.systemConfigRepository.search(e,Shopware.Context.api).then(e=>{this.odooUrl=e.first().configurationValue}).catch(e=>{console.error("Failed to fetch the URL:",e)})}}});let b=Shopware.Data.Criteria;Shopware.Component.register("brainst-odoo-pro-transaction",{template:'<sw-page>\n    <template #smart-bar-back>\n        {% block sw_page_slot_smart_bar_back %}\n            <brainst-odoo-pro-back-button/>\n        {% endblock %}\n    </template>\n\n    <template #smart-bar-header>\n        {% block brainst_odoo_table_transaction_smart_bar_header_title %}\n            <h2>{{ $tc(\'brainst-odoo-pro.transaction.title\') }}</h2>\n        {% endblock %}\n    </template>\n\n    <template #content>\n        {% block brainst_odoo_table_transaction_list_content %}\n            <brainst-odoo-pro-progress-bar entity="order_transaction" :value="total"/>\n            <sw-entity-listing\n                    v-if="brainstOdooEntries"\n                    :items="brainstOdooEntries"\n                    :repository="brainstOdooRepository"\n                    :showSelection="false"\n                    :columns="columns"\n                    :allowDelete="false">\n                <template #column-order.orderNumber="{ item, column }">\n                    <router-link\n                            class="sw-data-grid__cell-value"\n                            :to="{ name: \'sw.order.detail\', params: { id: item.recordId } }"\n                    >\n                        {{ item.order?.orderNumber || item.recordId }}\n                    </router-link>\n                </template>\n                <template #column-odooId="{ item, column }">\n                    <a :href="`${odooUrl}/web#id=${item.odooId}&view_type=form&model=account.payment`"\n                       class="sw-data-grid__cell-value" rel="nofollow noopener"\n                       target="_blank">{{ item.odooId }}</a>\n                </template>\n            </sw-entity-listing>\n        {% endblock %}\n    </template>\n</sw-page>\n',inject:["repositoryFactory"],data(){return{odooUrl:null,page:1,limit:25,brainstOdooEntries:null,total:0,isLoading:!0,currentLanguageId:Shopware.Context.api.languageId}},metaInfo(){return{title:this.$createTitle()}},created(){this.fetchPluginUrl(),this.getList()},computed:{brainstOdooRepository(){return this.repositoryFactory.create("brainst_odoo")},systemConfigRepository(){return this.repositoryFactory.create("system_config")},orderTransactionRepository(){return this.repositoryFactory.create("order_transaction")},columns(){return[{allowResize:!0,dataIndex:"order.orderNumber",label:this.$tc("brainst-odoo-pro.transaction.table.name"),primary:!0,property:"order.orderNumber"},{allowResize:!0,dataIndex:"odooId",label:this.$tc("brainst-odoo-pro.transaction.table.odoo-id"),property:"odooId",naturalSorting:!0}]}},methods:{getList(){this.isLoading=!0;let e=new b(this.page,this.limit);return e.addFilter(b.equals("module","transaction")),e.addAssociation("order"),e.addFields("recordId","odooId","order.id","order.orderNumber"),e.addSorting(b.sort("order.orderNumber","ASC",!1)),this.brainstOdooRepository.search(e,Shopware.Context.api).then(e=>{this.total=e.total,this.brainstOdooEntries=e,this.isLoading=!1}).catch(e=>{console.error("Failed to fetch Data:",e)})},fetchPluginUrl(){let e=new b;e.addFilter(b.equals("configurationKey","BrainstOdooPro.config.serverUrl")),this.systemConfigRepository.search(e,Shopware.Context.api).then(e=>{this.odooUrl=e.first().configurationValue}).catch(e=>{console.error("Failed to fetch the URL:",e)})}}}),u(451);let{Component:g,Mixin:y}=Shopware;g.register("brainst-odoo-pro-synchronize-button",{template:'<div>\n    <sw-button-process\n            variant="primary"\n            :isLoading="isLoading"\n            :processSuccess="isVerifyDone"\n            @click="showConfirmationModal">\n        <sw-icon :small="true" name="regular-sync"></sw-icon>\n        {{ $tc(\'brainst-odoo-pro.synchronize-button.title\') }}\n    </sw-button-process>\n    <sw-help-text :text="$tc(\'brainst-odoo-pro.synchronize-button.description\')" />\n\n    \n    <sw-modal\n        v-if="showConfirmation"\n        class="brainst-odoo-pro-sync-confirmation-modal"\n        :title="$tc(\'brainst-odoo-pro.synchronize-button.confirmation.title\')"\n        variant="small"\n        @modal-close="hideConfirmationModal"\n    >\n        <div class="brainst-odoo-pro-sync-confirmation-modal__content">\n            <div class="brainst-odoo-pro-sync-confirmation-modal__message">\n                <sw-icon name="regular-exclamation-triangle" size="24px" class="warning-icon"></sw-icon>\n                <p class="confirmation-text">\n                    {{ $tc(\'brainst-odoo-pro.synchronize-button.confirmation.message\') }}\n                </p>\n                <p class="warning-text">\n                    {{ $tc(\'brainst-odoo-pro.synchronize-button.confirmation.warning\') }}\n                </p>\n            </div>\n        </div>\n\n        <template #modal-footer>\n            <sw-button\n                size="small"\n                @click="hideConfirmationModal"\n                :disabled="isLoading"\n            >\n                {{ $tc(\'brainst-odoo-pro.synchronize-button.confirmation.cancel\') }}\n            </sw-button>\n            <sw-button-process\n                class="brainst-odoo-pro-sync-confirmation-modal__confirm-button"\n                :isLoading="isLoading"\n                variant="primary"\n                size="small"\n                @click="confirmSynchronization"\n            >\n                {{ $tc(\'brainst-odoo-pro.synchronize-button.confirmation.confirm\') }}\n            </sw-button-process>\n        </template>\n    </sw-modal>\n</div>',inject:["brainstOdooProSynchronizationService"],mixins:[y.getByName("notification")],data(){return{isLoading:!1,isVerifyDone:!1,showConfirmation:!1}},methods:{showConfirmationModal(){this.showConfirmation=!0},hideConfirmationModal(){this.showConfirmation=!1},confirmSynchronization(){this.hideConfirmationModal(),this.synchronize()},synchronize(){this.isLoading=!0,this.brainstOdooProSynchronizationService.synchronise().then(e=>{e.code?this.createNotificationError({title:this.$tc("brainst-odoo-pro.synchronize-button.title"),message:this.$tc("brainst-odoo-pro.synchronize-button.error_"+e.code)}):this.createNotificationSuccess({title:this.$tc("brainst-odoo-pro.synchronize-button.title"),message:this.$tc("brainst-odoo-pro.synchronize-button.success")})}).catch(e=>{let t=2;e.response&&e.response.code&&(t=e.response.code),this.createNotificationError({title:this.$tc("brainst-odoo-pro.synchronize-button.title"),message:this.$tc("brainst-odoo-pro.synchronize-button.error_"+t)})}).finally(()=>{this.isLoading=!1})}}}),u(91);let{Component:f}=Shopware;f.register("brainst-odoo-pro-progress-bar",{template:'{% block sw_progress_bar %}\n    <div class="brainst-progress-bar" :class="containerClasses">\n        {% block sw_progress_bar_total %}\n            <div class="brainst-progress-bar__container">\n                \n                <div class="brainst-progress-bar__header">\n                    <div class="brainst-progress-bar__label">\n                        <span class="brainst-progress-bar__title">{{ $tc(\'brainst-odoo-pro.progressBar.text\') }}</span>\n                        <span class="brainst-progress-bar__status" :class="statusClasses">{{ statusText }}</span>\n                    </div>\n                    <div class="brainst-progress-bar__percentage-badge" :class="badgeClasses">\n                        {{ percentageText }}\n                    </div>\n                </div>\n\n                \n                <div class="brainst-progress-bar__track" @click="showDetails = !showDetails">\n                    {% block sw_progress_bar_total_value %}\n                        \n                        <div class="brainst-progress-bar__background"></div>\n\n                        \n                        <div\n                            class="brainst-progress-bar__fill"\n                            :style="{ width: styleWidth }"\n                            :class="progressClasses"\n                        >\n                            \n                            <div class="brainst-progress-bar__shimmer" v-if="isActive"></div>\n                        </div>\n\n                        \n                        <div class="brainst-progress-bar__milestones">\n                            <div\n                                v-for="milestone in milestones"\n                                :key="milestone"\n                                class="brainst-progress-bar__milestone"\n                                :class="{ \'brainst-progress-bar__milestone--reached\': percentage >= milestone }"\n                                :style="{ left: milestone + \'%\' }"\n                            ></div>\n                        </div>\n                    {% endblock %}\n                </div>\n\n                \n                <div class="brainst-progress-bar__details">\n                    <span class="brainst-progress-bar__count">{{ value }} / {{ maxValue }}</span>\n                    <span class="brainst-progress-bar__remaining" v-if="remaining > 0">\n                        {{ remaining }} {{ $tc(\'brainst-odoo-pro.progressBar.remaining\') }}\n                    </span>\n                </div>\n\n                \n                <div class="brainst-progress-bar__loading" v-if="isLoading">\n                    <div class="brainst-progress-bar__spinner"></div>\n                </div>\n            </div>\n        {% endblock %}\n    </div>\n{% endblock %}\n',compatConfig:Shopware.compatConfig,inject:["userActivityService","brainstOdooProApiService"],data(){return{maxValue:100,isLoading:!1,showDetails:!1,milestones:[25,50,75],animationDelay:0}},props:{value:{type:Number,default:0},entity:{type:String,required:!1,default:null},module:{type:String,required:!1,default:null},totalRecords:{type:Number,required:!1,default:null},animated:{type:Boolean,required:!1,default:!0}},mounted(){null===this.totalRecords?this.recordCount():this.maxValue=this.totalRecords,this.$nextTick(()=>{this.animationDelay=100})},computed:{percentage(){let e=this.value/this.maxValue*100;return e>100&&(e=100),e<0&&(e=0),Math.round(e)},styleWidth(){return`${this.percentage}%`},percentageText(){return`${this.percentage}%`},remaining(){return Math.max(0,this.maxValue-this.value)},status(){return this.isLoading?"idle":this.percentage>=100?"completed":this.percentage>0?"syncing":"idle"},statusText(){switch(this.status){case"syncing":return this.$tc("brainst-odoo-pro.progressBar.syncing");case"completed":return this.$tc("brainst-odoo-pro.progressBar.completed");case"error":return this.$tc("brainst-odoo-pro.progressBar.error");default:return this.$tc("brainst-odoo-pro.progressBar.idle")}},isActive(){return"syncing"===this.status||this.value>0&&this.value<this.maxValue},containerClasses(){return{"brainst-progress-bar--animated":this.animated,"brainst-progress-bar--loading":this.isLoading,"brainst-progress-bar--completed":this.percentage>=100,"brainst-progress-bar--active":this.isActive,[`brainst-progress-bar--${this.status}`]:!0}},progressClasses(){return{"brainst-progress-bar__fill--no-transition":!this.animated||this.value<1&&0===this.percentage,"brainst-progress-bar__fill--completed":this.percentage>=100,"brainst-progress-bar__fill--milestone":this.milestones.includes(this.percentage)}},statusClasses(){return{[`brainst-progress-bar__status--${this.status}`]:!0}},badgeClasses(){return{"brainst-progress-bar__percentage-badge--completed":this.percentage>=100,"brainst-progress-bar__percentage-badge--high":this.percentage>=75,"brainst-progress-bar__percentage-badge--medium":this.percentage>=50&&this.percentage<75,"brainst-progress-bar__percentage-badge--low":this.percentage<50}}},watch:{value(){this.userActivityService.updateLastUserActivity()},totalRecords(e){null!==e&&(this.maxValue=e)}},methods:{recordCount(){this.isLoading=!0,this.brainstOdooProApiService.recordCount(this.entity,this.module).then(e=>{e.isValid?this.maxValue=e.totalRecords:this.maxValue=0}).catch(e=>{console.error("Failed to fetch record count:",e),this.maxValue=0}).finally(()=>{this.isLoading=!1})}}});let{Component:v,Mixin:_}=Shopware;v.register("brainst-odoo-pro-icon",{template:'{% block brainst_odoo_pro_icon %}\n    <img style="width:30px;height:30px" :src="assetFilter(\'brainstodoopro/static/img/brainst-odoo-pro-icon.png\')">\n{% endblock %}\n',mixins:[_.getByName("brainst-odoo-pro-helper")]});let{Component:w}=Shopware;w.register("brainst-odoo-pro-back-button",{template:'{% block brainst_odoo_pro_back_button %}\n    <router-link\n        class="smart-bar__back-btn"\n        :to="{ name: \'brainst.odoo.pro.list\' }"\n    >\n        <sw-icon\n            name="regular-chevron-left"\n            small\n        />\n        <brainst-odoo-pro-icon />\n    </router-link>\n{% endblock %}\n',metaInfo(){return{title:this.$createTitle()}}}),u(841),Shopware.Component.register("brainst-odoo-pro-gauge-chart",{template:'{% block brainst_odoo_pro_gauge_chart %}\n    <div class="brainst-gauge-chart">\n        <div class="gauge-title" v-if="title">\n            <h4>{{ title }}</h4>\n        </div>\n        \n        <div class="gauge-container" v-if="!isLoading">\n            <div class="gauge-svg-wrapper">\n                <svg viewBox="-10 -10 120 80" class="gauge-svg">\n                    \n                    <path\n                        v-for="(segment, index) in gaugeSegments"\n                        :key="index"\n                        :d="segment.path"\n                        :fill="segment.color"\n                        :opacity="segment.isActive ? 1 : 0.3"\n                        class="gauge-segment"\n                        :class="{ \'gauge-segment--active\': segment.isActive }"\n                    />\n\n                    \n                    <g class="gauge-scale">\n                        <text\n                            v-for="(mark, index) in scaleMarks"\n                            :key="\'label-\' + index"\n                            :x="mark.textX"\n                            :y="mark.textY"\n                            text-anchor="middle"\n                            dominant-baseline="middle"\n                            class="gauge-scale-text"\n                            :class="{ \'gauge-scale-text--small\': mark.value >= 100 }"\n                        >\n                            {{ mark.value >= maxValue ? (maxValue === 99 ? \'99+\' : \'999+\') : mark.value }}\n                        </text>\n                    </g>\n\n                    \n                    <path\n                        :d="needlePath"\n                        fill="#1f2937"\n                        class="gauge-needle"\n                    />\n\n                    \n                    <circle\n                        cx="50"\n                        cy="50"\n                        r="8"\n                        :fill="activeSegmentColor"\n                        class="gauge-center"\n                    />\n\n                    \n                    <text\n                        x="50"\n                        y="50"\n                        text-anchor="middle"\n                        dominant-baseline="middle"\n                        class="gauge-center-text"\n                        fill="white"\n                    >\n                        {{ displayText }}\n                    </text>\n                </svg>\n            </div>\n        </div>\n        \n        \n        <div class="gauge-loading" v-if="isLoading">\n            <sw-loader size="32px"></sw-loader>\n        </div>\n    </div>\n{% endblock %}\n',props:{value:{type:Number,required:!0,default:0},maxValue:{type:Number,required:!0,default:100},title:{type:String,required:!0,default:""},unit:{type:String,required:!1,default:""},displayValue:{type:String,required:!1,default:null},isLoading:{type:Boolean,required:!1,default:!1}},computed:{percentage(){return Math.min(this.value/this.maxValue*100,100)},needleAngle(){return 180-this.percentage/100*180},gaugeSegments(){let e=[],t=["#ef4444","#f59e0b","#eab308","#84cc16","#22c55e"];for(let o=0;o<5;o++){let r=180-36*o,n=r-36;e.push({startAngle:n,endAngle:r,color:t[o],path:this.createArcPath(n,r,30,45),isActive:this.percentage>=20*o})}return e},scaleMarks(){let e=[];for(let t=0;t<6;t++){let o=t/5*100,r=180-o/100*180,n=Math.round(o/100*this.maxValue),a=r*Math.PI/180,s=50+55*Math.cos(a),i=50-55*Math.sin(a);e.push({value:n,angle:r,textX:s,textY:i,percentage:o})}return e},needlePath(){let e=this.needleAngle,t=e*Math.PI/180,o=(e-90)*Math.PI/180,r=(e+90)*Math.PI/180;return`M ${50+.8*Math.cos(o)} ${50-.8*Math.sin(o)} L ${50+28*Math.cos(t)} ${50-28*Math.sin(t)} L ${50+.8*Math.cos(r)} ${50-.8*Math.sin(r)} Z`},displayText(){return this.displayValue||this.value.toString()},activeSegmentColor(){if(!this.gaugeSegments||0===this.gaugeSegments.length)return"#6b7280";let e=null;for(let t=this.gaugeSegments.length-1;t>=0;t--)if(this.gaugeSegments[t].isActive){e=this.gaugeSegments[t];break}return e?e.color:"#6b7280"}},methods:{createArcPath(e,t,o,r){let n=e*Math.PI/180,a=t*Math.PI/180,s=50+o*Math.cos(n),i=50-o*Math.sin(n),l=t-e>180?1:0;return`M ${s} ${i}
                    L ${50+r*Math.cos(n)} ${50-r*Math.sin(n)}
                    A ${r} ${r} 0 ${l} 1 ${50+r*Math.cos(a)} ${50-r*Math.sin(a)}
                    L ${50+o*Math.cos(a)} ${50-o*Math.sin(a)}
                    A ${o} ${o} 0 ${l} 0 ${s} ${i} Z`}}});var S=JSON.parse('{"brainst-odoo-pro":{"title":"Brainst Odoo Pro","description":"Shopware Odoo Synchronisierungsdaten von Kategorien, Vertriebskan\xe4len, Lieferung, Eigentum, Produkten, Kunden und Bestellungen.","list":{"title":"Brainst Odoo Pro","sales-channel":"Vertriebskanaltabelle","category":"Kategorietabelle","product":"Produkttabelle","property":"Eigenschaftstabelle","attribute-value":"Eigenschaftswerttabelle","customer":"Kundentabelle","customer-address":"Kundenadresstabelle","order":"Bestelltabelle","delivery":"Liefertabelle","transaction":"Transaktionstabelle"},"category":{"title":"Kategorietabelle","table":{"id":"Shopware-Kategorie-ID","odoo-id":"Odoo-Kategorie-ID","name":"Kategoriename"}},"product":{"title":"Produkttabelle","tabs":{"template":"Produkte","product":"Varianten"},"notes":{"template":"Dies sind Hauptprodukte, die als Vorlagen in Odoo gespeichert werden.","product":"Dies umfasst alle Varianten von Produkten und Produkte, die keine Varianten haben. Odoo erstellt f\xfcr jedes Produkt eine Variante. Badge-Indikatoren: P = Hauptprodukt, V = Variante."},"badges":{"parent":"Hauptprodukt","variant":"Variante"},"table":{"id":"Shopware-Produkt-ID","odoo-id":"Odoo-Produkt-ID","name":"Produktname"},"labels":{"id":"Produktvarianten-Identifikator in Shopware","odoo-id":"Entsprechende Produktvarianten-ID im Odoo-System","name":"Anzeigename mit Variantenoptionen (P = Hauptprodukt, V = Variante)"}},"template":{"title":"Vorlagentabelle","table":{"id":"Shopware-Produkt-ID","odoo-id":"Odoo-Vorlage-ID","name":"Produktname"},"labels":{"id":"Hauptprodukt-Identifikator in Shopware","odoo-id":"Entsprechende Vorlagen-ID im Odoo-System","name":"Anzeigename des Hauptprodukts"}},"property":{"title":"Eigenschaftstabelle","label":"Eigenschaft","note":"Shopware-Eigenschaften werden in Odoo als Attribute synchronisiert.","table":{"id":"Shopware-Eigenschaft-ID","odoo-id":"Odoo-Attribut-ID","name":"Eigenschaftsname"}},"customer":{"title":"Kundentabelle","table":{"id":"Shopware-Kunden-ID","odoo-id":"Odoo-Kunden-ID","name":"Kundenname"}},"order":{"title":"Bestelltabelle","table":{"id":"Shopware-Bestell-ID","odoo-id":"Odoo-Bestell-ID","name":"Bestellnummer"}},"sales-channel":{"title":"Vertriebskanaltabelle","table":{"id":"Shopware-Vertriebskanal-ID","odoo-id":"Odoo-Unternehmen-ID","name":"Vertriebskanalname"}},"attribute-value":{"title":"Eigenschaftswerttabelle","label":"Eigenschaftswert","note":"Shopware-Eigenschaftswerte werden in Odoo als Attributwerte synchronisiert.","table":{"id":"Shopware-Eigenschaftswert-ID","odoo-id":"Odoo-Attributwert-ID","name":"Eigenschaftswertname"}},"customer-address":{"title":"Kundenadresstabelle","label":"Kundenadresse","note":"Kundenadressen werden als untergeordnete Kontakte in Odoo synchronisiert, wobei der Shopware-Kunde als \xfcbergeordneter Kontakt fungiert.","table":{"id":"Shopware-Adress-ID","odoo-id":"Odoo-Kontakt-ID","name":"Adressname"}},"delivery":{"title":"Liefertabelle","table":{"id":"Shopware-Liefer-ID","odoo-id":"Odoo-Liefer-ID","name":"Bestellnummer"}},"transaction":{"title":"Transaktionstabelle","table":{"id":"Shopware-Transaktions-ID","odoo-id":"Odoo-Zahlungs-ID","name":"Bestellnummer"}},"api-verify-button":{"title":"API-Test","buttonLabel":"\xdcberpr\xfcfen Sie die API-Verbindung","success":"Verbindung mit Erfolg getestet","error":"Verbindung konnte nicht hergestellt werden. Bitte \xfcberpr\xfcfen Sie die Zugangsdaten","error_400":"Es ist ein Fehler aufgetreten. Bitte \xfcberpr\xfcfen Sie die Zugangsdaten","error_401":"Authentifizierung fehlgeschlagen. Bitte \xfcberpr\xfcfen Sie die Anmeldeinformationen.","error_422":"Diese Version von Odoo wird nicht unterst\xfctzt, unterst\xfctzt nur Odoo 17 und 18.","error_424":"Einige Module sind nicht installiert, bitte installieren","error_500":"Serverfehler"},"synchronize-button":{"title":"Synchronisieren","description":"Die Verarbeitung der Warteschlange wird einige Zeit in Anspruch nehmen. F\xfchren Sie dies daher bitte aus, wenn die Nutzung Ihrer Website f\xfcr einige Zeit minimal ist.<br/>Die gesch\xe4tzte Zeit betr\xe4gt 1000 Produkte pro Stunde*.","success":"Alle Entit\xe4ten werden zur Synchronisierung in die Warteschlange gestellt","error_1":"Bitte richten Sie die Odoo-Konfiguration ein und versuchen Sie es dann erneut","error_2":"Bei der Synchronisierung ist ein Fehler aufgetreten. Bitte versuchen Sie es erneut","error_3":"Sie haben die erste Synchronisierung bereits durchgef\xfchrt oder sind dabei","confirmation":{"title":"Synchronisierung best\xe4tigen","message":"Sind Sie sicher, dass Sie den Synchronisierungsprozess starten m\xf6chten? Einmal gestartet, kann diese Aktion nicht r\xfcckg\xe4ngig gemacht werden und synchronisiert alle Daten zwischen Shopware und Odoo.","warning":"Dieser Prozess kann erhebliche Zeit in Anspruch nehmen und sollte w\xe4hrend verkehrsarmer Zeiten durchgef\xfchrt werden.","confirm":"Ja, Synchronisierung starten","cancel":"Abbrechen"}},"progressBar":{"text":"Synchronisierungsfortschritt","idle":"Bereit","syncing":"Synchronisiert","completed":"Abgeschlossen","error":"Fehler","remaining":"verbleibend"},"automation-modal":{"title":"Automatisierungskonfiguration","description":"Konfigurieren Sie Automatisierungseinstellungen f\xfcr die bidirektionale Synchronisierung zwischen Shopware und Odoo.","toggleLabel":"Bidirektionale Synchronisierung aktivieren","toggleHelpText":"Wenn aktiviert, werden \xc4nderungen in Odoo automatisch mit Shopware synchronisiert","saveButton":"Speichern","success":"Automatisierungskonfiguration erfolgreich gespeichert","error":"Fehler beim Erstellen der Automatisierungsregeln. Bitte \xfcberpr\xfcfen Sie Ihre Odoo-Verbindung und versuchen Sie es erneut."},"dashboard":{"refresh":"Aktualisieren","title":"Synchronisierungs-Dashboard","totalSynced":"Gesamt Synchronisiert","entityTypes":"Entit\xe4tstypen","entitySynced":"Entit\xe4t Synchronisiert","syncStatus":"Synchronisierungsstatus","overallProgress":"Gesamtfortschritt","synced":"Synchronisiert","pending":"Ausstehend","total":"Gesamt","records":"Datens\xe4tze","gauge":{"title":"Sync-Rate","recordsPerHour":"Datens\xe4tze/Stunde","recordsPerMinute":"Datens\xe4tze/Minute","hour":"Stunde","minute":"Minute","lastHour":"Letzte Stunde","lastMinute":"Letzte Minute","switchToHour":"Zur Stundenansicht wechseln","switchToMinute":"Zur Minutenansicht wechseln"},"entityNames":{"salesChannels":"Vertriebskan\xe4le","categories":"Kategorien","productVariants":"Produktvarianten","products":"Produkte","propertyGroups":"Eigenschaftsgruppen","propertyGroupOptions":"Eigenschaftsgruppen-Optionen","customers":"Kunden","customerAddresses":"Kundenadressen","orders":"Bestellungen","deliveries":"Lieferungen","transactions":"Transaktionen"},"tooltips":{"complete":"Alle Datens\xe4tze sind synchronisiert","partial":"Einige Datens\xe4tze sind synchronisiert","pending":"Noch keine Datens\xe4tze synchronisiert","noData":"Keine Daten verf\xfcgbar"}}}}'),k=JSON.parse('{"brainst-odoo-pro":{"title":"Brainst Odoo Pro","description":"Shopware Odoo synchronization data of categories, sales channels, delivery, property, products, customers, and orders.","list":{"title":"Brainst Odoo Pro","sales-channel":"Sales Channel Table","category":"Category Table","product":"Product Table","property":"Property Table","attribute-value":"Property Value Table","customer":"Customer Table","customer-address":"Customer Address Table","order":"Order Table","delivery":"Delivery Table","transaction":"Transaction Table"},"category":{"title":"Category Table","table":{"id":"Shopware Category Id","odoo-id":"Odoo Category Id","name":"Category Name"}},"product":{"title":"Product Table","tabs":{"template":"Products","product":"Variants"},"notes":{"template":"These are main products which are saved as templates in Odoo.","product":"This includes all variants from products and products which do not have variants. Odoo creates a variant for each product. Badge indicators: P = Parent Product, V = Variant Product."},"badges":{"parent":"Parent Product","variant":"Variant Product"},"table":{"id":"Shopware Product Id","odoo-id":"Odoo Product Id","name":"Product Name"},"labels":{"id":"Product variant identifier in Shopware","odoo-id":"Corresponding product variant ID in Odoo system","name":"Display name with variant options (P = Parent, V = Variant)"}},"template":{"title":"Template Table","table":{"id":"Shopware Product Id","odoo-id":"Odoo Template Id","name":"Product Name"},"labels":{"id":"Main product identifier in Shopware","odoo-id":"Corresponding template ID in Odoo system","name":"Display name of the main product"}},"property":{"title":"Property Table","label":"Property","note":"Shopware properties are synchronized in Odoo as attributes.","table":{"id":"Shopware Property Id","odoo-id":"Odoo Attribute Id","name":"Property Name"}},"customer":{"title":"Customer Table","table":{"id":"Shopware Customer Id","odoo-id":"Odoo Customer Id","name":"Customer Name"}},"order":{"title":"Order Table","table":{"id":"Shopware Order Id","odoo-id":"Odoo Order Id","name":"Order Number"}},"sales-channel":{"title":"Sales Channel Table","table":{"id":"Shopware Sales Channel Id","odoo-id":"Odoo Company Id","name":"Sales Channel Name"}},"attribute-value":{"title":"Property Value Table","label":"Property Value","note":"Shopware property values are synchronized in Odoo as attribute values.","table":{"id":"Shopware Property Value Id","odoo-id":"Odoo Attribute Value Id","name":"Property Value Name"}},"customer-address":{"title":"Customer Address Table","label":"Customer Address","note":"Customer addresses are synchronized as child contacts in Odoo, with the Shopware customer serving as the parent contact.","table":{"id":"Shopware Address Id","odoo-id":"Odoo Contact Id","name":"Address Name"}},"delivery":{"title":"Delivery Table","table":{"id":"Shopware Delivery Id","odoo-id":"Odoo Delivery Id","name":"Order Number"}},"transaction":{"title":"Transaction Table","table":{"id":"Shopware Transaction Id","odoo-id":"Odoo Payment Id","name":"Order Number"}},"api-verify-button":{"title":"API Test","buttonLabel":"Verify API Connection","success":"Connection tested with success","error":"Connection could not be established. Please check the access data","error_400":"Something went wrong. Please check the access data","error_401":"Authentication failed please check cradintials.","error_422":"This version of odoo is not supported, supports only odoo 17 and 18.","error_424":"Some modules are not installed, please install","error_500":"Server error"},"synchronize-button":{"title":"Sync","description":"This will take some time to process the queue, so please do it when your site usage is minimal for some time.<br/>The estimated time is 1000 products per Hour*.","success":"All entities are queued for synchronization","error_1":"Please setup the odoo configuration, then try again","error_2":"Something went wrong with the synchronization, please try again","error_3":"You already have done or in the process of initial syncing","confirmation":{"title":"Confirm Synchronization","message":"Are you sure you want to start the synchronization process? Once started, this action cannot be undone and will synchronize all data between Shopware and Odoo.","warning":"This process may take considerable time and should be performed during low traffic periods.","confirm":"Yes, Start Synchronization","cancel":"Cancel"}},"progressBar":{"text":"Synchronization Progress","idle":"Ready","syncing":"Syncing","completed":"Completed","error":"Error","remaining":"remaining"},"automation-modal":{"title":"Automation Configuration","description":"Configure automation settings for two-way synchronization between Shopware and Odoo.","toggleLabel":"Enable Two-Way Sync","toggleHelpText":"When enabled, changes in Odoo will automatically sync back to Shopware","saveButton":"Save","success":"Automation configuration saved successfully","error":"Failed to create automation rules. Please check your Odoo connection and try again."},"dashboard":{"refresh":"Refresh","title":"Synchronization Dashboard","totalSynced":"Total Synced","entityTypes":"Entity Types","entitySynced":"Entity Synced","syncStatus":"Sync Status","overallProgress":"Overall Progress","synced":"Synced","pending":"Pending","total":"Total","records":"records","gauge":{"title":"Sync Rate","recordsPerHour":"Records/Hour","recordsPerMinute":"Records/Minute","hour":"hour","minute":"minute","lastHour":"Last Hour","lastMinute":"Last Minute","switchToHour":"Switch to Hour View","switchToMinute":"Switch to Minute View"},"entityNames":{"salesChannels":"Sales Channels","categories":"Categories","productVariants":"Product Variants","products":"Products","propertyGroups":"Property Groups","propertyGroupOptions":"Property Group Options","customers":"Customers","customerAddresses":"Customer Addresses","orders":"Orders","deliveries":"Deliveries","transactions":"Transactions"},"tooltips":{"complete":"All records are synchronized","partial":"Some records are synchronized","pending":"No records are synchronized yet","noData":"No data available"}}}}');let{Module:C}=Shopware;C.register("brainst-odoo-pro",{type:"plugin",name:"brainst-odoo-pro.title",title:"brainst-odoo-pro.title",description:"brainst-odoo-pro.description",color:"#9AA8B5",icon:"regular-cog",snippets:{"de-DE":S,"en-GB":k},routes:{list:{component:"brainst-odoo-pro-list",path:"list",meta:{parentPath:"sw.settings.index.plugins"}},category:{component:"brainst-odoo-pro-category",path:"category",meta:{parentPath:"brainst.odoo.pro.list"}},product:{component:"brainst-odoo-pro-product",path:"product",meta:{parentPath:"brainst.odoo.pro.list"}},property:{component:"brainst-odoo-pro-property",path:"property",meta:{parentPath:"brainst.odoo.pro.list"}},customer:{component:"brainst-odoo-pro-customer",path:"customer",meta:{parentPath:"brainst.odoo.pro.list"}},order:{component:"brainst-odoo-pro-order",path:"order",meta:{parentPath:"brainst.odoo.pro.list"}},"sales-channel":{component:"brainst-odoo-pro-sales-channel",path:"sales-channel",meta:{parentPath:"brainst.odoo.pro.list"}},"attribute-value":{component:"brainst-odoo-pro-attribute-value",path:"attribute-value",meta:{parentPath:"brainst.odoo.pro.list"}},"customer-address":{component:"brainst-odoo-pro-customer-address",path:"customer-address",meta:{parentPath:"brainst.odoo.pro.list"}},delivery:{component:"brainst-odoo-pro-delivery",path:"delivery",meta:{parentPath:"brainst.odoo.pro.list"}},transaction:{component:"brainst-odoo-pro-transaction",path:"transaction",meta:{parentPath:"brainst.odoo.pro.list"}}},settingsItem:[{group:"plugins",to:"brainst.odoo.pro.list",iconComponent:"brainst-odoo-pro-icon",backgroundEnabled:!0,label:"brainst-odoo-pro.title"}],extensionEntryRoute:{extensionName:"BrainstOdooPro",route:"brainst.odoo.pro.list"}});let{Component:I,Mixin:$}=Shopware;I.register("brainst-odoo-pro-api-verify-button",{template:'<div class="sw-field">\n    <sw-button-process\n            :isLoading="isLoading"\n            :processSuccess="isVerifyDone"\n            variant="primary"\n            @process-finish="verifyFinish"\n            @click="verifyApi"\n    >{{ $tc(\'brainst-odoo-pro.api-verify-button.buttonLabel\') }}</sw-button-process>\n\n    \n    <brainst-odoo-pro-automation-modal\n        v-if="showAutomationModal"\n        :pluginConfig="pluginConfig"\n        @modal-close="onCloseAutomationModal"\n        @config-save="onConfigSave"\n    />\n</div>\n',inject:["brainstOdooProApiService"],mixins:[$.getByName("notification")],data(){return{isLoading:!1,isVerifyDone:!1,showAutomationModal:!1}},computed:{pluginConfig(){let e=this.$parent;for(;void 0===e.actualConfigData;)e=e.$parent;return e.actualConfigData.null}},methods:{verifyFinish(){this.isVerifyDone=!1},onCloseAutomationModal(){this.showAutomationModal=!1},onConfigSave(e){},verifyApi(){this.isLoading=!0,this.brainstOdooProApiService.verifyConfig(this.pluginConfig).then(e=>{this.pluginConfig["BrainstOdooPro.config.version"]=e.version,this.pluginConfig["BrainstOdooPro.config.uid"]=e.uid,this.createNotificationSuccess({title:this.$tc("brainst-odoo-pro.api-verify-button.title"),message:this.$tc("brainst-odoo-pro.api-verify-button.success")}),this.isVerifyDone=!0,this.showAutomationModal=!0}).catch(e=>{let t=e.response.status?"_"+e.response.status:"",o=this.$tc("brainst-odoo-pro.api-verify-button.error"+t);("_424"===t||"_500"===t)&&(o+=": "+e.response.data.message),this.createNotificationError({title:this.$tc("brainst-odoo-pro.api-verify-button.title"),message:o})}).finally(()=>{this.isLoading=!1})}}}),u(192);let{Component:P,Mixin:O}=Shopware;P.register("brainst-odoo-pro-automation-modal",{template:'{% block brainst_odoo_pro_automation_modal %}\n    <sw-modal\n            class="brainst-odoo-pro-automation-modal"\n            :title="$tc(\'brainst-odoo-pro.automation-modal.title\')"\n            variant="small"\n            @modal-close="onCloseModal"\n    >\n        {% block brainst_odoo_pro_automation_modal_body %}\n            {% block brainst_odoo_pro_automation_modal_content %}\n                <div class="brainst-odoo-pro-automation-modal__content">\n                    {% block brainst_odoo_pro_automation_modal_description %}\n                        <p class="brainst-odoo-pro-automation-modal__description">\n                            {{ $tc(\'brainst-odoo-pro.automation-modal.description\') }}\n                        </p>\n                    {% endblock %}\n\n                    {% block brainst_odoo_pro_automation_modal_toggle %}\n                        <div class="brainst-odoo-pro-automation-modal__toggle-container">\n                            <sw-switch-field\n                                    class="brainst-odoo-pro-automation-modal__toggle"\n                                    :label="$tc(\'brainst-odoo-pro.automation-modal.toggleLabel\')"\n                                    v-model:value="enableTwoWaySync"\n                                    :helpText="$tc(\'brainst-odoo-pro.automation-modal.toggleHelpText\')"\n                            />\n                        </div>\n                    {% endblock %}\n                </div>\n            {% endblock %}\n        {% endblock %}\n\n        {% block brainst_odoo_pro_automation_modal_footer %}\n            <template #modal-footer>\n                {% block brainst_odoo_pro_automation_modal_footer_cancel %}\n                    <sw-button\n                            size="small"\n                            @click="onCloseModal"\n                            :disabled="isLoading"\n                    >\n                        {{ $tc(\'global.default.cancel\') }}\n                    </sw-button>\n                {% endblock %}\n\n                {% block brainst_odoo_pro_automation_modal_footer_save %}\n                    <sw-button-process\n                            class="brainst-odoo-pro-automation-modal__save-button"\n                            :isLoading="isLoading"\n                            variant="primary"\n                            size="small"\n                            @click="onSave"\n                    >\n                        {{ $tc(\'brainst-odoo-pro.automation-modal.saveButton\') }}\n                    </sw-button-process>\n                {% endblock %}\n            </template>\n        {% endblock %}\n    </sw-modal>\n{% endblock %}\n',inject:["brainstOdooProApiService","systemConfigApiService"],mixins:[O.getByName("notification")],data(){return{isLoading:!1,enableTwoWaySync:!1}},props:{pluginConfig:{type:Object,required:!0}},methods:{onCloseModal(){this.$emit("modal-close")},savePluginConfig(){let e={};return Object.keys(this.pluginConfig).forEach(t=>{t.startsWith("BrainstOdooPro.config.")&&(e[t]=this.pluginConfig[t])}),this.systemConfigApiService.saveValues(e)},onSave(){this.isLoading=!0,this.pluginConfig["BrainstOdooPro.config.enableTwoWaySync"]=this.enableTwoWaySync,this.brainstOdooProApiService.removeAutomation().then(e=>this.savePluginConfig()).then(e=>this.enableTwoWaySync?this.brainstOdooProApiService.createAutomation():(this.createNotificationSuccess({title:this.$tc("brainst-odoo-pro.automation-modal.title"),message:this.$tc("brainst-odoo-pro.automation-modal.success")}),this.onCloseModal(),Promise.resolve())).then(e=>{this.enableTwoWaySync&&e&&(this.createNotificationSuccess({title:this.$tc("brainst-odoo-pro.automation-modal.title"),message:this.$tc("brainst-odoo-pro.automation-modal.success")}),this.onCloseModal())}).catch(e=>{let t=this.$tc("brainst-odoo-pro.automation-modal.error");e.response&&e.response.data&&e.response.data.message?t=e.response.data.message:e.message&&(t=e.message),this.createNotificationError({title:this.$tc("brainst-odoo-pro.automation-modal.title"),message:t})}).finally(()=>{this.isLoading=!1})}}})}()})();