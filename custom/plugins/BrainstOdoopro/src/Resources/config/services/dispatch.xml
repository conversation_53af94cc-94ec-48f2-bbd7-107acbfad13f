<?xml version="1.0" ?>
<container xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
           xmlns="http://symfony.com/schema/dic/services"
           xsi:schemaLocation="http://symfony.com/schema/dic/services http://symfony.com/schema/dic/services/services-1.0.xsd">
    <services>
        <service id="brainst.odoo_pro.dispatch" class="Brainst\OdooPro\Service\ShopwareToOdoo\Dispatch\DispatchService">
            <argument type="service" id="messenger.bus.shopware"/>
            <argument type="service" id="Brainst\OdooPro\Util\Logger"/>
        </service>
    </services>
</container>
