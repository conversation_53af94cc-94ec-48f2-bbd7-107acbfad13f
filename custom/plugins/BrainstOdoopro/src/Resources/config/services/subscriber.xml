<?xml version="1.0" ?>
<container xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
           xmlns="http://symfony.com/schema/dic/services"
           xsi:schemaLocation="http://symfony.com/schema/dic/services http://symfony.com/schema/dic/services/services-1.0.xsd">
    <services>
        <service id="Brainst\OdooPro\Subscriber\OdooSubscriber">
            <argument type="service" id="request_stack"/>
            <argument id="brainst.odoo_pro.dispatch" type="service" />
            <tag name="kernel.event_subscriber"/>
        </service>
    </services>
</container>