<?php declare(strict_types=1);

namespace Brainst\OdooPro\Helper;

use DateTime;
use SimpleXMLElement;

/**
 * Class XmlrpcHelper
 * @package BrainstOdoopro
 * <AUTHOR>
 * @link www.brainstreamtechnolabs.com
 */
class XmlrpcHelper
{
    final const ENCODING = 'utf-8';

    /**
     * Encode the request for odoo
     * @param string $method
     * @param array<float|DateTime|int|bool|array<mixed>|string|null> $params
     * @return bool|string
     */
    public static function encodeRequest(string $method, array $params): bool|string
    {
        $xml = new SimpleXMLElement("<?xml version='1.0' encoding='UTF-8'?><methodCall></methodCall>");
        $xml->addChild("methodName", $method);
        $paramsElement = $xml->addChild("params");

        foreach ($params as $param) {
            $paramElement = $paramsElement->addChild("param");
            self::addXmlValue($paramElement, $param);
        }

        return $xml->asXML();
    }

    /**
     * Set all data to parent xml element from given parameter value for odoo
     * @param SimpleXMLElement $parent
     * @param float|DateTime|int|bool|array<mixed>|string|null $value
     * @return void
     */
    private static function addXmlValue(SimpleXMLElement $parent, float|DateTime|int|bool|array|string|null $value): void
    {
        if ($value instanceof DateTime) {
            $parent->addChild("value")->addChild("dateTime.iso8601", $value->format('Ymd\TH:i:s'));
        } elseif (is_array($value) && !self::isAssoc($value)) {
            // Handle list (indexed array)
            $arrayElement = $parent->addChild("value")->addChild("array")->addChild("data");
            foreach ($value as $subValue) {
                self::addXmlValue($arrayElement->addChild("value"), $subValue);
            }
        } elseif (is_array($value)) {
            // Handle struct (associative array or object)
            $structElement = $parent->addChild("value")->addChild("struct");
            foreach ((array)$value as $key => $subValue) {
                $member = $structElement->addChild("member");
                $member->addChild("name", $key);
                self::addXmlValue($member, $subValue);
            }
        } elseif (is_int($value)) {
            $parent->addChild("value")->addChild("int", (string)$value);
        } elseif (is_bool($value)) {
            $parent->addChild("value")->addChild("boolean", $value ? "1" : "0");
        } elseif (is_float($value)) {
            $parent->addChild("value")->addChild("double", (string)$value);
        } elseif ($value === null) {
            $parent->addChild("value");
        } else {
            $parent->addChild("value")->addChild("string", htmlspecialchars($value, ENT_QUOTES, 'UTF-8'));
        }
    }

    /**
     * Check is array associative or not
     * @param array<mixed>|array<string,mixed> $array
     * @return bool
     */
    private static function isAssoc(array $array): bool
    {
        return array_keys($array) !== range(0, count($array) - 1);
    }

    /**
     * Decode the odoo response from xml
     * @param string $xml
     * @return float|DateTime|int|bool|array<mixed>|string|null
     */
    public static function decode(string $xml): float|DateTime|int|bool|array|string|null
    {
        $xml = trim($xml);

        if (stristr($xml, '<?xml')) {
            preg_match('/encoding=["\'](.+?)["\']/', $xml, $matches);
            $xml_encoding = $matches[1] ?? 'UTF-8'; // Default to UTF-8
            if (strcasecmp($xml_encoding, self::ENCODING) !== 0) {
                $xml = iconv($xml_encoding, self::ENCODING . '//TRANSLIT', $xml);
            }
        }

        $parsedXml = simplexml_load_string($xml, "SimpleXMLElement", LIBXML_NOCDATA);

        return XmlrpcHelper::parseValue($parsedXml->params->param->value ?? $parsedXml->fault->value);
    }

    /**
     * Decode the odoo response from xml for the response content value
     * @param SimpleXMLElement|null $value
     * @return float|DateTime|int|bool|array<mixed>|string|null
     */
    public static function parseValue(?SimpleXMLElement $value): float|DateTime|int|bool|array|string|null
    {
        if (isset($value->string)) {
            return (string)$value->string;
        }

        if (isset($value->int)) {
            return (int)$value->int;
        }

        if (isset($value->boolean)) {
            return (string)$value->boolean === '1';
        }

        if (isset($value->double)) {
            return (float)$value->double;
        }

        if (isset($value->dateTime_iso8601)) {
            return DateTime::createFromFormat('Ymd\TH:i:s', (string)$value->dateTime_iso8601);
        }

        if (isset($value->datetime)) {
            return DateTime::createFromFormat('Ymd\TH:i:s', (string)$value->datetime);
        }

        if (isset($value->array)) {
            return self::parseArray($value->array);
        }

        if (isset($value->struct)) {
            return self::parseStruct($value->struct);
        }

        return null;
    }

    /**
     * Get values of the array type xml element
     * @param SimpleXMLElement $array
     * @return array<mixed>
     */
    public static function parseArray(SimpleXMLElement $array): array
    {
        $values = [];
        foreach ($array->data->value ?? [] as $item) {
            $values[] = self::parseValue($item);
        }
        return $values;
    }

    /**
     * Get values of the associative array type xml element
     * @param SimpleXMLElement $struct
     * @return array<string, mixed>
     */
    public static function parseStruct(SimpleXMLElement $struct): array
    {
        $result = [];
        foreach ($struct->member as $member) {
            $name = (string)$member->name;
            $result[$name] = self::parseValue($member->value);
        }
        return $result;
    }

    /**
     * Check if odoo xml response has fault
     * @param float|DateTime|int|bool|array<mixed>|string|null $value
     * @return bool
     */
    public static function isFault(float|DateTime|int|bool|array|string|null $value): bool
    {
        if (!is_array($value) && !is_object($value)) {
            return false;
        }

        if (is_object($value)) {
            $value = (array)$value;
        }

        return isset($value['faultCode']) && isset($value['faultString']) &&
            is_int($value['faultCode']) && is_string($value['faultString']);
    }
}