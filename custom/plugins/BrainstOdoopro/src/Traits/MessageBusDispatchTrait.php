<?php declare(strict_types=1);

namespace Brainst\OdooPro\Traits;

use Shopware\Core\Framework\MessageQueue\AsyncMessageInterface;
use Symfony\Component\Messenger\Exception\ExceptionInterface;
use Symfony\Component\Messenger\MessageBusInterface;
use Symfony\Component\Messenger\Stamp\DelayStamp;

/**
 * Class MessageBusDispatchTrait
 * @package BrainstOdooPro
 * <AUTHOR>
 * @link www.brainstreamtechnolabs.com
 */
trait MessageBusDispatchTrait
{
    protected MessageBusInterface $messageBus;

    /**
     * Set message bus for message queue
     *
     * @param MessageBusInterface $messageBus
     * @return void
     */
    protected function setMessageBus(MessageBusInterface $messageBus): void
    {
        $this->messageBus = $messageBus;
    }

    /**
     * Add data to message queue
     *
     * @param $message
     * @return void
     * @throws ExceptionInterface
     */
    protected function messageDispatch($message): void
    {
        $this->messageBus->dispatch(
            $message
        );
    }

    /**
     * Add delay to message queue
     *
     * @param AsyncMessageInterface $message
     * @param int $delay in secounds
     * @return void
     * @throws ExceptionInterface
     */
    protected function delayMessage(AsyncMessageInterface $message, int $delay = 0): void
    {
        $this->messageBus->dispatch(
            $message,
            [new DelayStamp($delay * 1000)]
        );
    }
}