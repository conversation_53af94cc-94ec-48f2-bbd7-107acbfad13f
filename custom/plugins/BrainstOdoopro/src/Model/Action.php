<?php

declare(strict_types=1);

namespace Brainst\OdooPro\Model;

use Brainst\Odoo\Model\ModelInterface;

/**
 * Class Action
 * @package BrainstOdoo
 * <AUTHOR>
 * @link www.brainstreamtechnolabs.com
 */
final class Action implements ModelInterface
{
    private const ODOO_ACTION_CREATE = 'create';
    private const ODOO_ACTION_RETURN = 'return';
    private const ODOO_ACTION_CANCEL = 'cancel';
    private const ODOO_ACTION_VALIDATE = 'validate';
    private const ODOO_ACTION_PARTIAL_SHIP = 'partial_ship';
    private const ODOO_ACTION_PARTIAL_RETURN = 'partial_return';

    private const ODOO_ACTION_POST = 'post';
    private const ODOO_ACTION_PAID = 'paid';
    private const ODOO_ACTION_UNCONFIRMED = 'unconfirmed';
    private const ODOO_ACTION_CHARGEBACK = 'chargeback';
    private const ODOO_ACTION_FAIL = 'fail';
    private const ODOO_ACTION_PARTIAL = 'partial';
    private const ODOO_ACTION_REFUND = 'refund';


    private function __construct(private readonly string $type)
    {
    }

    public function type(): string
    {
        return $this->type;
    }

    public function equals(ModelInterface $model): bool
    {
        return $model->type() === $this->type;
    }

    public static function load(string $type): self
    {
        return new self($type);
    }

    public static function create(): self
    {
        return new self(self::ODOO_ACTION_CREATE);
    }

    public static function return(): self
    {
        return new self(self::ODOO_ACTION_RETURN);
    }

    public static function validate(): self
    {
        return new self(self::ODOO_ACTION_VALIDATE);
    }

    public static function cancel(): self
    {
        return new self(self::ODOO_ACTION_CANCEL);
    }

    public static function partialShip(): self
    {
        return new self(self::ODOO_ACTION_PARTIAL_SHIP);
    }

    public static function partialReturn(): self
    {
        return new self(self::ODOO_ACTION_PARTIAL_RETURN);
    }

    public static function post(): self
    {
        return new self(self::ODOO_ACTION_POST);
    }

    public static function paid(): self
    {
        return new self(self::ODOO_ACTION_PAID);
    }

    public static function unconfirmed(): self
    {
        return new self(self::ODOO_ACTION_UNCONFIRMED);
    }

    public static function chargeback(): self
    {
        return new self(self::ODOO_ACTION_CHARGEBACK);
    }

    public static function fail(): self
    {
        return new self(self::ODOO_ACTION_FAIL);
    }

    public static function partial(): self
    {
        return new self(self::ODOO_ACTION_PARTIAL);
    }

    public static function refund(): self
    {
        return new self(self::ODOO_ACTION_REFUND);
    }

}
