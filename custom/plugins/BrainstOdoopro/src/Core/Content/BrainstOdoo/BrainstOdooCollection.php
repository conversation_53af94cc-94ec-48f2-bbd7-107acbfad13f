<?php declare(strict_types=1);

namespace Brainst\OdooPro\Core\Content\BrainstOdoo;

use Shopware\Core\Framework\DataAbstractionLayer\Entity;
use Shopware\Core\Framework\DataAbstractionLayer\EntityCollection;

/**
 * Class BrainstOdooCollection
 * @package BrainstOdooPro
 * <AUTHOR>
 * @link www.brainstreamtechnolabs.com
 *
 * @extends EntityCollection<BrainstOdooEntity>
 * @method void add(BrainstOdooEntity $entity)
 * @method void set(string $key, BrainstOdooEntity $entity)
 * @method BrainstOdooEntity[] getIterator()
 * @method BrainstOdooEntity[] getElements()
 * @method BrainstOdooEntity|null get(string $key)
 * @method BrainstOdooEntity|null first()
 * @method BrainstOdooEntity|null last()
 */
class BrainstOdooCollection extends EntityCollection
{
    protected function getExpectedClass(): string
    {
        return BrainstOdooEntity::class;
    }

    public function filterByProperties(string $property, array $value, bool $contain = true): static
    {
        return $this->filter(
            static function (Entity $struct) use ($contain, $property, $value) {
                $condition = in_array($struct->get($property), $value, true);
                return $contain ? $condition : !$condition;
            }
        );
    }

    public function getRecordIds(): array
    {
        return $this->fmap(fn (BrainstOdooEntity $odooEntity) => $odooEntity->getRecordId());
    }

}
