<?php declare(strict_types=1);

namespace Brainst\OdooPro\MessageQueue\Message;

use Shopware\Core\Framework\MessageQueue\AsyncMessageInterface;

/**
 * Class OdooDataMessage
 * @package BrainstOdooPro
 * <AUTHOR>
 * @link www.brainstreamtechnolabs.com
 */
class OdooDataMessage implements AsyncMessageInterface
{
    /**
     * @param string $entityName
     * @param array<string> $recordIds
     * @param string $operation
     * @param bool $initialSync
     * @param array $payload
     */
    public function __construct(
        private readonly string $entityName,
        private readonly array  $recordIds,
        private readonly string $operation,
        private readonly bool   $initialSync,
        private readonly array  $payload
    )
    {
    }

    /**
     * Create Object statically
     *
     * @param string $entityName
     * @param array<string> $recordIds
     * @param string $operation
     * @param bool $initialSync
     * @param array $payload
     * @return OdooDataMessage
     */
    public static function init(
        string $entityName,
        array  $recordIds,
        string $operation,
        bool   $initialSync = false,
        array  $payload = []
    ): OdooDataMessage
    {
        return new self($entityName, $recordIds, $operation, $initialSync, $payload);
    }

    /**
     * @return string
     */
    public function getEntityName(): string
    {
        return $this->entityName;
    }

    /**
     * @return array<string>
     */
    public function getRecordIds(): array
    {
        return $this->recordIds;
    }

    /**
     * @return string
     */
    public function getOperation(): string
    {
        return $this->operation;
    }

    /**
     * @return bool
     */
    public function isInitialSync(): bool
    {
        return $this->initialSync;
    }

    /**
     * @return array
     */
    public function getPayload(): array
    {
        return $this->payload;
    }
}