<?php declare(strict_types=1);

namespace Brainst\OdooPro\Service\ShopwareToOdoo\Sync\State;

use Brainst\OdooPro\Core\Content\BrainstOdoo\BrainstOdooMudule;
use Brainst\OdooPro\Model\Action;
use Brainst\OdooPro\Model\State;
use Brainst\OdooPro\Service\OdooMappingService;
use DateTimeImmutable;
use Brainst\OdooPro\Service\OdooService;
use Shopware\Core\Checkout\Order\Aggregate\OrderTransaction\OrderTransactionStates;
use Shopware\Core\Checkout\Order\OrderEntity;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Criteria;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Filter\EqualsAnyFilter;

/**
 * Class TransactionSyncService
 * @package BrainstOdooPro
 * <AUTHOR>
 * @link www.brainstreamtechnolabs.com
 */
class TransactionSyncService
{
    public function __construct(
        private readonly OdooService        $odooService,
        private readonly OdooMappingService $mappingService,
    )
    {
    }

    /**
     * Delete odoo order transaction
     *
     * @param OrderEntity $orderData
     * @param $odooRecord
     * @return void
     */
    public function update(OrderEntity $orderData, $odooRecord): void
    {
        $companyId = $odooRecord['company_id'][0];
        $recordId = $orderData->getId();
        $odooVersion = $this->odooService->getConfig('version');

        $shopwarePaymentStatus = $orderData->getTransactions()->first()->getStateMachineState()->getTechnicalName();
        $odooPaymentAction = $this->getOdooPaymentAction($shopwarePaymentStatus);
        $mapping = $this->mappingService->findRecord(BrainstOdooMudule::MODULE_TRANSACTION, $recordId);
        $odooId = $this->mappingService->findRecord(BrainstOdooMudule::MODULE_TRANSACTION, $orderData->getId())?->getOdooId() ?? 0;

        $paymentId = false;

        $criteria = new Criteria();
        $criteria->addFilter(new EqualsAnyFilter('odooCompanyId', [$companyId]));

        $customFields = $this->odooService->getConfig('companySettings', $orderData->getSalesChannelId());

        $invoiceRecs = $this->odooService->execute_kw('account.move', 'search_read',
            [[['id', '=', $odooId]], ['invoice_payments_widget', 'state', 'payment_state', 'name', 'amount_total', 'matched_payment_ids']]);

        if ($odooVersion === 17 && $invoiceRecs && $content = $invoiceRecs[0]['invoice_payments_widget']['content']) {
            $paymentId = $content[0]['account_payment_id'];
        } elseif ($invoiceRecs && isset($invoiceRecs[0]['matched_payment_ids'][0])) {
            $paymentId = $invoiceRecs[0]['matched_payment_ids'][0];
        }

        if (empty($invoiceRecs) || Action::create()->equals($odooPaymentAction)) {
            if ($paymentId) {
                $this->deletePayment($paymentId);
            }
            if ($invoiceRecs) {
                $this->deleteInvoice($orderData->getId());
            }

            $odooId = $this->createInvoice($odooRecord, $companyId, $odooVersion);

            $invoiceRecs = $this->odooService->execute_kw('account.move', 'search_read',
                [[['id', '=', $odooId]], ['invoice_payments_widget', 'state', 'payment_state', 'name', 'amount_total', 'matched_payment_ids']]);
        }

        $state = !empty($invoiceRecs) ? $invoiceRecs[0]['state'] : 'draft';
        $invoiceState = State::load($state);

        if (Action::post()->equals($odooPaymentAction) ||
            Action::fail()->equals($odooPaymentAction)) {
            if ($paymentId) {
                $this->deletePayment($paymentId);
            }
            if (!State::draft()->equals($invoiceState)) {
                $this->draftInvoice($odooId);
            }
            $this->postInvoice($odooId);
        }
        if (Action::unconfirmed()->equals($odooPaymentAction)) {
            if ($paymentId) {
                $this->deletePayment($paymentId);
            }
            if (State::cancel()->equals($invoiceState)) {
                $this->draftInvoice($odooId);
            }
            $this->postInvoice($odooId);
        }
        if (Action::partial()->equals($odooPaymentAction)) {
            if (State::cancel()->equals($invoiceState)) {
                $this->draftInvoice($odooId);
            }
            if (!State::posted()->equals($invoiceState)) {
                $this->postInvoice($odooId);
            }
            if (!$paymentId) {
                $paymentId = $this->payInvoice($odooId, $odooRecord, $companyId, $customFields, $invoiceRecs[0]);
            }
            if ($invoiceRecs[0]['payment_state'] !== 'in_payment') {
                $this->odooService->execute_kw('account.move', 'write', [
                    [$odooId],
                    ['payment_state' => 'in_payment']
                ]);
            }
        }

        if (Action::paid()->equals($odooPaymentAction)) {
            if (State::cancel()->equals($invoiceState)) {
                $this->draftInvoice($odooId);
            }
            if (!State::posted()->equals($invoiceState)) {
                $this->postInvoice($odooId);
            }
            if (!$paymentId) {
                $paymentId = $this->payInvoice($odooId, $odooRecord, $companyId, $customFields, $invoiceRecs[0]);
            }

            if ($odooVersion === 'v17') {
                $this->odooService->execute_kw('account.move', 'write', [
                    [$odooId],
                    ['payment_state' => 'paid']
                ]);
            } else {
                $this->odooService->execute_kw('account.payment', 'action_validate', [$paymentId]);
            }
        }

        if (Action::cancel()->equals($odooPaymentAction)) {
            if ($paymentId) {
                $this->deletePayment($paymentId);
            }
            if (!State::draft()->equals($invoiceState)) {
                $this->draftInvoice($odooId);
            }
            $this->odooService->execute_kw('account.move', 'button_cancel',
                [$odooId]);
        }

        if (Action::refund()->equals($odooPaymentAction) || Action::chargeback()->equals($odooPaymentAction)) {
            if (State::cancel()->equals($invoiceState)) {
                $this->draftInvoice($odooId);
            }
            if (!State::posted()->equals($invoiceState)) {
                $this->postInvoice($odooId);
            }
            if (!$paymentId) {
                $paymentId = $this->payInvoice($odooId, $odooRecord, $companyId, $customFields, $invoiceRecs[0]);
            }
            if ($invoiceRecs[0]['payment_state'] !== 'paid') {
                $this->odooService->execute_kw('account.move', 'write', [
                    [$odooId],
                    ['payment_state' => 'paid']
                ]);
            }
            $this->refundInvoice($odooId, $companyId, $customFields);

            if (Action::chargeback()->equals($odooPaymentAction)) {
                $odooId = $this->createInvoice($odooRecord, $companyId, $odooVersion);
            } else {
                $odooId = 0;
            }
        }

        // Check and update invoice id in shopware
        $this->mappingService->updateMapping(BrainstOdooMudule::MODULE_TRANSACTION, $recordId, $odooId, $mapping?->getId());
    }

    /**
     * @param OrderEntity $orderData
     * @return void
     */
    public function deleteOdooRecord(OrderEntity $orderData): void
    {
        $odooId = $this->mappingService->findRecord(BrainstOdooMudule::MODULE_PRODUCT, $orderData->getId())?->getOdooId() ?? 0;

        $invoiceRecs = $this->odooService->execute_kw('account.move', 'search_read',
            [[['id', '=', $odooId]], ['invoice_payments_widget', 'state', 'payment_state', 'move_type']]);
        if ($invoiceRecs) {
            if ($invoiceRecs[0]['invoice_payments_widget']['content']) {
                $paymentId = $invoiceRecs[0]['invoice_payments_widget']['content'][0]['account_payment_id'];
                if ($paymentId) {
                    $this->deletePayment($paymentId);
                }
            }
            $this->deleteInvoice($orderData->getId());
        }
    }

    /**
     * Delete odoo payment record
     *
     * @param int $paymentId
     * @return void
     */
    private function deletePayment(int $paymentId): void
    {
        $this->odooService->execute_kw('account.payment', 'action_draft', [$paymentId]);
        $this->odooService->execute_kw('account.payment', 'unlink', [[$paymentId]]);
    }

    /**
     * Delete odoo invoice record
     *
     * @param string $recordId
     * @return void
     */
    private function deleteInvoice(string $recordId): void
    {
        $mapping = $this->mappingService->findRecord(BrainstOdooMudule::MODULE_TRANSACTION, $recordId);
        if ($mapping) {
            $this->draftInvoice($mapping->getOdooId());
            $this->deleteOdooInvoice($mapping->getOdooId());

            $this->mappingService->removeMapping($mapping->getId());
        }
    }

    private function deleteOdooInvoice(int $odooId): void
    {
        $this->odooService->execute_kw('account.move', 'write', [
            [$odooId],
            ['posted_before' => false, 'move_type' => 'entry']
        ]);
        $this->odooService->execute_kw('account.move', 'unlink', [[$odooId]]);
    }

    /**
     * Make invoice status to draft
     *
     * @param int $odooId
     * @return void
     */
    private function draftInvoice(int $odooId): void
    {
        $this->odooService->execute_kw('account.move', 'button_draft', [$odooId]);
    }

    /**
     * Make invoice status to draft
     *
     * @param int $odooId
     * @return void
     */
    private function postInvoice(int $odooId): void
    {
        $this->odooService->execute_kw('account.move', 'action_post', [$odooId]);
    }

    /**
     * Create Invoice for the odoo order
     *
     * @param $odooRecord
     * @param int $companyId
     * @param $odooVersion
     * @return int
     */
    private function createInvoice($odooRecord, int $companyId, $odooVersion): int
    {
        if ($odooVersion === 17) {
            $lineItemIds = $odooRecord['order_line'];
            $odooInvoiceLineItems = [];

            if (!empty($lineItemIds)) {
                // Fetch line items for invoice
                $lineItems = $this->odooService->execute_kw('sale.order.line', 'search_read',
                    [[['id', 'in', $lineItemIds]], ['name', 'product_id', 'product_uom_qty', 'price_unit', 'price_total']]);

                foreach ($lineItems as $lineItemData) {
                    $odooInvoiceLineItems[] =
                        [
                            0,
                            0,
                            [
                                'sale_line_ids' => [[6, 0, [$lineItemData['id']]]],
                                'product_id' => $lineItemData['product_id'][0],
                                'name' => $lineItemData['name'],
                                'quantity' => $lineItemData['product_uom_qty'],
                                'price_unit' => $lineItemData['price_unit'],
                                'price_total' => $lineItemData['price_total'],
                                'tax_ids' => false,
                            ]
                        ];
                }
            }

            $createInvoiceData = [
                'partner_id' => $odooRecord['partner_invoice_id'][0],
                "partner_shipping_id" => $odooRecord['partner_shipping_id'][0],
                'ref' => 'Order #' . $odooRecord['name'],
                'move_type' => 'out_invoice',
                'invoice_date' => date('Y-m-d'),
                'invoice_date_due' => (new DateTimeImmutable())->modify('30 days')->format('Y-m-d'),
                'amount_total' => $odooRecord['amount_to_invoice'],
                'invoice_line_ids' => $odooInvoiceLineItems,
                'company_id' => $companyId
            ];

            return $this->odooService->execute_kw('account.move', 'create', [$createInvoiceData]);
        } else {
            $paymentInv = $this->odooService->execute_kw('sale.advance.payment.inv', 'create',
                [
                    [
                        'advance_payment_method' => 'delivered',
                        'consolidated_billing' => true,
                        'sale_order_ids' => [[4, $odooRecord['id']]],
                        'amount' => $odooRecord['amount_to_invoice'], // 0
                        'fixed_amount' => $odooRecord['amount_to_invoice'], // 0
                    ]
                ]);
            $move = $this->odooService->execute_kw('sale.advance.payment.inv', 'create_invoices',
                [[$paymentInv]]);

            return $move['res_id'];
        }
    }

    /**
     * Do payment for odoo invoice
     *
     * @param int $odooId
     * @param  $odooRecord
     * @param int $companyId
     * @param array $customFields
     * @param  $invoiceRec
     * @return int
     */
    private function payInvoice(int $odooId, $odooRecord, int $companyId, array $customFields, $invoiceRec): int
    {
        $result = $this->odooService->execute_kw('account.move', 'action_register_payment',
            [$odooId], [
                'context' => [
                    "active_id" => $odooRecord['id'] ?? "",
                    "active_ids" => [$odooRecord['id']],
                    "active_model" => "sale.advance.payment.inv",
                    "allowed_company_ids" => [$companyId],
                    "default_invoice_origin" => $odooRecord['name'],
                    "default_invoice_payment_term_id" => null,
                    "default_move_type" => "out_invoice",
                    "default_partner_id" => $odooRecord['partner_invoice_id'][0],
                    "default_partner_shipping_id" => $odooRecord['partner_shipping_id'][0],
                    "dont_redirect_to_payments" => true
                ]
            ]);
        $resultLineIds = $result['context']['active_ids'] ?? [];
        $context = [
            'active_model' => 'account.move.line',
            'active_id' => $odooId,
            'active_ids' => $resultLineIds,
            'uid' => (int)$this->odooService->getConfig('uid'),
            'allowed_company_ids' => [$companyId],
            "dont_redirect_to_payments" => true
        ];

        $odooCurrencyId = $odooRecord['currency_id'][0];

        $totalAmount = $invoiceRec['amount_total'];
        $prePayment = $this->odooService->execute_kw('account.payment.register',
            'web_save', [
                [],
                [
                    "amount" => $totalAmount,
                    "communication" => $invoiceRec['name'],
                    "company_id" => $companyId,
                    "group_payment" => true,
                    "can_edit_wizard" => true,
                    "can_group_payments" => false,
                    "journal_id" => $customFields['cashJournalId'],
                    "partner_id" => $odooRecord['partner_id'][0],
                    "partner_type" => "customer",
                    "payment_date" => date('Y-m-d'),
                    "payment_difference_handling" => "open",
                    "payment_method_line_id" => $customFields['inboundPaymentMethodId'],
                    "payment_token_id" => false,
                    "payment_type" => "inbound",
                    "source_amount" => $totalAmount,
                    "source_amount_currency" => $totalAmount,
                    "currency_id" => $odooCurrencyId,
                    "source_currency_id" => $odooCurrencyId
                ]
            ],
            [
                'context' => $context,
                "specification" => ["amount" => [],]
            ]);
        $prePaymentId = $prePayment[0]['id'];
        $this->odooService->execute_kw('account.payment.register',
            'action_create_payments', [$prePaymentId], ['context' => $context]);

        $paymentIds = $this->odooService->execute_kw('account.move',
            'search_read', [
                [['id', '=', $odooId]],
                ['matched_payment_ids']
            ]);
        return $paymentIds[0]['matched_payment_ids'][0];
    }


    /**
     * Refund odoo invoice payment
     *
     * @param int $odooId
     * @param int $companyId
     * @param array $customFields
     * @return void
     */
    private function refundInvoice(int $odooId, int $companyId, array $customFields): void
    {
        // Step 1: Create a Credit Note (Refund)
        $this->odooService->execute_kw('account.move', 'action_reverse', [[$odooId]]);

        // Step 2: Create a Payment Web Save
        $context = [
            "active_id" => $odooId,
            "active_ids" => [$odooId],
            "active_model" => "account.move",
            "allowed_company_ids" => [$companyId],
        ];

        $result = $this->odooService->execute_kw('account.move.reversal', 'web_save', [
            [],
            [
                'move_ids' => [[4, $odooId]],
                'reason' => false,
                'journal_id' => $customFields['invoiceJournalId'],
                'date' => date('Y-m-d'),
            ]
        ], [
            'context' => $context,
            "specification" =>
                [
                    "journal_id" => ["field" => ["display_nam" => []]],
                    "move_ids" => [],
                    "reason" => [],
                ]
        ]);

        // Step 3: Initialize Refund Invoice
        $refundId = $result[0]['id'];
        // refund_moves and modify_moves(modify_moves will post reverse and also create new draft)
        $newRec = $this->odooService->execute_kw('account.move.reversal', 'modify_moves',
            [$refundId],
            ['context' => $context]
        );

        // Step 4: Delete newly created invoice draft
        $this->deleteOdooInvoice($newRec['res_id']);
    }

    /**
     * Get odoo payment action for update
     *
     * @param string $stateName
     * @return Action
     */
    private function getOdooPaymentAction(string $stateName): Action
    {
        return match ($stateName) {
            OrderTransactionStates::STATE_IN_PROGRESS, OrderTransactionStates::STATE_AUTHORIZED, OrderTransactionStates::STATE_REMINDED => Action::post(),
            OrderTransactionStates::STATE_PAID => Action::paid(),
            OrderTransactionStates::STATE_CANCELLED => Action::cancel(),
            OrderTransactionStates::STATE_UNCONFIRMED => Action::unconfirmed(),
            OrderTransactionStates::STATE_CHARGEBACK => Action::chargeback(),
            OrderTransactionStates::STATE_FAILED => Action::fail(),
            OrderTransactionStates::STATE_PARTIALLY_PAID, OrderTransactionStates::STATE_PARTIALLY_REFUNDED => Action::partial(),
            OrderTransactionStates::STATE_REFUNDED => Action::refund(),
            default => Action::create(),
        };
    }
}
