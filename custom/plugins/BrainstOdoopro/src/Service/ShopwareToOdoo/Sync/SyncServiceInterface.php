<?php declare(strict_types=1);

namespace Brainst\OdooPro\Service\ShopwareToOdoo\Sync;

use Shopware\Core\Framework\Context;

/**
 * Class SyncServiceInterface
 * @package BrainstOdoPro
 * <AUTHOR>
 * @link www.brainstreamtechnolabs.com
 */
interface SyncServiceInterface
{
    /**
     * Update, insert or delete based on provided operation
     *
     * @param string $recordId
     * @param string $operation
     * @param bool $initialSync
     */
    public function sync(string $recordId, string $operation, bool $initialSync = false): void;

    public function getOrCreateOdooId(?string $recordId): ?int;
}