<?php declare(strict_types=1);

namespace Brainst\OdooPro\Service\ShopwareToOdoo\SyncHelper;

use Brainst\OdooPro\Service\OdooService;
use Shopware\Core\System\Currency\CurrencyEntity;

/**
 * Class CurrencyService
 * @package BrainstOdooPro
 * <AUTHOR>
 * @link www.brainstreamtechnolabs.com
 */
class CurrencyService
{
    /**
     * Get/Generate price list id from odoo
     *
     * @param OdooService $odooService
     * @param CurrencyEntity $currencyEntity
     * @param int $companyId
     * @return int
     */
    public static function getOrUpdatePriceListId(OdooService $odooService, CurrencyEntity $currencyEntity, int $companyId): int
    {
        $currencyId = self::getOdooCurrencyId($odooService, $currencyEntity);
        $odooRecord = $odooService->execute_kw('product.pricelist', 'search',
            [[['name', '=', 'Shopware ' . $currencyEntity->getIsoCode()], ['currency_id', '=', $currencyId]]]);
        if (!$odooRecord) {
            $currencyData = [
                'active' => true,
                'name' => $currencyEntity->getIsoCode(),
                'company_id' => $companyId,
                "currency_id" => $currencyId,
            ];
            return $odooService->execute_kw('product.pricelist', 'create', [$currencyData]);
        }

        return $odooRecord[0];
    }

    /**
     * Get/Generate Currency id from odoo
     *
     * @param OdooService $odooService
     * @param CurrencyEntity $currencyEntity
     * @return int
     */
    public static function getOdooCurrencyId(OdooService $odooService, CurrencyEntity $currencyEntity): int
    {
        $odooRecord = $odooService->execute_kw('res.currency', 'search_read',
            [[['name', '=', $currencyEntity->getIsoCode()], ['active', 'in', [true, false]]], ['active']]);

        if (!$odooRecord) {
            $currencyData = [
                'active' => true,
                'name' => $currencyEntity->getIsoCode(),
                "position" => "after",
                "full_name" => $currencyEntity->getName(),
                "rounding" => $currencyEntity->getItemRounding()->getDecimals(),
                "symbol" => $currencyEntity->getSymbol(),
                "rate_ids" => self::getRate($currencyEntity)
            ];
            return $odooService->execute_kw('res.currency', 'create', [$currencyData]);
        }
        if (!isset($odooRecord[0]['active'])) {
            $odooService->execute_kw('product.attribute', 'write', [[$odooRecord[0]['id']], [
                'active' => true,
                "rate_ids" => self::getRate($currencyEntity)
            ]]);
        }
        return $odooRecord[0]['id'];
    }

    private static function getRate(CurrencyEntity $currencyEntity): array
    {
        return [
            [
                0,
                0,
                [
                    "company_id" => false,
                    "company_rate" => $currencyEntity->getFactor(),
                    "name" => date("Y-m-d"),
                    "rate" => $currencyEntity->getFactor()
                ]
            ]
        ];
    }
}
