<?php declare(strict_types=1);

namespace Brainst\OdooPro\Service\ShopwareToOdoo\Queue;

use Brainst\OdooPro\MessageQueue\Message\OdooDataMessage;
use Brainst\OdooPro\Traits\MessageBusDispatchTrait;
use Shopware\Core\Checkout\Order\OrderDefinition;
use Shopware\Core\Framework\DataAbstractionLayer\EntityWriteResult;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Criteria;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Filter\EqualsFilter;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Sorting\FieldSorting;
use Throwable;

/**
 * Class OrderSyncQueueService
 * @package BrainstOdooPro
 * <AUTHOR>
 * @link www.brainstreamtechnolabs.com
 */
class OrderSyncQueueService extends AbstractSyncQueue
{
    use MessageBusDispatchTrait;

    public final const LIMIT = 2;
    private string $syncFromDate = '';

    /**
     * @throws Throwable
     */
    public function __invoke(): void
    {
        $iterator = $this->getIterator(self::LIMIT);

        while ($searchResult = $iterator->fetch()) {
            $this->delayMessage(
                OdooDataMessage::init(
                    OrderDefinition::ENTITY_NAME,
                    $searchResult->getIds(),
                    EntityWriteResult::OPERATION_INSERT,
                    true)
            );
        }
    }

    public function setSyncFromDate(string $syncFromDate): void
    {
        $this->syncFromDate = $syncFromDate;
    }

    protected function getCriteria(int $limit): Criteria
    {
        $criteria = parent::getCriteria($limit);
        if ($this->syncFromDate) {
            $criteria->addFilter(new EqualsFilter('orderDate', $this->syncFromDate));
        }
        return $criteria;
    }
}
