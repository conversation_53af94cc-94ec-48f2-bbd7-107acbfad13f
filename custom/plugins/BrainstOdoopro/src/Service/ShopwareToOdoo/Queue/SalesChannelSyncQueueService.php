<?php declare(strict_types=1);

namespace Brainst\OdooPro\Service\ShopwareToOdoo\Queue;

use Brainst\OdooPro\MessageQueue\Message\OdooDataMessage;
use Brainst\OdooPro\Traits\MessageBusDispatchTrait;
use Shopware\Core\Framework\DataAbstractionLayer\EntityWriteResult;
use Shopware\Core\System\SalesChannel\SalesChannelDefinition;
use Throwable;

/**
 * Class SalesChannelSyncQueueService
 * @package BrainstOdooPro
 * <AUTHOR>
 * @link www.brainstreamtechnolabs.com
 */
class SalesChannelSyncQueueService extends AbstractSyncQueue
{
    use MessageBusDispatchTrait;

    public final const LIMIT = 2;

    /**
     * @throws Throwable
     */
    public function __invoke(): void
    {
        $iterator = $this->getIterator(self::LIMIT);

        while ($searchResult = $iterator->fetch()) {
            $this->delayMessage(
                OdooDataMessage::init(
                    SalesChannelDefinition::ENTITY_NAME,
                    $searchResult->getIds(),
                    EntityWriteResult::OPERATION_INSERT,
                    true)
            );
        }
    }
}
