<?php declare(strict_types=1);

namespace Brainst\OdooPro\Service\ShopwareToOdoo\Dispatch;

/**
 * Class DispatchInterface
 * @package BrainstOdooPro
 * <AUTHOR>
 * @link www.brainstreamtechnolabs.com
 */
interface DispatchInterface
{
    /**
     * Create batch and process all data for sync to odoo
     *
     * @param string $entityName
     * @param string $operation
     * @param array<string> $ids
     * @param array $payload
     * @return void
     */
    public function __invoke(string $entityName, string $operation, array $ids, array $payload = []): void;
}