<?php declare(strict_types=1);

namespace Brainst\OdooPro\Service\ShopwareToOdoo\Dispatch;

use Brainst\OdooPro\MessageQueue\Message\OdooDataMessage;
use Brainst\OdooPro\Service\ShopwareToOdoo\Queue\AbstractSyncQueue;
use Brainst\OdooPro\Traits\ErrorLoggerTrait;
use Brainst\OdooPro\Traits\MessageBusDispatchTrait;
use Psr\Log\LoggerInterface;
use Symfony\Component\Messenger\MessageBusInterface;
use Throwable;

/**
 * Class DispatchService
 * @package BrainstOdooPro
 * <AUTHOR>
 * @link www.brainstreamtechnolabs.com
 */
class DispatchService implements DispatchInterface
{
    use ErrorLoggerTrait, MessageBusDispatchTrait;

    public function __construct(
        protected MessageBusInterface $messageBus,
        private LoggerInterface       $logger
    )
    {
    }

    public function __invoke(string $entityName, string $operation, array $ids, array $payload = []): void
    {
        try {
            $searchResult = array_chunk($ids, AbstractSyncQueue::LIMIT);
            foreach ($searchResult as $chunkIds) {
                $payload = array_intersect_key($payload, array_flip($chunkIds));

                $this->delayMessage(OdooDataMessage::init($entityName, $ids, $operation, payload: $payload), 10);
            }
        } catch (Throwable $exception) {
            $this->logError($exception);
        }
    }
}