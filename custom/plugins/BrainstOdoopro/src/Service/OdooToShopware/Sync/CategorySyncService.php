<?php declare(strict_types=1);

namespace Brainst\OdooPro\Service\OdooToShopware\Sync;

use Brainst\OdooPro\Core\Content\BrainstOdoo\BrainstOdooEntity;
use Brainst\OdooPro\Core\Content\BrainstOdoo\BrainstOdooMudule;
use Brainst\OdooPro\MessageQueue\Handler\FromOdooDataHandler;
use Brainst\OdooPro\Service\OdooMappingService;
use Brainst\OdooPro\Service\OdooService;
use Shopware\Core\Content\Category\CategoryCollection;
use Shopware\Core\Framework\Api\Context\SystemSource;
use Shopware\Core\Framework\Context;
use Shopware\Core\Framework\DataAbstractionLayer\EntityRepository;
use Shopware\Core\Framework\Uuid\Uuid;

class CategorySyncService implements SyncServiceInterface
{
    /**
     * @param OdooMappingService $mappingService
     * @param EntityRepository<CategoryCollection> $categoryRepository
     * @param OdooService $odooService
     */
    public function __construct(
        private readonly OdooMappingService $mappingService,
        private readonly EntityRepository   $categoryRepository,
        private readonly OdooService        $odooService
    )
    {
    }

    public function sync(int $odooId, string $operation): void
    {
        $mapping = $this->mappingService->findByOdooId(BrainstOdooMudule::MODULE_CATEGORY, $odooId);

        if ($operation === FromOdooDataHandler::OPERATION_UPDATE) {
            $this->update($odooId, $mapping);
        }
    }

    /**
     * Update the category record
     * @param int $odooId
     * @param BrainstOdooEntity|null $mapping
     * @return string
     */
    private function update(int $odooId, ?BrainstOdooEntity $mapping): string
    {
        $client = $this->odooService->getClient();
        [$db, $uid, $password] = $this->odooService->getCredentials();
        $odooRecord = $client->execute_kw($db, $uid, $password, 'product.category', 'search_read', [[['id', '=', $odooId]]], ['fields' => ['id', 'name', 'parent_id']]);
        $odooRecord = $odooRecord[0];

        $parentId = $this->getParentId($odooRecord['parent_id'][0] ?? 0);
        $recordId = $mapping?->getRecordId() ?? Uuid::randomHex();
        $updateData = [
            'id' => $recordId,
            'name' => $odooRecord['name'],
            'parentId' => $parentId
        ];
        $this->categoryRepository->upsert([$updateData], Context::createCLIContext());
        $this->mappingService->updateMapping(BrainstOdooMudule::MODULE_CATEGORY, $recordId, $odooId, $mapping?->getId(), 'odoo');
        return $recordId;
    }

    /**
     * Get parent id of the given payload record
     * @param int $parentId
     * @return string|null
     */
    private function getParentId(int $parentId): ?string
    {
        if (!isset($parentId)) {
            return null;
        }
        $parentMapping = $this->mappingService->findByOdooId(BrainstOdooMudule::MODULE_CATEGORY, $parentId);

        if ($parentMapping?->getRecordId()) {
            return $parentMapping->getRecordId();
        }
        return $this->update($parentId, $parentMapping);
    }
}