<?php declare(strict_types=1);

namespace Brainst\OdooPro\Service\OdooToShopware\Sync;

use Brainst\OdooPro\Core\Content\BrainstOdoo\BrainstOdooEntity;
use Brainst\OdooPro\Core\Content\BrainstOdoo\BrainstOdooMudule;
use Brainst\OdooPro\MessageQueue\Handler\FromOdooDataHandler;
use Brainst\OdooPro\MessageQueue\Message\FromOdooDataMessage;
use Brainst\OdooPro\Service\OdooMappingService;
use Brainst\OdooPro\Service\OdooService;
use Brainst\OdooPro\Service\OdooWebhookService;
use Psr\Log\LoggerInterface;
use Shopware\Core\Checkout\Customer\CustomerCollection;
use Shopware\Core\Checkout\Payment\PaymentMethodCollection;
use Shopware\Core\Framework\Api\Context\SystemSource;
use Shopware\Core\Framework\Context;
use Shopware\Core\Framework\DataAbstractionLayer\EntityCollection;
use Shopware\Core\Framework\DataAbstractionLayer\EntityRepository;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Criteria;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Sorting\FieldSorting;
use Shopware\Core\Framework\Uuid\Uuid;
use Shopware\Core\System\Country\CountryCollection;
use Shopware\Core\System\NumberRange\ValueGenerator\NumberRangeValueGeneratorInterface;
use Shopware\Core\Checkout\Customer\Aggregate\CustomerGroup\CustomerGroupCollection;
use Shopware\Core\System\SalesChannel\SalesChannelCollection;
use Shopware\Core\System\Language\LanguageCollection;
use Shopware\Core\Checkout\Customer\Aggregate\CustomerAddress\CustomerAddressCollection;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Filter\EqualsFilter;

class CustomerSyncService implements SyncServiceInterface
{
    /**
     * @param OdooMappingService $mappingService
     * @param EntityRepository<CustomerCollection> $customerRepository
     * @param OdooService $odooService
     * @param NumberRangeValueGeneratorInterface $valueGenerator
     * @param EntityRepository<CustomerGroupCollection> $customerGroupRepository
     * @param EntityRepository<SalesChannelCollection> $salesChannelRepository
     * @param EntityRepository<PaymentMethodCollection> $paymentMethodRepository
     * @param EntityRepository<LanguageCollection> $languageRepository
     * @param EntityRepository<CustomerAddressCollection> $addressRepository
     * @param EntityRepository<CountryCollection> $countryRepository
     * @param LoggerInterface $logger
     * @param OdooWebhookService $odooWebhookService
     */
    public function __construct(
        private readonly OdooMappingService                 $mappingService,
        private readonly EntityRepository                   $customerRepository,
        private readonly OdooService                        $odooService,
        private readonly NumberRangeValueGeneratorInterface $valueGenerator,
        private readonly EntityRepository                   $customerGroupRepository,
        private readonly EntityRepository                   $salesChannelRepository,
        private readonly EntityRepository                   $paymentMethodRepository,
        private readonly EntityRepository                   $languageRepository,
        private readonly EntityRepository                   $addressRepository,
        private readonly EntityRepository                   $countryRepository,
        protected readonly LoggerInterface                  $logger,
        private readonly OdooWebhookService                 $odooWebhookService,
    )
    {
    }

    public function sync(int $odooId, string $operation): void
    {
        $mapping = $this->mappingService->findByOdooId(BrainstOdooMudule::MODULE_CUSTOMER, $odooId);
        if ($operation === FromOdooDataHandler::OPERATION_UPDATE) {
            $this->update($odooId, $mapping);
        }
    }

    /**
     * Update the customer record
     * @param int $odooId
     * @param BrainstOdooEntity|null $mapping
     * @return void
     */
    private function update(int $odooId, ?BrainstOdooEntity $mapping): void
    {
        $context = Context::createCLIContext();

        $client = $this->odooService->getClient();
        [$db, $uid, $password] = $this->odooService->getCredentials();
        $odooRecord = $this->fetchOdooContact($client, $db, $uid, $password, [$odooId]);
        $odooRecord = $odooRecord[0];

        if (isset($odooRecord['parent_id'], $odooRecord['parent_id'][0])) {
            $parentId = $odooRecord['parent_id'][0];
            $mapping = $this->mappingService->findByOdooId(BrainstOdooMudule::MODULE_CUSTOMER_ADDRESS, $odooId);

            $parentMapping = $this->mappingService->findByOdooId(BrainstOdooMudule::MODULE_CUSTOMER, $parentId);
            if (!$parentMapping) {
                $payload = [
                    '_model' => 'res.partner',
                    '_id' => $parentId,
                ];
                $this->odooWebhookService->handleWebhook($payload, FromOdooDataHandler::OPERATION_UPDATE);
                return;
            }
            $customerName = $this->getCustomerName($parentMapping->getRecordId(), $context);

            $this->createAddress($parentMapping->getRecordId(), $odooRecord, $customerName, $context, $mapping);
            return;
        }
        // @TODO:: Enble the code if don't want to create new customer from odoo to shopware
//        if (!$mapping) {
//            $this->logger->error("Customer not fount for update in shopware from odoo", $odooRecord ?? []);
//            return;
//        }

        if (!isset($odooRecord['email'], $odooRecord['name'])) {
            $this->logger->error("Email or Name are not available in odoo record", $odooRecord ?? []);
            return;
        }

        [$firstName, $lastName] = $this->generateName($odooRecord['name']);

        $recordId = $mapping?->getRecordId() ?: Uuid::randomHex();
        $addressMappings = $this->mappingService->findAll(
            BrainstOdooMudule::MODULE_CUSTOMER_ADDRESS,
            $odooRecord['child_ids'] ?? [],
            true);
        [$billingAddress, $shippingAddress, $allAddressess] = $this->getAddressIds($recordId, $odooRecord, $context, $addressMappings);

        $updateData = [
            'id' => $recordId,
            'firstName' => $firstName,
            'lastName' => $lastName,
            'email' => $odooRecord['email'],
            'title' => $odooRecord['function'] ?: null,
            'vatIds' => $odooRecord['vat'] ?: null
        ];
        if (is_array($billingAddress)) {
            $updateData['defaultBillingAddress'] = $billingAddress;
        } else {
            $updateData['defaultBillingAddressId'] = $billingAddress;
        }

        if (is_array($shippingAddress)) {
            $updateData['defaultShippingAddress'] = $shippingAddress;
        } else {
            $updateData['defaultShippingAddressId'] = $shippingAddress;
        }

        if (!$mapping) {
            // required
            $updateData['customerNumber'] = $this->valueGenerator->getValue('customer', $context, null);
            $updateData['password'] = '$2y$10$XFRhv2TdOz9GItRt6ZgHl.e/HpO5Mfea6zDNXI9Q8BasBRtWbqSTS'; // shopware
            $updateData['accountType'] = $odooRecord['company_type'] === 'company' ? 'business' : 'private';
            $updateData['groupId'] = $this->getCustomerGroupId($context);
            $updateData['salesChannelId'] = $this->getDefaultSalesChannelId($context);
            $updateData['defaultPaymentMethodId'] = $this->getDefaultPaymentMethodId($context);
            $updateData['languageId'] = $this->getDefaultLanguageId($context);
        }

        $this->customerRepository->upsert([$updateData], $context);
        $this->mappingService->updateMapping(BrainstOdooMudule::MODULE_CUSTOMER, $recordId, $odooId, $mapping?->getId(), 'odoo');

        $allAddressessIds = [];
        foreach ($allAddressess as $odooAddressId => $address) {
            if (is_array($address)) {
                /** @var null|BrainstOdooEntity $addressMapping */
                $addressMapping = $addressMappings->filterByProperty('odooId', $odooAddressId)->first();

                if ($address === $billingAddress || $address === $shippingAddress) {
                    $this->updateMappingTable($address['id'], $odooAddressId, $addressMapping?->getId());
                } else {
                    $this->mapAddress($address, $odooAddressId, $context, $addressMapping?->getId());
                }
                $allAddressessIds[] = $address['id'];
            } else {
                $allAddressessIds[] = $address;
            }
        }
        $this->cleanupObsoleteAddresses($recordId, $allAddressessIds, $context);
    }

    private function getCustomerName(string $id, Context $context): string
    {
        $criteria = new Criteria([$id]);
        /** @var CustomerCollection $customers */
        $customers = $this->customerRepository->search($criteria, $context);
        return $customers->first()->__toString();
    }

    private function getCustomerGroupId(Context $context): ?string
    {
        $criteria = new Criteria();
        $criteria->setLimit(1);
        $criteria->addSorting(new FieldSorting('registrationActive'));
        /** @var CustomerGroupCollection $groups */
        $groups = $this->customerGroupRepository->search($criteria, $context);
        $group = $groups->first();
        return $group ? $group->getId() : null;
    }

    private function getDefaultSalesChannelId(Context $context): ?string
    {
        $criteria = new Criteria();
        $criteria->setLimit(1);
        /** @var SalesChannelCollection $salesChannels */
        $salesChannels = $this->salesChannelRepository->search($criteria, $context);
        $salesChannel = $salesChannels->first();
        return $salesChannel ? $salesChannel->getId() : null;
    }

    private function getDefaultPaymentMethodId(Context $context): ?string
    {
        $criteria = new Criteria();
        $criteria->setLimit(1);
        /** @var PaymentMethodCollection $paymentMethods */
        $paymentMethods = $this->paymentMethodRepository->search($criteria, $context);
        $paymentMethod = $paymentMethods->first();
        return $paymentMethod ? $paymentMethod->getId() : null;
    }

    private function getDefaultLanguageId(Context $context): ?string
    {
        $criteria = new Criteria();
        $criteria->setLimit(1);
        /** @var LanguageCollection $languages */
        $languages = $this->languageRepository->search($criteria, $context);
        $language = $languages->first();
        return $language ? $language->getId() : null;
    }

    private function generateName(string $odooName): array
    {
        $reversedName = strrev($odooName);

        $reversedNameArray = explode(' ', $reversedName, 2);

        $lastName = strrev($reversedNameArray[0]);
        $firstName = strrev(($reversedNameArray[1] ?? $reversedNameArray[0]));

        return [$firstName, $lastName];
    }

    private function getAddressIds(string $customerId, array $odooRecord, Context $context, EntityCollection $addressMappings): array
    {
        $client = $this->odooService->getClient();
        [$db, $uid, $password] = $this->odooService->getCredentials();
        $addressIds = $odooRecord['child_ids'] ?? [];
        $allAddressess = [];
        $shopwareAddress = $shippingAddress = $billingAddress = null;

        if (!empty($addressIds)) {
            $addresses = $this->fetchOdooContact($client, $db, $uid, $password, $addressIds);

            foreach ($addresses as $address) {
                /** @var null|BrainstOdooEntity $addressMapping */
                $addressMapping = $addressMappings->filterByProperty('odooId', $address['id'])->first();
                $shopwareAddress = $addressMapping?->getRecordId() ?: $this->generateAddressData($customerId, $address, $odooRecord['name'], $context);
                $allAddressess[$address['id']] = $shopwareAddress;
                if ($address['type'] === 'invoice' || count($addressIds) === 1) {
                    $billingAddress = $shopwareAddress;
                }
                if ($address['type'] === 'delivery' || count($addressIds) === 1) {
                    $shippingAddress = $shopwareAddress;
                }
            }
        } else {
            /** @var null|BrainstOdooEntity $addressMapping */
            $addressMapping = $addressMappings->filterByProperty('odooId', $odooRecord['id'])->first();
            $shopwareAddress = $addressMapping?->getRecordId() ?: $this->generateAddressData($customerId, $odooRecord, $odooRecord['name'], $context);
            $allAddressess[] = $shopwareAddress;
        }
        $billingAddress ??= $shippingAddress ?? $shopwareAddress;
        $shippingAddress ??= $billingAddress;
        return [$billingAddress, $shippingAddress, $allAddressess];
    }

    private function fetchOdooContact($client, $db, $uid, $password, array $addressIds): array
    {
        return $client->execute_kw(
            $db, $uid, $password,
            'res.partner', 'search_read',
            [[['id', 'in', $addressIds]]],
            [
                'fields' => [
                    'id', 'name', 'type', 'street', 'street2', 'city', 'zip', 'country_id', 'state_id', 'phone',
                    'email', 'company_id', 'function', 'vat', 'child_ids', 'parent_id', 'company_type'
                ]
            ]
        );
    }

    private function createAddress(string $customerId, array $odooData, string $odooName, Context $context, ?BrainstOdooEntity $mapping): void
    {
        $addressData = $this->generateAddressData($customerId, $odooData, $odooName, $context, $mapping?->getRecordId());
        $this->mapAddress($addressData, $odooData['id'], $context, $mapping?->getId());
    }

    private function mapAddress(array $addressData, int $odooId, Context $context, ?string $mappingId = null): void
    {
        $this->addressRepository->upsert([$addressData], $context);
        $this->updateMappingTable($addressData['id'], $odooId, $mappingId);
    }

    private function updateMappingTable(string $addressId, int $odooId, ?string $mappingId = null): void
    {
        $this->mappingService->updateMapping(
            BrainstOdooMudule::MODULE_CUSTOMER_ADDRESS,
            $addressId,
            $odooId,
            $mappingId,
            'odoo'
        );
    }

    private function generateAddressData(string $customerId, array $odooData, string $odooName, Context $context, ?string $recordId = null): array
    {
        $shopwareAddressId = $recordId ?: Uuid::randomHex();
        $countryId = $this->resolveCountryId($odooData['country_id'] ?? null, $context);

        if (!$countryId) {
            $countryId = $this->getDefaultCountryIdFromSalesChannel($context);
        }

        [$firstName, $lastName] = $this->generateName($odooData['name'] ?? $odooName);

        return [
            'id' => $shopwareAddressId,
            'customerId' => $customerId,
            'firstName' => $firstName,
            'lastName' => $lastName,
            'street' => $odooData['street'] ?: 'not available',
            'city' => $odooData['city'] ?: 'not available',
            'zipcode' => $odooData['zip'] ?: null,
            'countryId' => $countryId,
            'phoneNumber' => $odooData['phone'] ?: null,
            'additionalAddressLine1' => $odooData['street2'] ?: null,
            'title' => $odooData['function'] ?: null,
        ];
    }

    private function resolveCountryId($odooCountry, Context $context): ?string
    {
        if (is_array($odooCountry)) {
            $odooCountryId = $odooCountry['id'] ?? $odooCountry[0] ?? null;
        } else {
            $odooCountryId = $odooCountry;
        }

        if (!$odooCountryId) {
            return null;
        }

        $client = $this->odooService->getClient();
        [$db, $uid, $password] = $this->odooService->getCredentials();

        $countryData = $client->execute_kw(
            $db, $uid, $password,
            'res.country', 'search_read',
            [[['id', '=', $odooCountryId]]],
            ['fields' => ['code']]
        );

        if (!empty($countryData[0]['code'])) {
            $criteria = (new Criteria())->addFilter(new EqualsFilter('iso', $countryData[0]['code']));
            $country = $this->countryRepository->search($criteria, $context)->first();
            return $country?->getId();
        }

        return null;
    }

    private function getDefaultCountryIdFromSalesChannel(Context $context): ?string
    {
        $criteria = (new Criteria())->setLimit(1)->addAssociation('country');
        $salesChannel = $this->salesChannelRepository->search($criteria, $context)->first();

        return $salesChannel?->getCountry()?->getId();
    }

    private function cleanupObsoleteAddresses(string $customerId, array $validIds, Context $context): void
    {
        $criteria = (new Criteria())->addFilter(new EqualsFilter('customerId', $customerId));
        $existingAddresses = $this->addressRepository->search($criteria, $context);

        $deleteIds = [];
        foreach ($existingAddresses as $address) {
            if (!in_array($address->getId(), $validIds, true)) {
                $deleteIds[] = ['id' => $address->getId()];
            }
        }
        if (!empty($deleteIds)) {
            $this->addressRepository->delete($deleteIds, $context);
        }
    }
} 