<?php declare(strict_types=1);

namespace Brainst\OdooPro\Controller\Api;

use Brainst\OdooPro\Service\ShopwareToOdoo\Queue\AbstractSyncQueue;
use Brainst\OdooPro\Traits\ErrorLoggerTrait;
use Psr\Log\LoggerInterface;
use Shopware\Core\System\SystemConfig\SystemConfigService;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use Throwable;

/**
 * Class SynchronizationController
 * @package BrainstOdooPro
 * <AUTHOR>
 * @link www.brainstreamtechnolabs.com
 */
#[Route(defaults: ['_routeScope' => ['administration']])]
class SynchronizationController
{
    use ErrorLoggerTrait;

    public function __construct(
        private readonly SystemConfigService $configService,
        protected LoggerInterface            $logger,
        private readonly AbstractSyncQueue   $categorySyncQueueService,
        private readonly AbstractSyncQueue   $salesChannelSyncQueueService,
        private readonly AbstractSyncQueue   $productSyncQueueService,
        private readonly AbstractSyncQueue   $propertySyncQueueService,
        private readonly AbstractSyncQueue   $customerSyncQueueService,
        private readonly AbstractSyncQueue   $orderSyncQueueService
    )
    {
    }

    /**
     * Start initial sync with odoo
     *
     * @param Request $request
     * @return JsonResponse
     */
    #[Route(path: '/api/brainst-odoo-pro/sync', name: 'api.brainst-odoo-pro.sync', defaults: ['XmlHttpRequest' => true], methods: ['POST'])]
    public function sync(Request $request): JsonResponse
    {
        $isValid = false;
        $code = 2;
        try {
            $initialSync = $this->configService->get('BrainstOdooPro.config.initialSync');
            $enableIntegration = $this->configService->get('BrainstOdooPro.config.enableIntegration');
            if (!$initialSync || !$enableIntegration) {
                $code = 3;
            } else {
                // Get the sync order from date from request
                $syncOrderFromDate = $request->get('syncOrderFromDate');

                $this->configService->set('BrainstOdooPro.config.initialSync', false);
                $this->syncAll($syncOrderFromDate);
                $code = 0;
                $isValid = true;
            }
        } catch (Throwable $exception) {
            $this->configService->set('BrainstOdooPro.config.initialSync', $initialSync);
            $this->logError($exception);
        }
        return new JsonResponse(['isValid' => $isValid, 'code' => $code]);
    }


    /**
     * Sync start to add data in queue
     *
     * @param string|null $syncOrderFromDate
     * @return void
     * @throws Throwable
     */
    private function syncAll(?string $syncOrderFromDate = null): void
    {
        ($this->salesChannelSyncQueueService)();
        ($this->categorySyncQueueService)();
        ($this->customerSyncQueueService)();
        ($this->propertySyncQueueService)();
        ($this->productSyncQueueService)();

        // Pass the sync order from date to the order sync queue service
        if ($syncOrderFromDate && method_exists($this->orderSyncQueueService, 'setSyncFromDate')) {
            $this->orderSyncQueueService->setSyncFromDate($syncOrderFromDate);
        }
        ($this->orderSyncQueueService)();
    }
}
