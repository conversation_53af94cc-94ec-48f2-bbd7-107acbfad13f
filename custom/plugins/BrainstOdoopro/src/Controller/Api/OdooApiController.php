<?php declare(strict_types=1);

namespace Brainst\OdooPro\Controller\Api;

use Brainst\OdooPro\Service\AutomationService;
use Brainst\OdooPro\Xmlrpc\Client;
use Exception;
use Psr\Log\LoggerInterface;
use Shopware\Core\Framework\Validation\DataBag\RequestDataBag;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Routing\Annotation\Route;

/**
 * Class OdooApiController
 * @package BrainstOdooPro
 * <AUTHOR>
 * @link www.brainstreamtechnolabs.com
 */
#[Route(defaults: ['_routeScope' => ['administration']])]
class OdooApiController
{
    public function __construct(
        private readonly LoggerInterface $logger,
        private readonly AutomationService $automationService
    ) {
    }

    /**
     * Verify connection for odoo by given dataBag
     *
     * @param RequestDataBag $dataBag
     * @return JsonResponse
     */
    #[Route(path: '/api/brainst-odoo-pro/verify', name: 'api.brainst.odoo-pro.verify', defaults: ["XmlHttpRequest" => true], methods: ['POST'])]
    public function authenticate(RequestDataBag $dataBag): JsonResponse
    {
        try {
            $url = $dataBag->get('BrainstOdooPro.config.serverUrl');
            $db = $dataBag->get('BrainstOdooPro.config.database');
            $username = $dataBag->get('BrainstOdooPro.config.username');
            $password = $dataBag->get('BrainstOdooPro.config.password');

            $common = Client::create("$url/xmlrpc/2/common");
            $versionData = $common->version();
            $version = $this->checkVersion($versionData['server_version']);
            if (!$version) {
                return new JsonResponse(['success' => false, 'message' => 'This version of odoo is not supported, supports only odoo 17 and 18.'], 422);
            }

            $uid = $common->authenticate($db, $username, $password, []);
            if (!$uid) {
                return new JsonResponse(['success' => false, 'message' => 'Authentication failed please check cradintials.'], 401);
            }

            $requiredModules = $this->requiredModules($url, $db, $uid, $password);

            if (!empty($requiredModules)) {
                $modules = implode(', ', $requiredModules);
                return new JsonResponse(['success' => false, 'message' => "$modules"],  424);
            }
            return new JsonResponse(['success' => true, 'message' => "success", 'uid' => $uid, 'version' => $version]);

        } catch (Exception $exception) {
            $this->logger->error($exception);
            return new JsonResponse(['success' => false, 'message' => $exception->getMessage()],  500);
        }
    }

    /**
     * Create automation rules in Odoo
     *
     * @return JsonResponse
     */
    #[Route(path: '/api/brainst-odoo-pro/automation/create', name: 'api.brainst.odoo-pro.automation.create', defaults: ["XmlHttpRequest" => true], methods: ['POST'])]
    public function createAutomation(): JsonResponse
    {
        try {
            // Create new automations (remove is handled separately)
            $this->automationService->update();

            return new JsonResponse([
                'success' => true,
                'message' => 'Automation rules created successfully',
            ]);

        } catch (Exception $exception) {
            $this->logger->error('Failed to create automation rules', [
                'error' => $exception->getMessage(),
                'trace' => $exception->getTraceAsString()
            ]);

            return new JsonResponse([
                'success' => false,
                'message' => 'Failed to create automation rules: ' . $exception->getMessage()
            ], 500);
        }
    }

    /**
     * Remove existing automation rules from Odoo
     *
     * @return JsonResponse
     */
    #[Route(path: '/api/brainst-odoo-pro/automation/remove', name: 'api.brainst.odoo-pro.automation.remove', defaults: ["XmlHttpRequest" => true], methods: ['POST'])]
    public function removeAutomation(): JsonResponse
    {
        try {
            // Call the automation service to remove existing automations
            $this->automationService->remove();

            return new JsonResponse([
                'success' => true,
                'message' => 'Automation rules removed successfully'
            ]);

        } catch (Exception $exception) {
            $this->logger->error('Failed to remove automation rules', [
                'error' => $exception->getMessage(),
                'trace' => $exception->getTraceAsString()
            ]);

            return new JsonResponse([
                'success' => false,
                'message' => 'Failed to remove automation rules: ' . $exception->getMessage()
            ], 500);
        }
    }

    private function checkVersion(string $version): int
    {
        if (str_contains($version, "18")) {
            return 18;
        }
        if (str_contains($version, "17")) {
            return 17;
        }
        return 0;
    }

    private function requiredModules(string $url, string $db, int $uid, string $password): array
    {
        $requiredModules = [
            'accountant' => 'Accounting',
            'sale_management'  => 'Sales',
            'stock' => 'Inventory',
            'base_automation' => 'Automation Rules'
        ];

        $client = Client::create("$url/xmlrpc/2/object");
        $allModules = $client->execute_kw($db, $uid, $password, 'ir.module.module', 'search_read', [
            [['name', 'in', array_keys($requiredModules)], ['state', '=', 'installed']],
            ['name', 'state', 'shortdesc', 'application', 'is_module_official'],
        ]);

        foreach ($allModules as $module)  {
            unset($requiredModules[$module['name']]);
        }
        return $requiredModules;
    }
}
