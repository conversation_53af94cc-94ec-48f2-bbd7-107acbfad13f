<?php declare(strict_types=1);

namespace Brainst\OdooPro\Controller\Api;

use Brainst\OdooPro\Service\OdooWebhookService;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

/**
 * Class WeebhookApiController
 * @package BrainstOdoopro
 * <AUTHOR>
 * @link www.brainstreamtechnolabs.com
 */
#[Route(defaults: ['_routeScope' => ['api']])]
class WeebhookApiController
{
    public function __construct(
        protected LoggerInterface           $logger,
        private readonly OdooWebhookService $odooWebhookService
    )
    {
    }

    #[Route(path: '/api/brainst-odoo-pro/webhook/{operation?}', name: 'api.brainst.odoo-pro.webhook', defaults: ['auth_required' => false], methods: ['POST', 'GET'])]
    public function odooWebhook(Request $request, string $operation = null): Response
    {
        $this->logger->info($request->getContent());
        $payload = json_decode($request->getContent(), true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            $this->logger->error('Invalid JSON payload: ' . $request->getContent());
            return new Response('Invalid JSON', Response::HTTP_BAD_REQUEST);
        }
        $this->odooWebhookService->handleWebhook($payload, $operation);
        return new JsonResponse(['message' => 'Webhook received successfully'], Response::HTTP_OK);
    }
}
