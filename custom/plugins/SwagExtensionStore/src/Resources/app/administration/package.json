{"name": "swag-extension-store", "version": "0.0.1", "description": "Jest test for the administration part of th extension store", "main": "index.js", "scripts": {"unit": "jest --config jest.config.js --ci", "unit-watch": "jest --config jest.config.js --watch", "lint": "eslint --ext .js,.ts,.vue src", "lint-fix": "eslint --fix --ext .js,.ts,.vue src", "lint-ci": "npm run lint -- --format junit --output-file eslint.junit.xml"}, "author": "", "license": "MIT", "devDependencies": {"@babel/eslint-parser": "7.21.3", "@babel/plugin-proposal-class-properties": "7.18.6", "@babel/plugin-transform-runtime": "7.21.0", "@babel/preset-env": "7.20.2", "@shopware-ag/eslint-config-base": "2.0.0", "@shopware-ag/jest-preset-sw6-admin": "4.2.5", "@types/jest": "29.5.0", "@vue/compat": "3.3.4", "@vue/compiler-sfc": "3.3.4", "@vue/component-compiler-utils": "3.3.0", "@vue/test-utils": "2.4.6", "babel-jest": "29.5.0", "eslint-import-resolver-webpack": "0.13.2", "eslint-plugin-internal-rules": "file:internal-rules", "eslint-plugin-jest": "27.2.1", "jest": "29.5.0", "jest-environment-jsdom": "29.5.0"}, "dependencies": {"vue": "3.3.4", "vue-i18n": "9.2.2", "vue-template-compiler": "2.7.14"}, "optionalDependencies": {"administration": "^6.x"}}