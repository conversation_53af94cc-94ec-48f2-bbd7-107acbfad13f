@import "~scss/variables";

.sw-extension-store-detail__extension-unknown {
    width: 960px;
    max-width: 100%;
    height: 450px;
    background-color: $color-white;
    margin: 72px auto 0 auto;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    text-align: center;

    .sw-label {
        width: 72px;
        height: 72px;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-bottom: 20px;

        .sw-label__caption {
            width: auto;
        }
    }

    h2 {
        font-size: $font-size-m;
    }

    p {
        color: $color-darkgray-200;
        font-size: $font-size-xs;
        max-width: 325px;
        margin-bottom: 1.5rem;
    }
}

.sw-meteor-page.sw-extension-store-detail {
    .is--wrap-content {
        word-break: break-word;
    }

    .sw-extension-store-detail--user-provided-data {
        h2,
        h3,
        h4,
        h5,
        h6 {
            margin-top: 1.5em;
            margin-bottom: 1em;

            &:first-child {
                margin-top: 0;
            }

            &:last-child {
                margin-bottom: 0;
            }
        }

        ul,
        ol {
            padding-left: 16px;
        }

        a {
            color: $color-shopware-brand-500;
        }
    }

    .sw-extension-icon {
        width: 72px;
        height: 72px;
    }

    .sw-meteor-page__head-area {
        padding-bottom: 12px;

        .sw-meteor-page__smart-bar-module-info {
            grid-template-rows: min-content auto;
        }
    }

    .sw-meteor-page__smart-bar-header {
        flex-wrap: nowrap;
        flex-direction: column;
        align-items: stretch;

        .sw-meteor-page__smart-bar-title {
            font-size: $font-size-l;
            font-weight: $font-weight-bold;
            margin-bottom: 5px;
            margin-top: -4px;
        }
    }

    .sw-meteor-page__smart-bar-meta {
        color: $color-gray-500;
        font-size: $font-size-xxs;
        display: flex;
        flex-wrap: wrap;
        align-items: baseline;
        margin-bottom: 1px;
        font-weight: $font-weight-medium;
        gap: 8px;
    }

    .sw-extension-store-detail__extension-type-label,
    .sw-extension-store-detail__label-display {
        margin-right: 4px;
    }

    .sw-extension-type-label__cloud-label {
        background-color: rgba(41, 51, 61, 0.5) !important;
    }

    .sw-extension-store-detail__producer-link {
        text-decoration: none;
        font-weight: $font-weight-medium;
    }

    .sw-meteor-page__smart-bar-actions {
        .sw-extension-store-detail__trial-info {
            font-size: $font-size-xs;
            font-weight: $font-weight-regular;
            color: $color-shopware-brand-500;
            padding: 0;
            border: none;
            background: none;
            line-height: inherit;
            text-align: right;
        }

        .is--discounted {
            color: $color-crimson-400;
            text-align: right;

            > .sw-extension-store-detail__net-price {
                text-decoration: line-through;
            }

            > .sw-extension-store-detail__discounted-price {
                margin-left: 4px;
                font-weight: bolder;
                font-size: $font-size-m;
            }
        }

        .sw-extension-store-detail__discounted-price-info {
            color: $color-gray-500;
            font-size: $font-size-xxs;
            margin-top: 0;
            text-align: right;
        }
    }

    .sw-meteor-page__smart-bar-description {
        color: $color-gray-500;
        font-size: $font-size-xs;

        .sw-extension-store-detail__label-and-rating-overview {
            display: flex;
            gap: 8px;

            .sw-extension-store-detail__rating-overview {
                flex-shrink: 0;
                align-self: start;
                display: flex;
                align-items: center;

                & > *:not(:last-child) {
                    margin-right: 8px;
                }

                a,
                a:active,
                a:visited {
                    font-size: $font-size-xxs;
                    font-weight: $font-weight-medium;
                    color: $color-shopware-brand-500;
                    margin-top: 2px;
                    transition: color .2s ease;

                    &:hover,
                    &:focus,
                    &:focus-visible,
                    &:focus-within {
                        color: $color-shopware-brand-700;
                    }
                }

                .sw-extension-store-detail__no-ratings-text {
                    font-size: $font-size-xxs;
                    font-weight: $font-weight-medium;
                }
            }
        }
    }

    &.is--theme .sw-extension-store-slider .sw-extension-store-slider__slides {
        height: 70vw;
        max-height: 720px;
    }

    .sw-extension-store-detail__description {
        &--collapsed {
            max-height: 300px;
            overflow-y: hidden;
        }
    }

    .sw-extension-store-detail__button-extend-description {
        margin-top: 16px;
    }

    .sw-extension-store-detail__details-list {
        display: grid;
        grid-template-columns: auto 1fr;
        justify-items: start;
        column-gap: 40px;
        row-gap: 8px;

        dt {
            font-weight: $font-weight-semi-bold;

            a {
                font-weight: $font-weight-regular;
            }
        }
    }

    .sw-extension-store-detail__faq-question h4 {
        margin-bottom: 5px;
    }

    .sw-extension-store-detail__faq-question:not(:last-child) {
        margin-bottom: 1.5em;
    }

    .sw-extension-store-detail__changelog-entry {
        display: grid;
        grid-template-columns: 96px 1fr;
        grid-template-rows: auto 1fr;
        column-gap: 40px;
        grid-template-areas:
            "version changelog"
            "date changelog";
    }

    .sw-extension-store-detail__changelog-entry:not(:last-child) {
        margin-bottom: 32px;
        padding-bottom: 32px;
        border-bottom: 1px dashed $color-gray-300;
    }

    .sw-extension-store-detail__changelog-version {
        grid-area: version;
        justify-self: right;
        text-align: right;
        font-size: $font-size-s;
        font-weight: $font-weight-bold;
    }

    .sw-extension-store-detail__changelog-creation-date {
        grid-area: date;
        justify-self: right;
        text-align: right;
        font-size: $font-size-xxs;
        font-weight: $font-weight-medium;
        color: $color-gray-500;
    }

    .sw-extension-store-detail__changelog-text {
        grid-area: changelog;
        font-size: $font-size-xs;
        p, ul, ol {
            margin-bottom: 12px;
            li:not(:last-child) {
                margin-bottom: 8px;
            }
        }
        ul {
            list-style: none;
            li {
                margin-left: 16px;
                position: relative;
                &:before{
                    content: "\2022";
                    position: absolute;
                    left: -16px;
                }
            }
        }
        ol {
            list-style-position: inside;
        }
    }

    .sw-extension-store-detail__alert {
        max-width: 960px;
        margin: 0 auto 40px auto;
    }
}

.sw-extension-store-detail__account-modal {
    .sw-modal__body {
        padding: 0;
    }

    .sw-meteor-card {
        margin: 0;
    }
}
