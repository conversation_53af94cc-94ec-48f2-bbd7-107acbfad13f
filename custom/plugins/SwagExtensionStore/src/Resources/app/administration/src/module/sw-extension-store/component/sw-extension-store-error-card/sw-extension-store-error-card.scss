@import "~scss/variables";

.sw-extension-store-error-card {

    .sw-meteor-card__content-wrapper {
        min-height: 430px;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        text-align: center;
    }

    h2.sw-extension-store-error-card__title {
        font-size: $font-size-m;
    }

    &__label {
        width: 72px;
        height: 72px;
        display: flex;
        justify-content: center;
        align-items: center;
        margin: 0 0 20px 0;

        .sw-label__caption {
            width: auto;
        }
    }

    .sw-button {
        margin-top: 12px;
    }

    p {
        color: $color-darkgray-200;
        font-size: $font-size-xs;
        max-width: 325px;
        margin-bottom: 1.5rem;
    }
}
