(window["webpackJsonpPluginswag-extension-store"]=window["webpackJsonpPluginswag-extension-store"]||[]).push([[806],{201:function(){},806:function(e,n,t){"use strict";t.r(n),t.d(n,{default:function(){return a}}),t(550);let{Utils:s}=Shopware;var a={template:'{% block sw_extension_buy_modal %}\n    <sw-modal\n        class="sw-extension-buy-modal"\n        :title="$tc(\'sw-extension-store.component.sw-extension-buy-modal.title\')"\n        size="450px"\n        :isLoading="isLoading"\n        @modal-close="emitClose">\n\n        {% block sw_extension_buy_modal_content %}\n            <template v-if="checkoutStep === checkoutSteps.SUCCESS">\n                {% block sw_extension_buy_modal_adding_success_content %}\n                    <sw-extension-adding-success\n                        @close="emitClose">\n                    </sw-extension-adding-success>\n                {% endblock %}\n            </template>\n\n            <template v-else-if="checkoutStep === checkoutSteps.FAILED">\n                {% block sw_extension_buy_modal_adding_failed_content %}\n                    <sw-extension-adding-failed\n                        :extensionName="extension.name"\n                        :title="checkoutError && checkoutError.title"\n                        :detail="checkoutError && checkoutError.detail"\n                        :documentationLink="checkoutErrorDocumentationLink"\n                        @close="emitClose">\n                    </sw-extension-adding-failed>\n                {% endblock %}\n            </template>\n\n            <template v-else>\n                {% block sw_extension_buy_modal_content_not_installed %}\n                    {% block sw_extension_buy_modal_variant_selection %}\n                        <div class="sw-extension-buy-modal__variants-selection" :class="{\n                            \'sw-extension-buy-modal__variants-selection-single-entry\': recommendedVariants.length <= 1\n                        }">\n                            {% block sw_extension_buy_modal_variant_selection_content %}\n                                {% block sw_extension_buy_modal_variant_selection_extension_preview %}\n                                    <div class="sw-extension-buy-modal__extension-summary">\n                                        {% block sw_extension_buy_modal_variant_selection_extension_preview_content %}\n                                            {% block sw_extension_buy_modal_variant_selection_extension_icon %}\n                                                <sw-extension-icon :src="extension.icon"/>\n                                            {% endblock %}\n\n                                            {% block sw_extension_buy_modal_variant_selection_extension_name %}\n                                                <h4 class="sw-extension-buy-modal__extension-summary-name">\n                                                    {{ extension.label }}\n                                                </h4>\n                                            {% endblock %}\n                                        {% endblock %}\n                                    </div>\n                                {% endblock %}\n\n                                {% block sw_extension_buy_modal_variant_selection_variant_list %}\n                                    <ul>\n                                        {% block sw_extension_buy_modal_variant_selection_variant_list_item %}\n                                            <li v-for="variant in recommendedVariants"\n                                                :key="variant.id"\n                                                class="sw-extension-buy-modal__variants-card"\n                                                :class="variantCardClass(variant)"\n                                                @click="onChangeVariantSelection(variant)">\n\n                                                {% block sw_extension_buy_modal_variant_selection_variant_list_item_radio_button %}\n                                                    <div class="sw-extension-buy-modal__variants-card-input sw-field--radio">\n                                                        <div class="sw-field__radio-input">\n                                                            <input type="radio"\n                                                                   name="variant-selection"\n                                                                   :id="`sw-extension-buy-modal__variant-${variant.type}`"\n                                                                   :value="variant.id"\n                                                                   :checked="variant.id === selectedVariantId"\n                                                                   :disabled="isLoading"/>\n                                                            <div class="sw-field__radio-state"></div>\n                                                        </div>\n                                                    </div>\n                                                {% endblock %}\n\n                                                {% block sw_extension_buy_modal_variant_selection_variant_list_item_variant_label %}\n                                                    {% block sw_extension_buy_modal_variant_selection_variant_list_item_variant_label_description %}\n                                                        <label :for="`sw-extension-buy-modal__variant-${variant.type}`"\n                                                               class="sw-extension-buy-modal__variant-description">\n                                                            {{ variant.label || variant.type }}\n                                                        </label>\n                                                    {% endblock %}\n\n                                                    {% block sw_extension_buy_modal_variant_selection_variant_list_item_variant_label_rent %}\n                                                        <div v-if="variant.type === \'rent\'"\n                                                             class="sw-extension-buy-modal__rent">\n\n                                                            {% block sw_extension_buy_modal_variant_selection_variant_list_item_variant_label_rent_prices %}\n                                                                <i18n-t\n                                                                    keypath="sw-extension-store.buy-modal.rent.priceDisplay"\n                                                                    tag="span"\n                                                                    :class="getDiscountClasses(variant)">\n                                                                    <template #priceDisplay>\n                                                                        <span class="sw-extension-buy-modal__list-price">{{ renderPrice(variant.netPrice) }}</span>\n                                                                        <span v-if="hasDiscount(variant)" class="sw-extension-buy-modal__discounted-price">{{ getDiscountPrice(variant) }}</span>\n                                                                    </template>\n                                                                </i18n-t>\n                                                            {% endblock %}\n\n                                                            {% block sw_extension_buy_modal_variant_selection_variant_list_item_variant_label_rent_trial %}\n                                                                <div v-if="variant.trialPhaseIncluded && firstMonthFree">{{ $tc(\'sw-extension-store.buy-modal.rent.freeTrial\') }}</div>\n                                                            {% endblock %}\n\n                                                            {% block sw_extension_buy_modal_variant_selection_variant_list_item_variant_label_rent_disclaimer %}\n                                                                <div>{{ $tc(\'sw-extension-store.buy-modal.rent.disclaimer\') }}</div>\n                                                            {% endblock %}\n                                                        </div>\n                                                    {% endblock %}\n\n                                                    {% block sw_extension_buy_modal_variant_selection_variant_list_item_variant_label_buy %}\n                                                        <div v-else-if="variant.type === \'buy\'"\n                                                             :class="getDiscountClasses(variant)">\n\n                                                            {% block sw_extension_buy_modal_variant_selection_variant_list_item_variant_label_buy_prices %}\n                                                                <span v-if="hasDiscount(variant)"\n                                                                      class="sw-extension-buy-modal__list-price">{{ renderPrice(variant.netPrice) }}</span>\n                                                                <span class="sw-extension-buy-modal__discounted-price">{{ renderBuyPrice(variant) }}</span>\n                                                            {% endblock %}\n                                                        </div>\n                                                    {% endblock %}\n\n                                                    {% block sw_extension_buy_modal_variant_selection_variant_list_item_variant_label_fallback %}\n                                                        <span v-else\n                                                              class="sw-extension-buy-modal__display-default-price">\n                                                            {{ renderPrice(variant.netPrice) }}\n                                                        </span>\n                                                    {% endblock %}\n\n                                                    {% block sw_extension_buy_modal_variant_selection_variant_list_item_variant_label_legal %}\n                                                        <span v-if="variant.legalText"\n                                                              v-html="legalTextForVariant(variant)"\n                                                              class="sw-extension-buy-modal__variant-text-block"/>\n                                                    {% endblock %}\n                                                {% endblock %}\n                                            </li>\n                                        {% endblock %}\n                                    </ul>\n                                {% endblock %}\n                            {% endblock %}\n                        </div>\n                    {% endblock %}\n\n                    {% block sw_extension_buy_modal_summary %}\n                        <div class="sw-extension-buy-modal__variant-summary">\n                            {% block sw_extension_buy_modal_summary_content %}\n                                {% block sw_extension_buy_modal_summary_price %}\n                                    <p class="sw-extension-buy-modal__variant-summary-price">\n                                        {% block sw_extension_buy_modal_summary_price_content %}\n                                            {% block sw_extension_buy_modal_summary_label_price %}\n                                                <span>\n                                                    {{ $tc(\'sw-extension-store.component.sw-extension-buy-modal.variantSummary.labelPrice\') }}\n                                                </span>\n                                            {% endblock %}\n\n                                            {% block sw_extension_buy_modal_summary_actual_price %}\n                                                <span v-if="!isLoading"\n                                                      class="sw-extension-buy-modal__variant-summary-actual-price">\n                                                    {{ $tc(\'sw-extension-store.general.labelPrice\', variantRecommendation(selectedVariant), { price: formattedPrice }) }}\n                                                </span>\n                                            {% endblock %}\n                                        {% endblock %}\n                                    </p>\n                                {% endblock %}\n\n                                {% block sw_extension_buy_modal_summary_label_plus_vat %}\n                                    <p class="sw-extension-buy-modal__variant-summary-price-subline"\n                                       :class="vatIncludedClasses">\n                                        {{ $tc(\'sw-extension-store.component.sw-extension-buy-modal.variantSummary.labelVat\') }}\n                                    </p>\n                                {% endblock %}\n\n                                {% block sw_extension_buy_modal_summary_renewal_date %}\n                                    <p class="sw-extension-buy-modal__variant-summary-price-subline"\n                                       :class="renewalDateClasses">\n                                        {{ $tc(\'sw-extension-store.component.sw-extension-buy-modal.variantSummary.renewal\', 0, { renewalDate: dateFilter(todayPlusOneMonth) }) }}\n                                    </p>\n                                {% endblock %}\n                            {% endblock %}\n                        </div>\n                    {% endblock %}\n\n                    {% block sw_extension_payment_selection %}\n                        <sw-single-select\n                            v-if="showPaymentSelection"\n                            :options="paymentMeans"\n                            :placeholder="$tc(\'sw-extension-store.buy-modal.paymentSelectionPlaceholder\')"\n                            valueProperty="id"\n                            v-model:value="selectedPaymentMean"\n                        >\n                        </sw-single-select>\n                    {% endblock %}\n\n                    {% block sw_extension_payment_general_text_block %}\n                        <p class="sw-extension-buy-modal__general-text-block"\n                           v-if="paymentText"\n                           v-html="paymentText">\n                        </p>\n                    {% endblock %}\n\n                    {% block sw_extension_buy_modal_accept_toc %}\n                        <sw-gtc-checkbox\n                            v-model:value="tocAccepted"\n                        >\n                        </sw-gtc-checkbox>\n                    {% endblock %}\n\n                    {% block sw_extension_buy_modal_accept_extension_permissions %}\n                        <sw-checkbox-field\n                            v-if="extensionHasPermissions"\n                            class="sw-extension-buy-modal__checkbox sw-extension-buy-modal__checkbox-permissions"\n                            :class="`sw-extension-buy-modal__checkbox-permissions--${extension.name}`"\n                            v-model:value="permissionsAccepted"\n                        >\n                            <template #label>\n                                <span v-if="legalText">\n                                    {{ $t(\'sw-extension-store.buy-modal.checkboxes.permissionsAndAppProvider.introText\') }}\n                                    <a\n                                        href="#"\n                                        class="link permissions-modal-trigger"\n                                        @click.prevent="openPermissionsModal"\n                                    >\n                                        {{ $t(\'sw-extension-store.buy-modal.checkboxes.permissionsAndAppProvider.buttonPermissions\') }}\n                                    </a>\n                                    {{ $t(\'sw-extension-store.buy-modal.checkboxes.permissionsAndAppProvider.middleText\') }}\n                                    <a\n                                        href="#"\n                                        class="link legal-text-modal-trigger"\n                                        @click.prevent="openLegalTextModal"\n                                    >\n                                        {{ $t(\'sw-extension-store.buy-modal.checkboxes.permissionsAndAppProvider.buttonAppProvider\') }}\n                                    </a>\n                                    {{ $t(\'sw-extension-store.buy-modal.checkboxes.permissionsAndAppProvider.endText\') }}\n                                </span>\n\n                                <span v-else>\n                                    {{ $tc(\'sw-extension-store.component.sw-extension-buy-modal.labelAcceptPermissions\') }}\n                                    <a\n                                        href="#"\n                                        class="link permissions-modal-trigger"\n                                        @click="openPermissionsModal"\n                                    >\n                                        {{ $tc(\'sw-extension-store.detail.labelButtonShowPermissions\') }}\n                                    </a>\n                                </span>\n                            </template>\n                        </sw-checkbox-field>\n                    {% endblock %}\n\n                    {% block sw_extension_buy_modal_accept_extension_app_provider %}\n                        <sw-checkbox-field\n                            v-if="!extensionHasPermissions && legalText"\n                            v-model="legalTextAccepted"\n                            class="sw-extension-buy-modal__checkbox sw-extension-buy-modal__checkbox-app-provider"\n                            :class="`sw-extension-buy-modal__checkbox-app-provider--${extension.name}`"\n                        >\n                            <template #label>\n                                {{ $t(\'sw-extension-store.buy-modal.checkboxes.permissionsAndAppProvider.appProviderStartText\') }}\n                                <a\n                                    @click.prevent="openLegalTextModal"\n                                    class="link legal-text-modal-trigger"\n                                    href="#"\n                                >\n                                    {{ $t(\'sw-extension-store.buy-modal.checkboxes.permissionsAndAppProvider.buttonAppProvider\') }}\n                                </a>\n                                {{ $t(\'sw-extension-store.buy-modal.checkboxes.permissionsAndAppProvider.endText\') }}\n                            </template>\n                        </sw-checkbox-field>\n                    {% endblock %}\n\n                    {% block sw_extension_buy_modal_accept_extension_privacy_policy_extensions %}\n                        <sw-checkbox-field\n                            v-if="extension.privacyPolicyExtension"\n                            v-model="privacyExtensionsAccepted"\n                            class="sw-extension-buy-modal__checkbox-privacy-policy"\n                            :class="`sw-extension-buy-modal__checkbox-privacy-policy--${extension.name}`"\n                        >\n                            <template #label>\n                                <span class="is--required">\n                                    {{ $tc(\'sw-extension-store.component.sw-extension-buy-modal.labelAcceptPrivacyExtensions\') }}\n                                </span>\n                                <a\n                                    href="#"\n                                    class="link privacy-policy-modal-trigger"\n                                    @click.prevent="openPrivacyModal"\n                                >\n                                    {{ $tc(\'sw-extension-store.component.sw-extension-buy-modal.buttonShowPrivacyExtensions\') }}\n                                </a>\n                            </template>\n                        </sw-checkbox-field>\n                    {% endblock %}\n\n                    {% block sw_extension_buy_modal_alert_can_not_buy %}\n                        <sw-alert v-if="!userCanBuyFromStore" variant="info">\n                            {% block sw_extension_buy_modal_alert_can_not_buy_content %}\n                                {{ $tc(\'sw-extension-store.component.sw-extension-buy-modal.alertCanNotBuy.text\') }}\n                            {% endblock %}\n                        </sw-alert>\n                    {% endblock %}\n\n                    {% block sw_extension_buy_modal_alert_payment_means_required %}\n                        <sw-alert v-if="showPaymentWarning" variant="warning">\n                            {% block sw_extension_buy_modal_alert_payment_means_required_content %}\n                                {{ $t(\'sw-extension-store.buy-modal.warnings.paymentMeansRequiredText\') }}\n                                <sw-external-link\n                                    :href="cart && cart.payment && cart.payment.registrationUrl"\n                                    class="sw-extension-buy-modal__external-link"\n                                >\n                                    {{ $t(\'sw-extension-store.buy-modal.warnings.paymentMeansRequiredLinkText\') }}\n                                    <sw-icon name="regular-long-arrow-right"></sw-icon>\n                                </sw-external-link>\n                            {% endblock %}\n                        </sw-alert>\n                    {% endblock %}\n\n                    {% block sw_extension_buy_modal_button_purchase %}\n                        <sw-button variant="primary"\n                                   block\n                                   :disabled="!canPurchaseExtension"\n                                   @click="purchaseExtension">\n                            {{ purchaseButtonLabel }}\n                        </sw-button>\n                    {% endblock %}\n                {% endblock %}\n\n                {% block sw_extension_buy_modal_permissions_modal %}\n                    <sw-extension-permissions-modal\n                        v-if="showPermissionsModal"\n                        :extensionLabel="extension.label"\n                        :domains="extension.domains"\n                        :permissions="extension.permissions"\n                        @modal-close="closePermissionsModal">\n                    </sw-extension-permissions-modal>\n                {% endblock %}\n\n                {% block sw_extension_buy_modal_privacy_extensions_modal %}\n                    <sw-extension-privacy-policy-extensions-modal\n                        v-if="showPrivacyModal"\n                        :extensionName="extension.label"\n                        :privacyPolicyExtension="extension.privacyPolicyExtension"\n                        @modal-close="closePrivacyModal">\n                    </sw-extension-privacy-policy-extensions-modal>\n                {% endblock %}\n\n                {% block sw_extension_buy_modal_legal_text_modal %}\n                    <sw-modal\n                        v-if="showLegalTextModal"\n                        @modal-close="closeLegalTextModal"\n                        class="sw-extension-buy-modal__legal-text-modal"\n                        :title="$t(\'sw-extension-store.buy-modal.legalTextModal.title\')">\n                        <div v-html="legalText"></div>\n\n                        <template #modal-footer>\n                            <sw-button\n                                variant="primary"\n                                size="small"\n                                @click="closeLegalTextModal">\n                                {{ $tc(\'global.default.close\') }}\n                            </sw-button>\n                        </template>\n                    </sw-modal>\n                {% endblock %}\n            </template>\n        {% endblock %}\n    </sw-modal>\n{% endblock %}\n',inject:["shopwareExtensionService","extensionStoreLicensesService"],emits:["modal-close"],mixins:["sw-extension-error"],props:{extension:{type:Object,required:!0}},data(){return{tocAccepted:!1,selectedVariantId:null,isLoading:!1,permissionsAccepted:!1,legalTextAccepted:!1,showPermissionsModal:!1,showLegalTextModal:!1,privacyExtensionsAccepted:!1,showPrivacyModal:!1,checkoutStep:null,checkoutError:null,cart:null,paymentMeans:[]}},computed:{recommendedVariants(){return this.shopwareExtensionService.orderVariantsByRecommendation(this.extension.variants)},selectedVariant(){return this.extension.variants.find(e=>e.id===this.selectedVariantId)},todayPlusOneMonth(){let e=new Date;return e.setMonth(e.getMonth()+1),e},dateFilter(){return s.format.date},formattedPrice(){let e=this.cart&&this.cart.positions&&this.cart.positions[0],n=e&&e.netPrice;return n&&e&&e.firstMonthFree?s.format.currency(0,"EUR"):n?s.format.currency(n,"EUR",2):s.format.currency(this.shopwareExtensionService.getPriceFromVariant(this.selectedVariant),"EUR")},trialPrice(){return this.renderPrice(0)},purchaseButtonLabel(){switch(this.selectedVariant.type){case this.shopwareExtensionService.EXTENSION_VARIANT_TYPES.FREE:return this.$tc("sw-extension-store.component.sw-extension-buy-modal.purchaseButtonsLabels.free");case this.shopwareExtensionService.EXTENSION_VARIANT_TYPES.RENT:return this.$tc("sw-extension-store.component.sw-extension-buy-modal.purchaseButtonsLabels.rent");case this.shopwareExtensionService.EXTENSION_VARIANT_TYPES.BUY:default:return this.$tc("sw-extension-store.component.sw-extension-buy-modal.purchaseButtonsLabels.buy")}},vatIncludedClasses(){return{"is--hidden":this.selectedVariant.type===this.shopwareExtensionService.EXTENSION_VARIANT_TYPES.FREE}},renewalDateClasses(){return{"is--hidden":this.selectedVariant.type!==this.shopwareExtensionService.EXTENSION_VARIANT_TYPES.RENT}},extensionHasPermissions(){return!!Object.keys(this.extension.permissions).length},canPurchaseExtension(){return this.tocAccepted&&this.permissionsAccepted&&this.legalTextAccepted&&this.privacyExtensionsAccepted&&this.userCanBuyFromStore&&!this.showPaymentWarning},userCanBuyFromStore(){return this.tocAccepted,null!==Shopware.State.get("shopwareExtensions").userInfo},showPaymentWarning(){return(this.paymentMeans||[]).length<=0&&this.cart&&this.cart.payment&&this.cart.payment.paymentMeanRequired},checkoutSteps(){return Object.freeze({CHECKOUT:null,SUCCESS:"checkout-success",FAILED:"checkout-failed"})},showPaymentSelection(){return(this.paymentMeans||[]).length>0&&this.cart&&this.cart.payment&&this.cart.payment.paymentMeanRequired},paymentText(){return this.cart&&this.cart.payment&&this.cart.payment.paymentText?this.$sanitize(this.cart.payment.paymentText,{ALLOWED_TAGS:["a","b","i","u","br","strong","p","br"],ALLOWED_ATTR:["href","target","rel"]}):null},legalText(){return this.cart&&this.cart.legalText?this.$sanitize(this.cart.legalText,{ALLOWED_TAGS:["a","b","i","u","br","strong","p","br"],ALLOWED_ATTR:["href","target","rel"]}):null},selectedPaymentMean:{get(){return this.cart&&this.cart.payment&&this.cart.payment.paymentMean&&this.cart.payment.paymentMean.id},set(e){this.cart&&this.cart.payment&&this.cart.payment.paymentMean&&(this.cart.payment.paymentMean.id=e),this.cart.payment.paymentMean={id:e}}},checkoutErrorDocumentationLink(){return s.get(this.checkoutError,"meta.documentationLink",null)},firstMonthFree(){return this.cart&&this.cart.positions[0]&&!0===this.cart.positions[0].firstMonthFree}},async created(){let e=this.recommendedVariants.length>0?this.recommendedVariants[0].id:null;this.setSelectedVariantId(e),this.permissionsAccepted=!this.extensionHasPermissions,this.privacyExtensionsAccepted=!this.extension.privacyPolicyExtension,await this.fetchPlan(),this.legalTextAccepted=!this.legalText},watch:{selectedVariantId(){this.getCart()},userCanBuyFromStore(e){e&&this.getCart()},permissionsAccepted(e){this.legalTextAccepted=e}},methods:{emitClose(){this.isLoading||this.$emit("modal-close")},setSelectedVariantId(e){this.isLoading||(this.selectedVariantId=e)},variantCardClass(e){return{"is--selected":e.id===this.selectedVariantId}},onChangeVariantSelection(e){this.setSelectedVariantId(e.id)},variantRecommendation(e){return this.shopwareExtensionService.mapVariantToRecommendation(e)},async purchaseExtension(){this.isLoading=!0;let e=null;try{await this.orderCart(),await this.shopwareExtensionService.updateExtensionData(),e=this.checkoutSteps.SUCCESS}catch(n){this.handleErrors(n),e=this.checkoutSteps.FAILED,s.get(n,"response.data.errors[0]",null)&&(this.checkoutError=n.response.data.errors[0])}finally{await this.shopwareExtensionService.updateExtensionData(),this.checkoutStep=e,this.isLoading=!1}},async orderCart(){await this.extensionStoreLicensesService.orderCart(this.cart)},async getCart(){if(this.userCanBuyFromStore){this.isLoading=!0;try{let e=await this.extensionStoreLicensesService.newCart(this.extension.id,this.selectedVariantId);this.cart=e.data}catch(e){this.handleErrors(e),this.isLoading=!1,this.emitClose()}finally{this.isLoading=!1}}},getDiscountClasses(e){return{"is--discounted":this.hasDiscount(e)}},hasDiscount(e){let n=e.discountCampaign;return n&&new Date(Date.parse(n.startDate))<new Date&&new Date(Date.parse(n.endDate))>=new Date},renderPrice(e){return s.format.currency(e,"EUR")},renderBuyPrice(e){return this.hasDiscount(e)?this.renderPrice(e.discountCampaign.discountedPrice):this.renderPrice(e.netPrice)},getDiscountPrice(e){return e.discountCampaign?this.renderPrice(e.discountCampaign.discountedPrice):this.trialPrice},getDiscountEnds(e){return s.format.date(e.discountCampaign?new Date(Date.parse(e.discountCampaign.endDate)):null)},handleErrors(e){this.showExtensionErrors(e)},openPermissionsModal(){this.showPermissionsModal=!0},closePermissionsModal(){this.showPermissionsModal=!1},openLegalTextModal(){this.showLegalTextModal=!0},closeLegalTextModal(){this.showLegalTextModal=!1},async fetchPlan(){this.isLoading=!0,await this.shopwareExtensionService.checkLogin(),await this.getPaymentMeans(),this.isLoading=!1},async getPaymentMeans(){this.extensionStoreLicensesService.getPaymentMeans().then(e=>{this.paymentMeans=e.data}).catch(e=>{let n=e.response&&e.response.data&&e.response.data.errors||[];if(!Array.isArray(n)){Shopware.Utils.debug.warn("Payment loading error",e);return}n.forEach(e=>{this.createNotificationError({system:!0,autoClose:!1,growl:!0,title:e.title,message:e.detail})})})},legalTextForVariant(e){return e&&e.legalText?this.$sanitize(e.legalText,{ALLOWED_TAGS:["a","b","i","u","br","strong","p","br"],ALLOWED_ATTR:["href","target","rel"]}):null},openPrivacyModal(){this.showPrivacyModal=!0},closePrivacyModal(){this.showPrivacyModal=!1}}}},550:function(e,n,t){var s=t(201);s.__esModule&&(s=s.default),"string"==typeof s&&(s=[[e.id,s,""]]),s.locals&&(e.exports=s.locals),t(346).Z("09ec7d3f",s,!0,{})},346:function(e,n,t){"use strict";function s(e,n){for(var t=[],s={},a=0;a<n.length;a++){var i=n[a],o=i[0],r={id:e+":"+a,css:i[1],media:i[2],sourceMap:i[3]};s[o]?s[o].parts.push(r):t.push(s[o]={id:o,parts:[r]})}return t}t.d(n,{Z:function(){return p}});var a="undefined"!=typeof document;if("undefined"!=typeof DEBUG&&DEBUG&&!a)throw Error("vue-style-loader cannot be used in a non-browser environment. Use { target: 'node' } in your Webpack config to indicate a server-rendering environment.");var i={},o=a&&(document.head||document.getElementsByTagName("head")[0]),r=null,l=0,c=!1,d=function(){},_=null,m="data-vue-ssr-id",u="undefined"!=typeof navigator&&/msie [6-9]\b/.test(navigator.userAgent.toLowerCase());function p(e,n,t,a){c=t,_=a||{};var o=s(e,n);return b(o),function(n){for(var t=[],a=0;a<o.length;a++){var r=i[o[a].id];r.refs--,t.push(r)}n?b(o=s(e,n)):o=[];for(var a=0;a<t.length;a++){var r=t[a];if(0===r.refs){for(var l=0;l<r.parts.length;l++)r.parts[l]();delete i[r.id]}}}}function b(e){for(var n=0;n<e.length;n++){var t=e[n],s=i[t.id];if(s){s.refs++;for(var a=0;a<s.parts.length;a++)s.parts[a](t.parts[a]);for(;a<t.parts.length;a++)s.parts.push(x(t.parts[a]));s.parts.length>t.parts.length&&(s.parts.length=t.parts.length)}else{for(var o=[],a=0;a<t.parts.length;a++)o.push(x(t.parts[a]));i[t.id]={id:t.id,refs:1,parts:o}}}}function h(){var e=document.createElement("style");return e.type="text/css",o.appendChild(e),e}function x(e){var n,t,s=document.querySelector("style["+m+'~="'+e.id+'"]');if(s){if(c)return d;s.parentNode.removeChild(s)}if(u){var a=l++;n=v.bind(null,s=r||(r=h()),a,!1),t=v.bind(null,s,a,!0)}else n=w.bind(null,s=h()),t=function(){s.parentNode.removeChild(s)};return n(e),function(s){s?(s.css!==e.css||s.media!==e.media||s.sourceMap!==e.sourceMap)&&n(e=s):t()}}var y=function(){var e=[];return function(n,t){return e[n]=t,e.filter(Boolean).join("\n")}}();function v(e,n,t,s){var a=t?"":s.css;if(e.styleSheet)e.styleSheet.cssText=y(n,a);else{var i=document.createTextNode(a),o=e.childNodes;o[n]&&e.removeChild(o[n]),o.length?e.insertBefore(i,o[n]):e.appendChild(i)}}function w(e,n){var t=n.css,s=n.media,a=n.sourceMap;if(s&&e.setAttribute("media",s),_.ssrId&&e.setAttribute(m,n.id),a&&(t+="\n/*# sourceURL="+a.sources[0]+" */\n/*# sourceMappingURL=data:application/json;base64,"+btoa(unescape(encodeURIComponent(JSON.stringify(a))))+" */"),e.styleSheet)e.styleSheet.cssText=t;else{for(;e.firstChild;)e.removeChild(e.firstChild);e.appendChild(document.createTextNode(t))}}}}]);