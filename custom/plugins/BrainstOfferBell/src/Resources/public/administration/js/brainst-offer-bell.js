(function(){"use strict";var e={};e.p="bundles/brainstofferbell/",window?.__sw__?.assetPath&&(e.p=window.__sw__.assetPath+"/bundles/brainstofferbell/"),function(){let{Context:e,Component:t}=Shopware,{Criteria:i}=Shopware.Data;t.register("brainst-offerBell-tab",{template:'{% block brainst_offerbell_tab %}\n    <sw-container>\n        <sw-card title="Configurations">\n            {% block brainst_offerbell_tab_active %}\n                <sw-switch-field\n                        v-model:value="offerBell.active"\n                        :label="$tc(\'brainst-offerBell.form.activeLabel\')"\n                        >\n\n                </sw-switch-field>\n            {% endblock %}\n\n            {% block brainst_offerbell_tab_title %}\n                <sw-text-field\n                        v-model:value="offerBell.title"\n                        :label="$tc(\'brainst-offerBell.form.titleLabel\')"\n                        :placeholder="$tc(\'brainst-offerBell.form.titlePlaceholder\')"\n                ></sw-text-field>\n            {% endblock %}\n\n            {% block brainst_offerbell_tab_media %}\n                <div class="media-upload">\n                    <sw-upload-listener\n                            upload-tag="brainst-offerBell-translation-image"\n                            auto-upload\n                            @media-upload-finish="onSetMediaItem"\n                    ></sw-upload-listener>\n                    <sw-media-upload-v2\n                            :label="$tc(\'sw-category.base.menu.imageLabel\')"\n                            variant="regular"\n                            :disabled="!acl.can(\'Offerbell.editor\')"\n                            :source="mediaItem"\n                            upload-tag="brainst-offerBell-translation-image"\n                            :allow-multi-select="false"\n                            :default-folder="offerTranslationRepository.schema.entity"\n                            :fileAccept="fileAccept"\n                            @media-drop="onMediaDropped"\n                            @media-upload-sidebar-open="showMediaModal = true"\n                            @media-upload-remove-image="onRemoveMediaItem"\n                    ></sw-media-upload-v2>\n                </div>\n            {% endblock %}\n\n            {% block brainst_offerbell_tab_media_modal %}\n                <sw-media-modal-v2\n                        v-if="showMediaModal"\n                        :allow-multi-select="false"\n                        :initial-folder-id="mediaDefaultFolderId"\n                        :entity-context="offerTranslationRepository.schema.entity"\n                        :fileAccept="fileAccept"\n                        @media-modal-selection-change="onMediaSelectionChange"\n                        @modal-close="showMediaModal = false"\n                ></sw-media-modal-v2>\n            {% endblock %}\n        </sw-card>\n    </sw-container>\n{% endblock %}',inject:["acl","repositoryFactory"],data(){return{isLoading:!1,promotionId:null,showMediaModal:!1,mediaDefaultFolderId:"",offerBell:{active:!1,mediaId:"",title:"test",promotionId:this.$route.params.id},currentLanguageId:e.api.languageId,isSaved:!1,fileAccept:"image/*"}},computed:{offerBellRepository(){return this.repositoryFactory.create("brainst_offer")},offerTranslationRepository(){return this.repositoryFactory.create("brainst_offer_translation")},mediaItem(){return this.offerBell?this.offerBell.media:""},mediaRepository(){return this.repositoryFactory.create("media")},mediaDefaultFolderRepository(){return this.repositoryFactory.create("media_default_folder")},mediaDefaultFolderCriteria(){let e=new i(1,1);return e.addAssociation("folder"),e.addFilter(i.equals("entity","brainst_offer_translation")),e}},async created(){this.registerEventListeners(),this.loadMediaDefaultFolder(),this.$route.params.id?(this.promotionId=this.$route.params.id,this.loadOfferBell()):this.defaultOfferBell()},methods:{defaultOfferBell(){let t=this.offerBellRepository.create(e.api);this.offerBell=Object.assign(t,this.offerBell)},async changeLanguage(e){this.currentLanguageId=e,await this.loadOfferBell()},async loadOfferBell(){let t=new i;t.addFilter(i.equals("promotionId",this.promotionId));let a=await this.offerBellRepository.search(t,e.api);a.first()?(this.offerBell=a.first(),this.offerBell.mediaId&&this.onSetMediaItem({targetId:this.offerBell.mediaId})):this.defaultOfferBell()},loadMediaDefaultFolder(){this.getMediaDefaultFolderId().then(e=>{this.mediaDefaultFolderId=e})},getMediaDefaultFolderId(){return this.mediaDefaultFolderRepository.search(this.mediaDefaultFolderCriteria,e.api).then(e=>{let t=e.first();return null===t?null:t.folder?.id?t.folder.id:null})},async onMediaSelectionChange(e){let t=e[0];t&&this.mediaRepository.get(t.id).then(e=>{this.offerBell.mediaId=e.id,this.offerBell.media=e})},async onSetMediaItem({targetId:e}){this.mediaRepository.get(e).then(t=>{this.offerBell.mediaId=e,this.offerBell.media=t})},onRemoveMediaItem(){this.offerBell.mediaId="",this.offerBell.media=""},onMediaDropped(e){this.onSetMediaItem({targetId:e.id})},registerEventListeners(){let e=this.getPromotionComponent();this.saveOfferBell=this.saveOfferBell.bind(this),void 0===e||this.registeredEventListeners||e.$on("save-offerBell",this.saveOfferBell),this.registeredEventListeners=!0},unregisterEventListeners(){let e=this.getPromotionComponent();void 0!==e&&e.$off("save-offerBell"),this.registeredEventListeners=!1},getPromotionComponent:function(){let e=this;for(;"sw-promotion-v2-detail"!==e.$options.name;){if(!e.$parent)return;e=e.$parent}return e},async saveOfferBell(){try{await this.offerBellRepository.save(this.offerBell,e.api).then(()=>{this.createNotificationSuccess({message:"Success"}),this.isLoading=!1}).catch(e=>{this.isLoading=!1,console.error("Error:",e),console.log(e.stack)})}catch(e){this.isLoading=!1,console.error("Error:",e)}}}}),Shopware.Component.override("sw-promotion-v2-detail",{template:"{% block sw_promotion_v2_detail_content_tabs_discounts %}\n    {% parent %}\n\n    <sw-tabs-item :route=\"{ name: 'brainst.offerBell.tab', params: { id: $route.params.id } }\" title=\"OfferBell\">\n        {{ $tc('brainst-offerBell.tabs.OfferBell') }}\n    </sw-tabs-item>\n{% endblock %}",methods:{onSave(){let e=this.$super("onSave");return this.$emit("save-offerBell"),e}}});var a=JSON.parse('{"brainst-offerbell":{"tabs":{"title":"AngebotGlocke"},"form":{"activeLabel":"Aktiv","titleLabel":"Titel","titlePlaceholder":"Titel hinzuf\xfcgen"}}}'),l=JSON.parse('{"brainst-offerBell":{"tabs":{"title":"OfferBell"},"form":{"activeLabel":"Active","titleLabel":"Title","titlePlaceholder":"Add title"}}}');let{Module:o}=Shopware;o.register("brainst-offerBell-tab",{routeMiddleware(e,t){"sw.promotion.v2.detail"===t.name&&t.children.push({name:"brainst.offerBell.tab",path:"/sw/promotion/v2/detail/:id/offerBell",snippets:{"de-DE":a,"en-GB":l},component:"brainst-offerBell-tab",meta:{parentPath:"sw.promotion.v2.index",privilege:"promotion.viewer"}}),e(t)}})}()})();