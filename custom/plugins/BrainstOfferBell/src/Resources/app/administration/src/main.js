import './page/brainst-offerbell-tab';
import './page/sw-promotion-v2-detail';

import deDE from './snippet/de-DE.json';
import enGB from './snippet/en-GB.json';


const { Module } = Shopware;

Module.register('brainst-offerBell-tab', {
    routeMiddleware(next, currentRoute) {
        if (currentRoute.name === 'sw.promotion.v2.detail') {
            currentRoute.children.push({
                name: 'brainst.offerBell.tab',
                path: '/sw/promotion/v2/detail/:id/offerBell',
                snippets: {
                    'de-DE': deDE,
                    'en-GB': enGB
                },
                component: 'brainst-offerBell-tab',
                meta: {
                    parentPath: "sw.promotion.v2.index",
                    privilege: 'promotion.viewer'
                }
            });
        }
        next(currentRoute);
    }
});
