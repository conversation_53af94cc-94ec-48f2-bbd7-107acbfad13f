<?php declare(strict_types=1);

namespace Brainst\OfferBell\Core\Content\BrainstOffer\Aggregate\BrainstOfferTranslation;

use Shopware\Core\Framework\DataAbstractionLayer\Entity;
use Shopware\Core\Framework\DataAbstractionLayer\EntityIdTrait;
use Shopware\Core\Content\Media\MediaEntity;
use Brainst\OfferBell\Core\Content\BrainstOffer\BrainstOfferEntity;
use Shopware\Core\System\Language\LanguageEntity;

class BrainstOfferTranslationEntity extends Entity
{
    use EntityIdTrait;

    /**
     * @var string
     */
    protected $title;

    /**
     * @var string
     */
    protected $mediaId;

    /**
     * @var array|null
     */
    protected $customFields;

    /**
     * @var MediaEntity|null
     */
    protected $media;

    /**
     * @var \DateTimeInterface
     */
    protected $createdAt;

    /**
     * @var \DateTimeInterface|null
     */
    protected $updatedAt;

    /**
     * @var string
     */
    protected $brainstOfferId;

    /**
     * @var string
     */
    protected $languageId;

    /**
     * @var BrainstOfferEntity|null
     */
    protected $brainstOffer;

    /**
     * @var LanguageEntity|null
     */
    protected $language;

    public function getTitle(): string
    {
        return $this->title;
    }

    public function setTitle(string $title): void
    {
        $this->title = $title;
    }

    public function getMediaId(): string
    {
        return $this->mediaId;
    }

    public function setMediaId(string $mediaId): void
    {
        $this->mediaId = $mediaId;
    }

    public function getCustomFields(): ?array
    {
        return $this->customFields;
    }

    public function setCustomFields(?array $customFields): void
    {
        $this->customFields = $customFields;
    }

    public function getMedia(): ?MediaEntity
    {
        return $this->media;
    }

    public function setMedia(?MediaEntity $media): void
    {
        $this->media = $media;
    }

    public function getCreatedAt(): \DateTimeInterface
    {
        return $this->createdAt;
    }

    public function setCreatedAt(\DateTimeInterface $createdAt): void
    {
        $this->createdAt = $createdAt;
    }

    public function getUpdatedAt(): ?\DateTimeInterface
    {
        return $this->updatedAt;
    }

    public function setUpdatedAt(?\DateTimeInterface $updatedAt): void
    {
        $this->updatedAt = $updatedAt;
    }

    public function getBrainstOfferId(): string
    {
        return $this->brainstOfferId;
    }

    public function setBrainstOfferId(string $brainstOfferId): void
    {
        $this->brainstOfferId = $brainstOfferId;
    }

    public function getLanguageId(): string
    {
        return $this->languageId;
    }

    public function setLanguageId(string $languageId): void
    {
        $this->languageId = $languageId;
    }

    public function getBrainstOffer(): ?BrainstOfferEntity
    {
        return $this->brainstOffer;
    }

    public function setBrainstOffer(?BrainstOfferEntity $brainstOffer): void
    {
        $this->brainstOffer = $brainstOffer;
    }

    public function getLanguage(): ?LanguageEntity
    {
        return $this->language;
    }

    public function setLanguage(?LanguageEntity $language): void
    {
        $this->language = $language;
    }
}