<?php declare(strict_types=1);

namespace Brainst\OfferBell\Core\Content\BrainstOffer;

use Brainst\OfferBell\Core\Content\BrainstOffer\Aggregate\BrainstOfferTranslation\BrainstOfferTranslationDefinition;
use Shopware\Core\Checkout\Promotion\PromotionDefinition;
use Shopware\Core\Content\Media\MediaDefinition;
use Shopware\Core\Framework\DataAbstractionLayer\EntityDefinition;
use Shopware\Core\Framework\DataAbstractionLayer\Field\FkField;
use Shopware\Core\Framework\DataAbstractionLayer\Field\Flag\ApiAware;
use Shopware\Core\Framework\DataAbstractionLayer\Field\Flag\Inherited;
use Shopware\Core\Framework\DataAbstractionLayer\Field\Flag\Runtime;
use Shopware\Core\Framework\DataAbstractionLayer\Field\ManyToOneAssociationField;
use Shopware\Core\Framework\DataAbstractionLayer\Field\OneToOneAssociationField;
use Shopware\Core\Framework\DataAbstractionLayer\Field\TranslatedField;
use Shopware\Core\Framework\DataAbstractionLayer\Field\TranslationsAssociationField;
use Shopware\Core\Framework\DataAbstractionLayer\FieldCollection;
use Shopware\Core\Framework\DataAbstractionLayer\Field\BoolField;
use Shopware\Core\Framework\DataAbstractionLayer\Field\Flag\PrimaryKey;
use Shopware\Core\Framework\DataAbstractionLayer\Field\Flag\Required;
use Shopware\Core\Framework\DataAbstractionLayer\Field\IdField;

class BrainstOfferDefinition extends EntityDefinition
{
    public const ENTITY_NAME = 'brainst_offer';

    public function getEntityName(): string
    {
        return self::ENTITY_NAME;
    }

    public function getEntityClass(): string
    {
        return BrainstOfferEntity::class;
    }

    public function getCollectionClass(): string
    {
        return BrainstOfferCollection::class;
    }

    protected function defineFields(): FieldCollection
    {
        return new FieldCollection([
            (new IdField('id', 'id'))->addFlags(new Required(), new PrimaryKey()),
            (new FkField('promotion_id', 'promotionId', PromotionDefinition::class))->addFlags(new ApiAware()),

            (new BoolField('active', 'active'))->addFlags(new ApiAware()),
            (new TranslatedField('title'))->addFlags(new Inherited()),
            (new TranslatedField('mediaId'))->addFlags(new Inherited())->addFlags(),
//            (new TranslatedField('mediaId'))->addFlags(new Inherited())->addFlags(),
            (new TranslationsAssociationField(
                BrainstOfferTranslationDefinition::class,
                'brainst_offer_id'
            ))->addFlags(new ApiAware(), new Required()),

            new OneToOneAssociationField('promotion', 'promotion_id', 'id', PromotionDefinition::class, false),
        ]);
    }
}
