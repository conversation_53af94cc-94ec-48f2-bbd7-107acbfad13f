<?php declare(strict_types=1);

namespace Brainst\Xero\Service\Queue;

use Brainst\Xero\Model\Operation;
use Brainst\Xero\Service\Sync\TaxSyncService;
use Brainst\Xero\Traits\MessageBusDispatchTrait;
use Throwable;

/**
 * Class TaxSyncQueueService
 * @package BrainstXero
 * <AUTHOR>
 * @link www.brainstreamtechnolabs.com
 */
class TaxSyncQueueService extends AbstractSyncQueue
{
    use MessageBusDispatchTrait;

    public final const LIMIT = 10;

    /**
     * @throws Throwable
     */
    public function __invoke(): void
    {
        $iterator = $this->getIterator(self::LIMIT);

        while ($searchResult = $iterator->fetch()) {
            $this->messageDispatch(
                TaxSyncService::class,
                $searchResult->getIds(),
                Operation::update()->type(),
                true
            );
        }
    }
}
