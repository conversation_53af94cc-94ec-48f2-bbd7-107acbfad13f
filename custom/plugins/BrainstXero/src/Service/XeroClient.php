<?php declare(strict_types=1);

namespace Brainst\Xero\Service;

use Brainst\Xero\Core\Content\BrainstXero\BrainstXeroCollection;
use Brainst\Xero\Core\Content\BrainstXero\BrainstXeroEntity;
use GuzzleHttp\Client;
use Guz<PERSON><PERSON>ttp\Exception\ClientException;
use Guz<PERSON>Http\Exception\GuzzleException;
use GuzzleHttp\Psr7\Request;
use Shopware\Core\Framework\Context;
use Shopware\Core\Framework\DataAbstractionLayer\EntityRepository;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Criteria;
use Shopware\Core\Framework\DataAbstractionLayer\Search\EntitySearchResult;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Filter\EqualsAnyFilter;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Filter\EqualsFilter;
use Shopware\Core\System\SystemConfig\SystemConfigService;

/**
 *
 * Class XeroClient
 * @package BrainstXero
 * <AUTHOR>
 * @link www.brainstreamtechnolabs.com
 */
class XeroClient
{
    private const JOB_SLEEP_TIME = 60;
    private const XERO_BASE_URI = 'https://api.xero.com/api.xro/2.0/';
    private const XERO_AUTH_URI = 'https://identity.xero.com/connect/token';
    private const XERO_CONNECTION_URI = 'https://api.xero.com/connections';
    private Client $client;
    private string $clientId;
    private string $clientSecret;
    private string $redirectUri;
    private string $accessToken;
    private string $tenantId;
    private string $refreshToken;
    private ?string $salesChannelId;

    /**
     * @param SystemConfigService $configService
     * @param EntityRepository<BrainstXeroCollection> $xeroRepository
     */
    public function __construct(
        private readonly SystemConfigService $configService,
        private readonly EntityRepository    $xeroRepository
    )
    {
        $this->client = new Client(['base_uri' => self::XERO_BASE_URI]);
    }

    /**
     * @throws GuzzleException
     */
    public function connect(?string $salesChannelId, bool $setToken = true): self
    {
        $this->salesChannelId = $salesChannelId;
        $this->redirectUri = (string)$this->getConfig('redirectUri');
        $this->clientId = (string)$this->getConfig('clientId');
        $this->clientSecret = (string)$this->getConfig('clientSecret');
        if ($setToken) {
            $this->accessToken = (string)$this->getConfig('accessToken');
            $this->tenantId = (string)$this->getConfig('tenantId');
            $this->refreshToken = (string)$this->getConfig('refreshToken');
            $expiresAt = (int)$this->getConfig('expiresAt');
            if ($expiresAt < time() + 300) {
                $this->refreshToken();
            }
        }
        return $this;
    }


    /**
     * Create token for Xero client
     *
     * @param string $code
     * @return void
     * @throws GuzzleException
     */
    public function generateToken(string $code): void
    {
        $data = [
            'grant_type' => 'authorization_code',
            'code' => $code,
            'redirect_uri' => $this->redirectUri,
            'client_id' => $this->clientId,
            'client_secret' => $this->clientSecret
        ];
        $this->xeroIdentityUpdate($data);
    }

    /**
     * Refresh xero token of client
     *
     * @return void
     * @throws GuzzleException
     */
    public function refreshToken(): void
    {
        $data = [
            'grant_type' => 'refresh_token',
            'refresh_token' => $this->refreshToken,
            'client_id' => $this->clientId,
            'client_secret' => $this->clientSecret
        ];
        $this->xeroIdentityUpdate($data);
    }

    /**
     * @param string $endPoint
     * @param string $method
     * @param array<string, mixed> $data
     * @return array{Status: string, string?: array<int, array{ string: string }>}
     * @throws GuzzleException
     */
    public function call(string $endPoint, string $method = 'GET', ?array $data = null): array
    {
        $headers = [
            'Authorization' => "Bearer " . $this->accessToken,
            'Xero-Tenant-Id' => $this->tenantId,
            'Accept' => 'application/json',
            'Content-Type' => 'application/json'
        ];

        $requestData = json_encode($data) ?: null;
        $request = new Request($method, $endPoint, $headers, $requestData);

        try {
            $response = $this->client->send($request);

            /** @var array{Status: string, string?: array<int, array{string: string}>} $responseData */
            $responseData = json_decode($response->getBody()->getContents(), true) ?: [];
            return $responseData;
        } catch (ClientException $exception) {
            if ($exception->getCode() === 429) {
                $eResponse = $exception->getResponse();
                if (isset($eResponse->getHeader('X-Rate-Limit-Problem')[0], $eResponse->getHeader('Retry-After')[0]) && $eResponse->getHeader('X-Rate-Limit-Problem')[0] !== 'minute') {
                    $delayStemp = time() + (int)$eResponse->getHeader('Retry-After')[0];
                    $this->configService->set('BrainstXero.config.messageDelay', $delayStemp);
                    throw $exception;
                }
                $sleep = (int)($eResponse->getHeader('Retry-After')[0] ?? self::JOB_SLEEP_TIME);
                echo "\033[1;33mAPI reached limit, Job is going to sleep for \033[1;31m$sleep\033[1;33m seconds...\033[0m\n";
                sleep($sleep);
                echo "\033[1;32mJob has woken up and continues processing.\033[0m\n";
                return $this->call($endPoint, $method, $data);
            }
            throw $exception;
        }
    }

    /**
     * Get Sales Channel Id
     *
     * @return string|null
     */
    public function getSalesChannelId(): ?string
    {
        return $this->salesChannelId;
    }

    /**
     * Check is integration enable or not
     *
     * @param string $salesChannelId
     * @return bool
     */
    public function isIntegrationEnable(string $salesChannelId): bool
    {
        /** @var bool $enableIntegration */
        $enableIntegration = $this->configService->get('BrainstXero.config.enableIntegration', $salesChannelId);
        return $enableIntegration;
    }

    /**
     * Get sales account code for invoice line item account code
     *
     * @return string
     */
    public function getSaleAccountCode(): string
    {
        /** @var string $accountCode */
        $accountCode = $this->configService->get('BrainstXero.config.saleAccountCode', $this->salesChannelId);
        return $accountCode ?? '820';
    }

    /**
     * Get payment account code for the invoice payment
     *
     * @return string
     */
    public function getPaymentAccountCode(): string
    {
        /** @var string $accountCode */
        $accountCode = $this->configService->get('BrainstXero.config.paymentAccountCode', $this->salesChannelId);
        return $accountCode ?? '880';
    }

    /**
     * Store tokens for xero
     *
     * @param array<string, int|string> $data
     * @return void
     * @throws GuzzleException
     */
    private function xeroIdentityUpdate(array $data): void
    {
        $response = $this->client->post(self::XERO_AUTH_URI, [
            'form_params' => $data,
        ]);

        /** @var array<string, int|string> $token */
        $token = json_decode($response->getBody()->getContents(), true);
        $tokens = [
            'accessToken' => $token['access_token'],
            'refreshToken' => $token['refresh_token'],
            'expiresAt' => time() + (int)$token['expires_in']
        ];
        $tenant = $this->xeroTenantGet((string)$token['access_token']);
        $tokens = array_merge($tokens, $tenant);
        $this->saveConfig($tokens);
    }

    /**
     * Store tokens for xero
     * @param string $token
     * @return array<string, int|string>
     * @throws GuzzleException
     */
    private function xeroTenantGet(string $token): array
    {
        $formData = [
            'headers' => [
                'Authorization' => "Bearer " . $token,
                'Accept' => 'application/json'
            ]
        ];
        $response = $this->client->get(self::XERO_CONNECTION_URI, $formData);

        /** @var array<int, array<string, string>> $responseData */
        $responseData = json_decode($response->getBody()->getContents(), true);

        return ['tenantId' => $responseData[0]['tenantId']];
    }


    /**
     * Save config value for xero
     *
     * @param array<string, int|string> $config
     * @return void
     * @throws GuzzleException
     */
    private function saveConfig(array $config): void
    {
        foreach ($config as $key => $value) {
            $this->configService->set("BrainstXero.config.$key", $value, $this->salesChannelId);
        }
        $this->connect($this->salesChannelId);
    }

    /**
     * Get config value for xero
     *
     * @param string $key
     * @return string|int|bool|null
     */
    private function getConfig(string $key): string|int|bool|null
    {
        /** @var string|int|bool|null $config */
        $config = $this->configService->get("BrainstXero.config.$key", $this->salesChannelId);
        return $config;
    }


    /**
     * Update the mapping table for the xero record
     *
     * @param string $module
     * @param string $entityId
     * @param string $xeroId
     * @param Context $context
     * @param string|null $mappingId
     * @return void
     */
    public function updateMapping(string $module, string $entityId, string $xeroId, Context $context, ?string $mappingId = null): void
    {
        $data = [
            'id' => $mappingId,
            'module' => $module,
            'recordId' => $entityId,
            'xeroId' => $xeroId,
            'salesChannelId' => $this->salesChannelId
        ];
        $this->xeroRepository->upsert([$data], $context);
    }

    /**
     * Update the mapping table for the xero record
     *
     * @param string $module
     * @param array<array{entityId: string, xeroId:string, mappingId: null|string}> $entityData
     * @param Context $context
     * @return void
     */
    public function updateMappingBatch(string $module, array $entityData, Context $context): void
    {
        $updateData = [];
        $salesChannelId = $this->salesChannelId ?: null;
        foreach ($entityData as $data) {
            $updateData[] = [
                'id' => $data['mappingId'],
                'module' => $module,
                'recordId' => $data['entityId'],
                'xeroId' => $data['xeroId'],
                'salesChannelId' => $salesChannelId
            ];
        }
        $this->xeroRepository->upsert($updateData, $context);
    }

    /**
     * Find the record from mapping table if it's already exist
     *
     * @param string $module
     * @param string $entityId
     * @param Context $context
     * @return BrainstXeroEntity|null
     */
    public function findRecord(string $module, string $entityId, Context $context): ?BrainstXeroEntity
    {
        $criteria = new Criteria();
        $criteria->addFilter(new EqualsFilter('module', $module));
        $criteria->addFilter(new EqualsFilter('recordId', $entityId));
        $criteria->addFilter(new EqualsFilter('salesChannelId', $this->salesChannelId));

        /** @var BrainstXeroEntity $result */
        $result = $this->xeroRepository->search($criteria, $context)->first();
        return $result;
    }

    /**
     * Find the record from mapping table if it's already exist
     *
     * @param string $module
     * @param list<string> $entityIds
     * @param Context $context
     * @param bool $checkSalesChannel
     * @return EntitySearchResult<BrainstXeroCollection>
     */
    public function findAll(string $module, array $entityIds, Context $context, bool $checkSalesChannel = false): EntitySearchResult
    {
        $criteria = new Criteria();
        $criteria->addFilter(new EqualsFilter('module', $module));
        $criteria->addFilter(new EqualsAnyFilter('recordId', $entityIds));
        if ($checkSalesChannel) {
            $criteria->addFilter(new EqualsFilter('salesChannelId', $this->salesChannelId));
        }

        return $this->xeroRepository->search($criteria, $context);
    }

    /**
     * Remove from the mapping table for the xero record
     *
     * @param string $entityId
     * @param Context $context
     * @return void
     */
    public function removeMapping(string $entityId, Context $context): void
    {
        /** @var array<array<string, mixed>> $ids */
        $ids = [['id' => $entityId]];
        $this->xeroRepository->delete($ids, $context);
    }
}
