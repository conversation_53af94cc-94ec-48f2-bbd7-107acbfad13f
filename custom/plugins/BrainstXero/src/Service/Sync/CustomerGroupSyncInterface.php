<?php declare(strict_types=1);

namespace Brainst\Xero\Service\Sync;

/**
 * Class CustomerGroupSyncInterface
 * @package BrainstXero
 * <AUTHOR>
 * @link www.brainstreamtechnolabs.com
 */
interface CustomerGroupSyncInterface extends SyncServiceInterface
{
    /**
     * Assign group to contact with remove option
     *
     * @param string $contactGroupId
     * @param string $contactId
     * @param string|null $removeGroup
     * @return void
     */
    public function assignContact(string $contactGroupId, string $contactId, ?string $removeGroup = null): void;
}