<?php declare(strict_types=1);

namespace Brainst\Xero\Subscriber;

use Brainst\Xero\Service\Dispatch\DispatchInterface;
use Brainst\Xero\Traits\EntityEventHandlerTrait;
use Shopware\Core\Checkout\Customer\CustomerEvents;
use Shopware\Core\Checkout\Order\OrderEvents;
use Shopware\Core\Content\Product\ProductEvents;
use Shopware\Core\System\Currency\CurrencyEvents;
use Shopware\Core\System\Tax\TaxEvents;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;

/**
 * Class XeroSubscriber
 * @package BrainstXero
 * <AUTHOR>
 * @link www.brainstreamtechnolabs.com
 */
class XeroSubscriber implements EventSubscriberInterface
{
    use EntityEventHandlerTrait;

    public function __construct(
        protected DispatchInterface $dispatch
    )
    {
    }

    public static function getSubscribedEvents(): array
    {
        return [
            CustomerEvents::CUSTOMER_WRITTEN_EVENT => 'OnEntityWritten',
            CustomerEvents::CUSTOMER_DELETED_EVENT => 'onEntityDeleted',
            CustomerEvents::CUSTOMER_GROUP_WRITTEN_EVENT => 'OnEntityWritten',
            CustomerEvents::CUSTOMER_GROUP_DELETED_EVENT => 'onEntityDeleted',
            ProductEvents::PRODUCT_WRITTEN_EVENT => 'OnEntityWritten',
            ProductEvents::PRODUCT_DELETED_EVENT => 'onEntityDeleted',
            TaxEvents::TAX_WRITTEN_EVENT => 'OnEntityWritten',
            TaxEvents::TAX_DELETED_EVENT => 'onEntityDeleted',
            OrderEvents::ORDER_TRANSACTION_WRITTEN_EVENT => 'OnEntityWritten',
            OrderEvents::ORDER_DELETED_EVENT => 'onEntityDeleted',
            CurrencyEvents::CURRENCY_WRITTEN_EVENT => 'OnEntityWritten',
        ];
    }
}