<?php declare(strict_types=1);

namespace Brainst\Xero\Traits;

use Brainst\Xero\Helper\ServiceHelper;
use Brainst\Xero\Model\Operation;
use Brainst\Xero\Service\Dispatch\DispatchInterface;
use Shopware\Core\Framework\DataAbstractionLayer\EntityWriteResult;
use Shopware\Core\Framework\DataAbstractionLayer\Event\EntityDeletedEvent;
use Shopware\Core\Framework\DataAbstractionLayer\Event\EntityWrittenEvent;

/**
 * Class EntityEventHandlerTrait
 * @package BrainstXero
 * <AUTHOR>
 * @link www.brainstreamtechnolabs.com
 */
trait EntityEventHandlerTrait
{
    use EntityIdRetrieverTrait;

    /**
     * Set connection for dispatch
     *
     * @param DispatchInterface $dispatch
     * @return void
     */
    public function setDispatch(DispatchInterface $dispatch): void
    {
        $this->dispatch = $dispatch;
    }

    /**
     * Add or update entity in xero when entity added or updated
     *
     * @param EntityWrittenEvent $event
     * @return void
     */
    public function OnEntityWritten(EntityWrittenEvent $event): void
    {

        $entityIds = $this->getEntityIdsByOperation($event, [
            EntityWriteResult::OPERATION_INSERT,
            EntityWriteResult::OPERATION_UPDATE,
        ]);

        $this->dispatchService($event->getEntityName(), $entityIds, Operation::update());
    }

    /**
     * Delete entity from xero
     *
     * @param EntityDeletedEvent $event
     * @return void
     */
    public function onEntityDeleted(EntityDeletedEvent $event): void
    {
        $deletedIds = $this->getEntityIdsByOperation($event);

        $this->dispatchService($event->getEntityName(), $deletedIds, Operation::delete());
    }

    /**
     * @param string $entityName
     * @param array<string> $entityIds
     * @param Operation $operation
     * @return void
     */
    private function dispatchService(string $entityName, array $entityIds, Operation $operation): void
    {
        if (empty($entityIds) || !ServiceHelper::canSync($entityName)) {
            return;
        }

        $serviceClass = ServiceHelper::getClass($entityName);
        ($this->dispatch)($serviceClass, $entityIds, $operation);
    }
}