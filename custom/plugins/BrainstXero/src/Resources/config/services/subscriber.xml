<?xml version="1.0" ?>
<container xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
           xmlns="http://symfony.com/schema/dic/services"
           xsi:schemaLocation="http://symfony.com/schema/dic/services http://symfony.com/schema/dic/services/services-1.0.xsd">
    <services>
        <service id="Brainst\Xero\Subscriber\XeroSubscriber">
            <argument type="service" id="brainst.xero.dispatch"/>
            <tag name="kernel.event_subscriber"/>
        </service>
    </services>
</container>