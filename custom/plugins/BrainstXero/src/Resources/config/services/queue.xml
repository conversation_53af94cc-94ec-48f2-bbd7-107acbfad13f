<?xml version="1.0" ?>
<container xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
           xmlns="http://symfony.com/schema/dic/services"
           xsi:schemaLocation="http://symfony.com/schema/dic/services http://symfony.com/schema/dic/services/services-1.0.xsd">
    <services>
        <service id="Brainst\Xero\Service\Queue\CurrencySyncQueueService">
            <argument type="service" id="messenger.bus.shopware"/>
            <argument type="service" id="currency.repository"/>
        </service>

        <service id="Brainst\Xero\Service\Queue\CustomerSyncQueueService">
            <argument type="service" id="messenger.bus.shopware"/>
            <argument type="service" id="customer.repository"/>
        </service>

        <service id="Brainst\Xero\Service\Queue\CustomerGroupSyncQueueService">
            <argument type="service" id="messenger.bus.shopware"/>
            <argument type="service" id="customer_group.repository"/>
        </service>

        <service id="Brainst\Xero\Service\Queue\TaxSyncQueueService">
            <argument type="service" id="messenger.bus.shopware"/>
            <argument type="service" id="tax.repository"/>
        </service>

        <service id="Brainst\Xero\Service\Queue\ProductSyncQueueService">
            <argument type="service" id="messenger.bus.shopware"/>
            <argument type="service" id="product.repository"/>
        </service>

        <service id="Brainst\Xero\Service\Queue\InvoiceSyncQueueService">
            <argument type="service" id="messenger.bus.shopware"/>
            <argument type="service" id="order.repository"/>
        </service>
    </services>
</container>
