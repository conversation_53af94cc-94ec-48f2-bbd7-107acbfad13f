<?xml version="1.0" ?>
<container xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
           xmlns="http://symfony.com/schema/dic/services"
           xsi:schemaLocation="http://symfony.com/schema/dic/services http://symfony.com/schema/dic/services/services-1.0.xsd">
    <services>
        <service id="Brainst\Xero\Controller\Api\XeroApiController">
            <argument type="service" id="Shopware\Core\System\SystemConfig\SystemConfigService"/>
            <argument type="service" id="Brainst\Xero\Util\Logger" on-invalid="ignore"/>
            <argument type="service" id="Brainst\Xero\Service\XeroClient"/>
            <call method="setContainer">
                <argument type="service" id="service_container"/>
            </call>
            <tag name="controller.service_arguments"/>
        </service>

        <service id="Brainst\Xero\Controller\Api\XeroRedirectController">
            <argument type="service" id="Brainst\Xero\Util\Logger" on-invalid="ignore"/>
            <argument type="service" id="Brainst\Xero\Service\XeroClient"/>
            <call method="setContainer">
                <argument type="service" id="service_container"/>
            </call>
            <tag name="controller.service_arguments"/>
        </service>

        <service id="Brainst\Xero\Controller\Api\SynchronizationController" public="true">
            <argument type="service" id="Shopware\Core\System\SystemConfig\SystemConfigService"/>
            <argument type="service" id="Brainst\Xero\Util\Logger" on-invalid="ignore"/>
            <argument type="service" id="system_config.repository"/>
            <argument type="service" id="Brainst\Xero\Service\Queue\CurrencySyncQueueService"/>
            <argument type="service" id="Brainst\Xero\Service\Queue\CustomerGroupSyncQueueService"/>
            <argument type="service" id="Brainst\Xero\Service\Queue\CustomerSyncQueueService"/>
            <argument type="service" id="Brainst\Xero\Service\Queue\ProductSyncQueueService"/>
            <argument type="service" id="Brainst\Xero\Service\Queue\TaxSyncQueueService"/>
            <argument type="service" id="Brainst\Xero\Service\Queue\InvoiceSyncQueueService"/>
            <tag name="controller.service_arguments"/>
        </service>
    </services>
</container>