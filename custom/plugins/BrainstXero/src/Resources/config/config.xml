<?xml version="1.0" encoding="UTF-8"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="https://raw.githubusercontent.com/shopware/shopware/trunk/src/Core/System/SystemConfig/Schema/config.xsd">
    <card>
        <title>General configuration</title>
        <title lang="de-DE">Allgemeine Konfiguration</title>
        <input-field type="bool">
            <name>enableIntegration</name>
            <label>Enable the integration</label>
            <label lang="de-DE">Aktivieren Sie die Integration</label>
            <helpText>Activate the integration and start data sync</helpText>
            <helpText lang="de-DE">Aktivieren Sie die Integration und starten Sie die Datensynchronisierung</helpText>
            <defaultValue>false</defaultValue>
        </input-field>

        <input-field type="bool">
            <name>initialSync</name>
            <label>Enable the initial sync by GUI</label>
            <label lang="de-DE">Aktivieren Sie die anfängliche Synchronisierung per GUI</label>
            <helpText>Activate the initial sync with Xero and do the sync by button in extension settings. Please
                be cautious while enabling it if you have done the initial sync, If you have done sync and tried
                multiple times then the queue will get busy for a long time again
            </helpText>
            <helpText lang="de-DE">Aktivieren Sie die anfängliche Synchronisierung mit Xero und führen Sie die
                Synchronisierung über die Schaltfläche in den Erweiterungseinstellungen durch. Bitte seien Sie beim
                Aktivieren vorsichtig, wenn Sie die erste Synchronisierung durchgeführt haben. Wenn Sie die
                Synchronisierung durchgeführt und es mehrmals versucht haben, wird die Warteschlange erneut für eine
                lange Zeit beschäftigt sein
            </helpText>
            <defaultValue>true</defaultValue>
        </input-field>
    </card>

    <card>
        <title>Xero configuration</title>
        <title lang="de-DE">Xero-Konfiguration</title>

        <input-field type="url">
            <name>redirectUri</name>
            <copyable>true</copyable>
            <label>Redirect URL</label>
            <label lang="de-DE">Weiterleitungs-URL</label>
            <helpText>Set this redirect URL in the Xero configuration redirect URI</helpText>
            <helpText lang="de-DE">Legen Sie diese Umleitungs-URL in der Umleitungs-URI der Xero-Konfiguration fest</helpText>
            <disabled>true</disabled>
        </input-field>

        <input-field type="text">
            <name>clientId</name>
            <required>true</required>
            <label>Client Id</label>
            <label lang="de-DE">Kunden-ID</label>
            <helpText>Client ID from the app's keys tab</helpText>
            <helpText lang="de-DE">Client-ID aus der Registerkarte „Schlüssel“ der App</helpText>
        </input-field>

        <input-field type="password">
            <name>clientSecret</name>
            <required>true</required>
            <label>Client Secret</label>
            <label lang="de-DE">Client-Geheimnis</label>
            <helpText>Client Secret from the app's keys tab</helpText>
            <helpText lang="de-DE">Client-Geheimnis auf der Registerkarte „Schlüssel“ der App</helpText>
        </input-field>

        <component name="brainst-xero-api-verify-button">
            <name>xeroApiVerify</name>
        </component>
    </card>
    <card>
        <title>Xero Accounts</title>
        <title lang="de-DE">Xero-Konten</title>

        <input-field type="single-select">
            <name>saleAccountCode</name>
            <options>
                <option>
                    <id>820</id>
                    <name>Sales Tax</name>
                    <name lang="de-DE">Sales Tax</name>
                </option>
            </options>
            <label>Sales Account Code</label>
            <label lang="de-DE">Verkaufskontocode</label>
            <defaultValue>820</defaultValue>
        </input-field>

        <input-field type="single-select">
            <name>paymentAccountCode</name>
            <options>
                <option>
                    <id>880</id>
                    <name>Owner A Drawings</name>
                    <name lang="de-DE">Owner A Drawings</name>
                </option>
            </options>
            <label>Payment Account Code</label>
            <label lang="de-DE">Zahlungskontocode</label>
            <defaultValue>880</defaultValue>
        </input-field>
    </card>
</config>

