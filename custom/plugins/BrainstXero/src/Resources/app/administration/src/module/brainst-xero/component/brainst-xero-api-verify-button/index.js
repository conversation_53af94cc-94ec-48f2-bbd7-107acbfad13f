const {Component, Mixin} = Shopware;
import template from './brainst-xero-api-verify-button.html.twig';

/**
 * Registers the `brainst-xero-api-verify-button` component in the Shopware administration.
 *
 * @type {Object}
 */
Component.register('brainst-xero-api-verify-button', {
    template: template,

    inject: ['brainstXeroApiService', 'systemConfigApiService'],

    mixins: [
        Mixin.getByName('notification')
    ],

    data() {
        return {
            isLoading: false,
            isVerifyDone: false,
            fields: [
                'BrainstXero.config.clientId',
                'BrainstXero.config.clientSecret'
            ],
            resetFields: [
                'BrainstXero.config.clientId',
                'BrainstXero.config.clientSecret',
                'BrainstXero.config.accessToken',
                'BrainstXero.config.refreshToken',
                'BrainstXero.config.tenantId',
                'BrainstXero.config.expiresAt'
            ],
            originalSyncData: {}
        };
    },

    computed: {
        pluginConfig() {
            let $parent = this.$parent;
            while ($parent.actualConfigData === undefined) {
                $parent = $parent.$parent;
            }
            return $parent;
        },
        salesChannelId() {
            return this.pluginConfig.currentSalesChannelId;
        },
        isInheritField() {
            return this.salesChannelId !== null;
        },

        actualCurrentData() {
            return this.pluginConfig.actualConfigData[this.salesChannelId];
        },
        actualInheritedData() {
            return this.pluginConfig.actualConfigData[null];
        },
        isInherited() {
            if (this.salesChannelId === null) {
                return false;
            }
            return !this.fields.every(field => this.actualCurrentData[field]);
        },
    },

    async mounted() {
        const redirectUri = this.pluginConfig.actualConfigData[null]['BrainstXero.config.redirectUri'];
        if (!redirectUri || !(redirectUri && this.salesChannelId)) {
            this.pluginConfig.actualConfigData[null]['BrainstXero.config.redirectUri'] = (await this.brainstXeroApiService.getRedirectUrl()).redirectURL
        }
        this.getXeroAccounts();
    },

    methods: {
        verifyFinish() {
            this.isVerifyDone = false;
        },
        verifyApi() {
            const self = this;
            self.isLoading = true;

            self.systemConfigApiService.batchSave(self.pluginConfig.actualConfigData);
            const pluginConfig = {
                currentSalesChannelId: self.salesChannelId
            }

            self.brainstXeroApiService
                .verifyConfig(pluginConfig)
                .then((res) => {
                    if (res.isValid) {
                        self.openOAuthWindow(res.authURL);
                    } else {
                        self.verificationFailed();
                    }
                })
                .catch((error) => {
                    console.log(error);
                    self.verificationFailed();
                });
        },
        openOAuthWindow(authURL) {
            // Open a new popup window for OAuth
            const self = this;
            const width = 300;
            const height = 400;
            const left = (screen.width / 2) - (width / 2);
            const top = (screen.height / 2) - (height / 2);
            const oauthWindow = window.open(
                authURL,
                'oauthPopup',
                `width=${width},height=${height},top=${top},left=${left},resizable=yes,scrollbars=yes`,
            );

            const checkWindowRedirect = setInterval(function () {
                try {
                    if (oauthWindow.closed) {
                        clearInterval(checkWindowRedirect);
                        self.isLoading = false;
                        return;
                    }

                    if (oauthWindow.location.origin === window.location.origin) {
                        const status = oauthWindow.document.getElementById('verification-status').innerHTML;
                        if (status === 'complete') {
                            oauthWindow.close();
                            self.createNotificationSuccess({
                                title: self.$tc('brainst-xero.api-verify-button.title'),
                                message: self.$tc('brainst-xero.api-verify-button.success')
                            });
                            self.isVerifyDone = true;
                            self.getXeroToken();
                            self.getXeroAccounts();
                        } else {
                            oauthWindow.close();
                            self.verificationFailed();
                        }
                    }
                } catch (e) {
                }
            }, 1000, self);
        },
        verificationFailed() {
            this.createNotificationError({
                title: this.$tc('brainst-xero.api-verify-button.title'),
                message: this.$tc('brainst-xero.api-verify-button.error')
            });
            this.isLoading = false;
        },
        getXeroToken() {
            const self = this;
            self.brainstXeroApiService
                .getXeroToken(self.salesChannelId)
                .then((res) => {
                    if (res.isValid) {
                        self.actualCurrentData['BrainstXero.config.accessToken'] = res.accessToken;
                        self.actualCurrentData['BrainstXero.config.refreshToken'] = res.refreshToke;
                    } else {
                        console.log(res.message)
                    }
                })
                .catch((error) => {
                    console.log(error);
                });
        },
        getXeroAccounts() {
            const self = this;
            self.brainstXeroApiService
                .getXeroAccounts(self.pluginConfig.currentSalesChannelId)
                .then((res) => {
                    if (res.isValid && res.accounts) {
                        this.setConfigOptions({'BrainstXero.config.saleAccountCode': res.accounts});
                        const paymentAccounts = res.accounts.filter(account => account.payment === true);
                        this.setConfigOptions({'BrainstXero.config.paymentAccountCode': paymentAccounts});
                    } else {
                        console.log(res.message)
                    }
                })
                .catch((error) => {
                    console.log(error);
                });
        },
        restoreInheritance() {
            this.originalSyncData = this.actualCurrentData;
            this.resetFields.forEach(field => this.actualCurrentData[field] = null);
        },
        removeInheritance() {
            this.resetFields.forEach(field => {
                this.actualCurrentData[field] = this.actualCurrentData[field] || this.originalSyncData[field] || this.actualInheritedData[field];
            });
        },

        setConfigOptions(options, checkDisable = false, disable = false) {
            const keys = Object.keys(options);
            (this.pluginConfig.config).forEach(section => {
                section.elements?.forEach(element => {
                    const optionValues = options[element.name];
                    if (keys.includes(element.name) && optionValues && optionValues.length > 0) {
                        element.config.options = optionValues;
                        if (checkDisable) {
                            element.config.disabled = disable;
                        }
                    }
                });
            });
        },
    }
})
