{"brainst-xero": {"title": "Xero", "description": "Shopware Xero synchronization data of customers, product, vendors, sales receipt, refund receipt, payment methods and orders.", "api-verify-button": {"title": "Connection", "label": "Verify", "success": "Verification has been successfully completed", "error": "Connection could not be established. Please check the access data"}, "api-redirect-url": {"helpText": "Copy the redirect URI from here and add in the xero before verification.", "label": "You Need to Set this below Redirect URL to Xero setting on <a target=\"_blank\" title=\"Set xero redirect URIs\" rel=\"noopener\" href=\"https://developer.intuit.com/app/developer/qbo/docs/develop/authentication-and-authorization/set-redirect-uri\"> Click here for more info</a>"}, "index": {"title": "Xero Settings"}, "synchronize-button": {"title": "Sync to xero", "description": "This will take some time to process the queue, so please do it when your site usage is minimal for some time.<br/><br/>Make sure all configuration in config file are set and verification process is completed before syncing data.<br/><br/>This sync all data for all sales channel configurations. <br/>Multiple time sync can lead to data duplication issues in Xero.", "success": "All entities are queued for synchronization", "error_1": "Please setup the xero configuration, then try again", "error_2": "Something went wrong with the synchronization, please try again", "error_3": "You already have done or in the process of initial syncing"}}}