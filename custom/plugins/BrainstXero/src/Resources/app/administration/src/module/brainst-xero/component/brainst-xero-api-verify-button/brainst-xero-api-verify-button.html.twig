<template>
    <div class="sw-field">
        <sw-inheritance-switch
                v-if="isInheritField"
                :disabled="false"
                class="sw-inheritance-switch sw-field__inheritance-icon"
                :is-inherited="isInherited"
                @inheritance-restore="restoreInheritance"
                @inheritance-remove="removeInheritance"
                :label="tested"
        ></sw-inheritance-switch>
        <sw-button-process
                :disabled="isInherited"
                :isLoading="isLoading"
                :processSuccess="isVerifyDone"
                variant="primary"
                @process-finish="verifyFinish"
                @click="verifyApi"
        >{{ $tc('brainst-xero.api-verify-button.label') }}</sw-button-process>
    </div>
</template>