<?php declare(strict_types=1);

namespace Brainst\Xero\Controller\Api;

use Brainst\Xero\Service\XeroClient;
use Brainst\Xero\Traits\ErrorLoggerTrait;
use GuzzleHttp\Exception\GuzzleException;
use Psr\Log\LoggerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

/**
 * Class XeroRedirectController
 * @package BrainstXero
 * <AUTHOR>
 * @link www.brainstreamtechnolabs.com
 */
#[Route(defaults: ['_routeScope' => ['api']])]
class XeroRedirectController extends AbstractController
{
    use ErrorLoggerTrait;

    public function __construct(
        protected LoggerInterface   $logger,
        private readonly XeroClient $client
    )
    {
    }

    /**
     * Get token from xero redirected link and set in config
     *
     * @param Request $request
     * @return Response
     */
    #[Route(path: '/api/brainst-xero/auth-redirect', name: 'api.brainst.xero.auth.redirect', defaults: ['auth_required' => false], methods: ['GET'])]
    public function xeroAuthRedirect(Request $request): Response
    {
        $code = (string)$request->query->get('code');

        /** @var string|null $salesChannelId */
        $salesChannelId = $request->query->get('state');

        $status = 'failed';
        try {
            $this->client->connect($salesChannelId, false)->generateToken($code);
            $status = 'complete';
        } catch (GuzzleException  $exception) {
            $this->logError($exception);
        }

        return new Response("<html lang='en'><body><p id='verification-status'>$status</p><p id='verification-message'>Verification process $status.</p></body></html>");
    }
}
