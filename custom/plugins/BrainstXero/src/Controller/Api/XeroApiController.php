<?php declare(strict_types=1);

namespace Brainst\Xero\Controller\Api;

use Brainst\Xero\Service\XeroClient;
use Brainst\Xero\Traits\ErrorLoggerTrait;
use GuzzleHttp\Exception\GuzzleException;
use Psr\Log\LoggerInterface;
use Shopware\Core\Framework\Validation\DataBag\RequestDataBag;
use Shopware\Core\System\SystemConfig\SystemConfigService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;
use Throwable;

/**
 * Class XeroApiController
 * @package BrainstXero
 * <AUTHOR>
 * @link www.brainstreamtechnolabs.com
 */
#[Route(defaults: ['_routeScope' => ['administration']])]
class XeroApiController extends AbstractController
{
    use ErrorLoggerTrait;

    public function __construct(
        private readonly SystemConfigService $configService,
        protected LoggerInterface            $logger,
        private readonly XeroClient          $xeroClient,
    )
    {
    }

    /**
     * Verify connection for xero by given dataBag
     *
     * @param RequestDataBag $dataBag
     * @return JsonResponse
     */
    #[Route(path: '/api/_action/brainst-xero/verify', name: 'api.brainst.xero.verify', defaults: ['XmlHttpRequest' => true], methods: ['POST'])]
    public function authenticate(RequestDataBag $dataBag): JsonResponse
    {
        try {
            $salesChannel = $dataBag->get('currentSalesChannelId');
            $salesChannel = is_scalar($salesChannel) ? (string)$salesChannel : null;
            $clientId = $dataBag->get('BrainstXero.config.clientId', $salesChannel);
            $redirectUri = $dataBag->get('BrainstXero.config.redirectUri', $salesChannel);
            $scope = 'openid email profile offline_access accounting.settings accounting.transactions accounting.contacts accounting.journals.read accounting.reports.read';

            $url = 'https://login.xero.com/identity/connect/authorize?' . http_build_query([
                    'response_type' => 'code',
                    'client_id' => $clientId,
                    'redirect_uri' => $redirectUri,
                    'scope' => $scope,
                    'state' => $salesChannel,
                ]);

            return new JsonResponse([
                'isValid' => true,
                'authURL' => $url,
            ]);
        } catch (Throwable $exception) {
            return $this->handleException($exception);
        }
    }

    /**
     * Get redirect URI for xero OAuth2 redirection
     *
     * @return JsonResponse
     */
    #[Route(path: '/api/_action/brainst-xero/redirect-url', name: 'api.brainst.xero.redirect-url', defaults: ['XmlHttpRequest' => true], methods: ['GET'])]
    public function getRedirectUrl(): JsonResponse
    {
        $redirectUrl = $this->generateUrl('api.brainst.xero.auth.redirect', referenceType: UrlGeneratorInterface::ABSOLUTE_URL);
        return new JsonResponse(['isValid' => true, 'redirectURL' => $redirectUrl]);
    }

    /**
     * Get xero token list by sales channel id
     *
     * @param string|null $salesChannel
     * @return JsonResponse
     */
    #[Route(path: '/api/_action/brainst-xero/tokens/{salesChannel?}', name: 'api.brainst.xero.tokens', defaults: ['XmlHttpRequest' => true], methods: ['GET'])]
    public function xeroTokens(?string $salesChannel = null): JsonResponse
    {
        try {
            return new JsonResponse([
                'isValid' => true,
                'accessToken' => $this->configService->get('BrainstXero.config.accessToken', $salesChannel),
                'refreshToken' => $this->configService->get('BrainstXero.config.refreshToken', $salesChannel),
            ]);
        } catch (Throwable $exception) {
            return $this->handleException($exception);
        }
    }

    /**
     * Get xero accounts list
     *
     * @param string|null $salesChannel
     * @return JsonResponse
     */
    #[Route(path: '/api/_action/brainst-xero/accounts/{salesChannel?}', name: 'api.brainst.xero.accounts', defaults: ['XmlHttpRequest' => true], methods: ['GET'])]
    public function xeroAccounts(?string $salesChannel = null): JsonResponse
    {
        try {
            $accounts = $this->getAccountsList($salesChannel);
            return new JsonResponse(['isValid' => true, 'accounts' => $accounts]);
        } catch (Throwable $exception) {
            return $this->handleException($exception);
        }
    }

    /**
     * Failed exception
     *
     * @param Throwable $exception
     * @return JsonResponse
     */
    private function handleException(Throwable $exception): JsonResponse
    {
        $this->logError($exception);
        return new JsonResponse(['isValid' => false, 'message' => $exception->getMessage()]);
    }

    /**
     * get account list array
     *
     * @return array<array{id : string|false, name:array<string>} >
     * @throws GuzzleException
     */
    private function getAccountsList(?string $salesChannel): array
    {
        $client = $this->xeroClient->connect($salesChannel);
        $data = $client->call('Accounts');
        if (!isset($data['Accounts'])) {
            return [];
        }
        /** @var array<array{Status:string, Code: string, Name: string, EnablePaymentsToAccount: bool, Type: string}> $accounts */
        $accounts = $data['Accounts'];
        $accountList = [];

        foreach ($accounts as $account) {
            if ($account['Status'] === 'ACTIVE') {
                $accountList[] = [
                    'id' => $account['Code'],
                    'name' => [
                        'de-DE' => $account['Name'],
                        'en-GB' => $account['Name']
                    ],
                    'payment' => $account['EnablePaymentsToAccount'] || $account['Type'] === 'BANK'
                ];
            }
        }
        return $accountList;
    }
}
