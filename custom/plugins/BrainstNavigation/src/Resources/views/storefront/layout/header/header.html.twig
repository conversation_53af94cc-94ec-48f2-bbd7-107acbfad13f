{% sw_extends '@Storefront/storefront/layout/header/header.html.twig' %}
{# {% set feature66 = feature('v6.6.0.0') ? feature('v6.6.0.0') : false %} #}
{% block layout_header_logo %}
    {{ parent() }}
    {% if config('BrainstNavigation.config.desktopOffCanvas') %}
        <div class="col-lg-auto d-none d-lg-block">
            {% block layout_header_navigation_toggle_desktop %}
                <div class="nav-main-toggle">
                    {% block layout_header_navigation_toggle_desktop_button %}
                        <button
                                class="btn nav-main-toggle-btn header-actions-btn"
                                type="button"
                                data-off-canvas-menu="true"
                                data-offcanvas-menu="true"
                                aria-label="{{ "general.menuLink"|trans|striptags }}"
                        >
                            {% block layout_header_navigation_toggle_desktop_button_icon %}
                                {% sw_icon 'stack' %}
                            {% endblock %}
                        </button>
                    {% endblock %}
                </div>
            {% endblock %}
        </div>
    {% endif %}
{% endblock %}
