{% set desktopImageBorderRadious = noRounded ? '' :'border-radius: ' ~  config("BrainstNavigation.config.desktopImageRoundedCorners") ~  'px;' %}
{% set navigationMaxDepth = 5 %}

{% if not level %}
    {% set level = 0 %}
{% endif %}

{% set customImage = config('BrainstNavigation.config.noImageMedia') %}
{% set sizes = {
    'default': '310px'
} %}
{% set attributes = {
    'style': desktopImageBorderRadious,
    'class': 'navigation-flyout-teaser-image',
    'alt': (category.media.translated.alt ?: ''),
    'title': (category.media.translated.title ?: ''),
    'data-object-fit': 'cover',
    'loading': 'lazy'
} %}

<a class="navigation-flyout-teaser-image-container
    navigation-flyout-teaser-image-container-customize
    {% if defaultHidden %}hidden{% endif %}"
   id="{{ category.id }}"
   data-parent-image-id="{{ parentImageId??"" }}"
   href="{{ category_url(category) }}"
   {% if category_linknewtab(category) %}target="_blank"
   {% if category.linkType == "external" %}rel="noopener noreferrer"{% endif %}
        {% endif %}
   title="{{ name }}">
    {% if category.media %}
        {% sw_thumbnails 'navigation-flyout-teaser-image-thumbnails' with {
            media: category.media,
            sizes: sizes,
            attributes: attributes
        } %}
    {% elseif config('BrainstNavigation.config.showNoImage') %}
        {% if customImage %}
            {% set mediaCollection = searchMedia([customImage], context.context) %}
            {% set media = mediaCollection.get(customImage) %}
            {% sw_thumbnails 'navigation-flyout-teaser-image-thumbnails' with {
                media: media,
                sizes: sizes,
                attributes: attributes
            } %}
        {% else %}
            <img src="{{ asset('bundles/brainstnavigation/placeholder-image.png', 'asset') }}"
                 style="{{ desktopImageBorderRadious }}"
                 alt="{{ (category.media.translated.alt ?: '') }}"
                 title="{{ (category.media.translated.title ?: '') }}"
                 class="navigation-flyout-teaser-image" data-object-fit="cover"
                 loading="lazy"
                 sizes="310px">
        {% endif %}
    {% endif %}
</a>

{% for newCategory in navigationTree.children %}
    {% if config('BrainstNavigation.config.desktopHoverImageChange') and level < navigationMaxDepth %}
        {% sw_include '@Storefront/storefront/layout/navigation/flyout-image.html.twig'
            with {
            navigationTree: newCategory,
            category: newCategory.category,
            name: newCategory.category.getName(),
            defaultHidden: true,
            parentImageId: parentImageId ?? category.id, level: level + 1
        } %}
    {% endif %}
{% endfor %}
