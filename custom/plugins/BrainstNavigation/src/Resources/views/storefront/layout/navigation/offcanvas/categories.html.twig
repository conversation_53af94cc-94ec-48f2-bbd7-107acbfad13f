{# @var navigation \Shopware\Core\Content\Category\Tree\Tree #}
{% set activeId = navigation.active.id ?? context.salesChannel.navigationCategoryId %}
{% set isRoot = activeId == context.salesChannel.navigationCategoryId %}
{% set children = navigation.getChildren(activeId) %}
{% set active = navigation.active %}
{% set showImages = (active.customFields.show_subcategory_images or config('BrainstNavigation.config.bsShowImagesForAll')) ? true : false %}
{% set inlineImage =  config('BrainstNavigation.config.offCanvasInlineImage') %}

{% block layout_navigation_offcanvas_navigation_categories %}
    <div class="navigation-offcanvas-container js-navigation-offcanvas">
        <div class="navigation-offcanvas-overlay-content js-navigation-offcanvas-overlay-content">
            {% if not isRoot %}
                {% sw_include '@Storefront/storefront/layout/navigation/offcanvas/back-link.html.twig' with { item: active } %}
            {% endif %}

            {% if not isRoot and page.navigation.active.type != "folder" %}
                {% sw_include '@Storefront/storefront/layout/navigation/offcanvas/show-active-link.html.twig' with { item: active } %}
            {% endif %}

            <ul class="list-unstyled navigation-offcanvas-list 
                {% if showImages %}bs-image-category {% endif %}
                {% if inlineImage %}bs-inline-image {% endif %}">
                {# @var item \Shopware\Core\Content\Category\Tree\TreeItem #}
                {% for item in children.tree %}
                    <li class="navigation-offcanvas-list-item">
                        {% sw_include '@Storefront/storefront/layout/navigation/offcanvas/item-link.html.twig' with { item: item, activeId: activeId, showImage: showImages } %}
                    </li>
                {% endfor %}
            </ul>
        </div>
    </div>
{% endblock %}
