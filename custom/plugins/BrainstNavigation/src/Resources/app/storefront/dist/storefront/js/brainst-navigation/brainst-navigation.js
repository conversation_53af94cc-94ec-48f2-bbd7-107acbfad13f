(()=>{"use strict";var e={857:e=>{var t=function(e){var t;return!!e&&"object"==typeof e&&"[object RegExp]"!==(t=Object.prototype.toString.call(e))&&"[object Date]"!==t&&e.$$typeof!==s},s="function"==typeof Symbol&&Symbol.for?Symbol.for("react.element"):60103;function i(e,t){return!1!==t.clone&&t.isMergeableObject(e)?o(Array.isArray(e)?[]:{},e,t):e}function n(e,t,s){return e.concat(t).map(function(e){return i(e,s)})}function r(e){return Object.keys(e).concat(Object.getOwnPropertySymbols?Object.getOwnPropertySymbols(e).filter(function(t){return Object.propertyIsEnumerable.call(e,t)}):[])}function a(e,t){try{return t in e}catch(e){return!1}}function o(e,s,l){(l=l||{}).arrayMerge=l.arrayMerge||n,l.isMergeableObject=l.isMergeableObject||t,l.cloneUnlessOtherwiseSpecified=i;var c,h,d=Array.isArray(s);return d!==Array.isArray(e)?i(s,l):d?l.arrayMerge(e,s,l):(h={},(c=l).isMergeableObject(e)&&r(e).forEach(function(t){h[t]=i(e[t],c)}),r(s).forEach(function(t){(!a(e,t)||Object.hasOwnProperty.call(e,t)&&Object.propertyIsEnumerable.call(e,t))&&(a(e,t)&&c.isMergeableObject(s[t])?h[t]=(function(e,t){if(!t.customMerge)return o;var s=t.customMerge(e);return"function"==typeof s?s:o})(t,c)(e[t],s[t],c):h[t]=i(s[t],c))}),h)}o.all=function(e,t){if(!Array.isArray(e))throw Error("first argument should be an array");return e.reduce(function(e,s){return o(e,s,t)},{})},e.exports=o},381:(e,t,s)=>{s.r(t),s.d(t,{default:()=>n});var i=s(568);class n extends i.Z{init(){let e=this.el.getAttribute("data-image-id"),t=document.getElementById(e),s=t?t.getAttribute("data-parent-image-id"):null,i=document.getElementById(s);this.el.addEventListener("mouseenter",n=>{e!==s&&(i&&i.classList&&i.classList.add("hidden"),t&&t.classList&&t.classList.remove("hidden"))}),this.el.addEventListener("mouseleave",n=>{e!==s&&(i&&i.classList&&i.classList.remove("hidden"),t&&t.classList&&t.classList.add("hidden"))})}}},50:(e,t,s)=>{s.r(t),s.d(t,{default:()=>m});var i=s(568);class n{static isTouchDevice(){return"ontouchstart"in document.documentElement}static isIOSDevice(){return n.isIPhoneDevice()||n.isIPadDevice()}static isNativeWindowsBrowser(){return n.isIEBrowser()||n.isEdgeBrowser()}static isIPhoneDevice(){return!!navigator.userAgent.match(/iPhone/i)}static isIPadDevice(){return!!navigator.userAgent.match(/iPad/i)}static isIEBrowser(){return -1!==navigator.userAgent.toLowerCase().indexOf("msie")||!!navigator.userAgent.match(/Trident.*rv:\d+\./)}static isEdgeBrowser(){return!!navigator.userAgent.match(/Edge\/\d+/i)}static getList(){return{"is-touch":n.isTouchDevice(),"is-ios":n.isIOSDevice(),"is-native-windows":n.isNativeWindowsBrowser(),"is-iphone":n.isIPhoneDevice(),"is-ipad":n.isIPadDevice(),"is-ie":n.isIEBrowser(),"is-edge":n.isEdgeBrowser()}}}var r=s(830);class a{static iterate(e,t){if(e instanceof Map||Array.isArray(e))return e.forEach(t);if(e instanceof FormData){for(var s of e.entries())t(s[1],s[0]);return}if(e instanceof NodeList)return e.forEach(t);if(e instanceof HTMLCollection)return Array.from(e).forEach(t);if(e instanceof Object)return Object.keys(e).forEach(s=>{t(e[s],s)});throw Error("The element type ".concat(typeof e," is not iterable!"))}}let o="offcanvas";class l{open(e,t,s,i,n,r,a){this._removeExistingOffCanvas();let o=this._createOffCanvas(s,r,a,i);this.setContent(e,i,n),this._openOffcanvas(o,t)}setContent(e,t){let s=this.getOffCanvas();s[0]&&(s[0].innerHTML=e,this._registerEvents(t))}setAdditionalClassName(e){this.getOffCanvas()[0].classList.add(e)}getOffCanvas(){return document.querySelectorAll(".".concat(o))}close(e){let t=this.getOffCanvas();a.iterate(t,e=>{bootstrap.Offcanvas.getInstance(e).hide()}),setTimeout(()=>{this.$emitter.publish("onCloseOffcanvas",{offCanvasContent:t})},e)}goBackInHistory(){window.history.back()}exists(){return this.getOffCanvas().length>0}_openOffcanvas(e,t){window.focusHandler.saveFocusState("offcanvas"),l.bsOffcanvas.show(),window.history.pushState("offcanvas-open",""),"function"==typeof t&&t()}_registerEvents(e){let t=n.isTouchDevice()?"touchend":"click",s=this.getOffCanvas();a.iterate(s,t=>{let i=()=>{setTimeout(()=>{t.remove(),window.focusHandler.resumeFocusState("offcanvas"),this.$emitter.publish("onCloseOffcanvas",{offCanvasContent:s})},e),t.removeEventListener("hide.bs.offcanvas",i)};t.addEventListener("hide.bs.offcanvas",i)}),window.addEventListener("popstate",this.close.bind(this,e),{once:!0});let i=document.querySelectorAll(".".concat("js-offcanvas-close"));a.iterate(i,s=>s.addEventListener(t,this.close.bind(this,e)))}_removeExistingOffCanvas(){l.bsOffcanvas=null;let e=this.getOffCanvas();return a.iterate(e,e=>e.remove())}_getPositionClass(e){return"left"===e?"offcanvas-start":"right"===e?"offcanvas-end":"offcanvas-".concat(e)}_createOffCanvas(e,t,s,i){let n=document.createElement("div");if(n.classList.add(o),n.classList.add(this._getPositionClass(e)),n.setAttribute("tabindex","-1"),!0===t&&n.classList.add("is-fullwidth"),s){let e=typeof s;if("string"===e)n.classList.add(s);else if(Array.isArray(s))s.forEach(e=>{n.classList.add(e)});else throw Error('The type "'.concat(e,'" is not supported. Please pass an array or a string.'))}return document.body.appendChild(n),l.bsOffcanvas=new bootstrap.Offcanvas(n,{backdrop:!1!==i||"static"}),n}constructor(){this.$emitter=new r.Z}}let c=Object.freeze(new l);class h{static open(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"left",i=!(arguments.length>3)||void 0===arguments[3]||arguments[3],n=arguments.length>4&&void 0!==arguments[4]?arguments[4]:350,r=arguments.length>5&&void 0!==arguments[5]&&arguments[5],a=arguments.length>6&&void 0!==arguments[6]?arguments[6]:"";c.open(e,t,s,i,n,r,a)}static setContent(e){let t=!(arguments.length>1)||void 0===arguments[1]||arguments[1],s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:350;c.setContent(e,t,s)}static setAdditionalClassName(e){c.setAdditionalClassName(e)}static close(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:350;c.close(e)}static exists(){return c.exists()}static getOffCanvas(){return c.getOffCanvas()}static REMOVE_OFF_CANVAS_DELAY(){return 350}}let d="loader",u={BEFORE:"before",INNER:"inner"};class f{create(){if(!this.exists()){if(this.position===u.INNER){this.parent.innerHTML=f.getTemplate();return}this.parent.insertAdjacentHTML(this._getPosition(),f.getTemplate())}}remove(){let e=this.parent.querySelectorAll(".".concat(d));a.iterate(e,e=>e.remove())}exists(){return this.parent.querySelectorAll(".".concat(d)).length>0}_getPosition(){return this.position===u.BEFORE?"afterbegin":"beforeend"}static getTemplate(){return'<div class="'.concat(d,'" role="status">\n                    <span class="').concat("visually-hidden",'">Loading...</span>\n                </div>')}static SELECTOR_CLASS(){return d}constructor(e,t=u.BEFORE){this.parent=e instanceof Element?e:document.body.querySelector(e),this.position=t}}class v{get(e,t){let s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"application/json",i=this._createPreparedRequest("GET",e,s);return this._sendRequest(i,null,t)}post(e,t,s){let i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"application/json";i=this._getContentType(t,i);let n=this._createPreparedRequest("POST",e,i);return this._sendRequest(n,t,s)}delete(e,t,s){let i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"application/json";i=this._getContentType(t,i);let n=this._createPreparedRequest("DELETE",e,i);return this._sendRequest(n,t,s)}patch(e,t,s){let i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"application/json";i=this._getContentType(t,i);let n=this._createPreparedRequest("PATCH",e,i);return this._sendRequest(n,t,s)}abort(){if(this._request)return this._request.abort()}setErrorHandlingInternal(e){this._errorHandlingInternal=e}_registerOnLoaded(e,t){t&&(!0===this._errorHandlingInternal?(e.addEventListener("load",()=>{t(e.responseText,e)}),e.addEventListener("abort",()=>{console.warn("the request to ".concat(e.responseURL," was aborted"))}),e.addEventListener("error",()=>{console.warn("the request to ".concat(e.responseURL," failed with status ").concat(e.status))}),e.addEventListener("timeout",()=>{console.warn("the request to ".concat(e.responseURL," timed out"))})):e.addEventListener("loadend",()=>{t(e.responseText,e)}))}_sendRequest(e,t,s){return this._registerOnLoaded(e,s),e.send(t),e}_getContentType(e,t){return e instanceof FormData&&(t=!1),t}_createPreparedRequest(e,t,s){return this._request=new XMLHttpRequest,this._request.open(e,t),this._request.setRequestHeader("X-Requested-With","XMLHttpRequest"),s&&this._request.setRequestHeader("Content-type",s),this._request}constructor(){this._request=null,this._errorHandlingInternal=!1}}var p=s(49);class g extends i.Z{init(){this._cache={},this._client=new v,this._content=f.getTemplate(),this._registerEvents()}_registerEvents(){if(this.el.removeEventListener(this.options.tiggerEvent,this._getLinkEventHandler.bind(this)),this.el.addEventListener(this.options.tiggerEvent,this._getLinkEventHandler.bind(this)),h.exists()){let e=h.getOffCanvas();a.iterate(e,e=>{let t=e.querySelectorAll(this.options.linkSelector);a.iterate(t,e=>{g._resetLoader(e),e.addEventListener("click",t=>{this._getLinkEventHandler(t,e)})})})}}_openMenu(e){g._stopEvent(e),h.open(this._content,this._registerEvents.bind(this),this.options.position),h.setAdditionalClassName(this.options.additionalOffcanvasClass),this.$emitter.publish("openMenu")}_getLinkEventHandler(e,t){if(!t){let t=p.Z.querySelector(document,this.options.initialContentSelector);return this._content=t.innerHTML,t.classList.contains("is-root")?this._cache[this.options.navigationUrl]=this._content:this._fetchMenu(this.options.navigationUrl),this._openMenu(e)}if(g._stopEvent(e),t.classList.contains(this.options.linkLoadingClass))return;g._setLoader(t);let s=p.Z.getAttribute(t,"data-href",!1)||p.Z.getAttribute(t,"href",!1);if(!s)return;let i=this.options.forwardAnimationType;(t.classList.contains(this.options.homeBtnClass)||t.classList.contains(this.options.backBtnClass))&&(i=this.options.backwardAnimationType),this.$emitter.publish("getLinkEventHandler"),this._fetchMenu(s,this._updateOverlay.bind(this,i))}static _setLoader(e){e.classList.add(this.options.linkLoadingClass);let t=e.querySelector(this.options.loadingIconSelector);t&&(t._linkIcon=t.innerHTML,t.innerHTML=f.getTemplate())}static _resetLoader(e){e.classList.remove(this.options.linkLoadingClass);let t=e.querySelector(this.options.loadingIconSelector);t&&t._linkIcon&&(t.innerHTML=t._linkIcon)}_updateOverlay(e,t){if(this._content=t,h.exists()){let s=g._getOffcanvasMenu();s||this._replaceOffcanvasContent(t),this._createOverlayElements();let i=g._getOverlayContent(s),n=g._getMenuContentFromResponse(t);this._replaceOffcanvasMenuContent(e,n,i),this._registerEvents()}this.$emitter.publish("updateOverlay")}_replaceOffcanvasMenuContent(e,t,s){if(e===this.options.forwardAnimationType){this._animateForward(t,s);return}if(e===this.options.backwardAnimationType){this._animateBackward(t,s);return}this._animateInstant(t,s),this.$emitter.publish("replaceOffcanvasMenuContent")}_animateInstant(e){this._overlay.innerHTML=e,this.$emitter.publish("animateInstant")}_animateForward(e,t){""===this._placeholder.innerHTML&&(this._placeholder.innerHTML=t),this._overlay.classList.remove(this.options.transitionClass),this._overlay.style.left="100%",this._overlay.innerHTML=e,setTimeout(()=>{this._overlay.classList.add(this.options.transitionClass),this._overlay.style.left="0%"},1),this.$emitter.publish("animateForward")}_animateBackward(e,t){""===this._overlay.innerHTML&&(this._overlay.innerHTML=t),this._placeholder.innerHTML=e,this._overlay.classList.remove(this.options.transitionClass),this._overlay.style.left="0%",setTimeout(()=>{this._overlay.classList.add(this.options.transitionClass),this._overlay.style.left="100%"},1),this.$emitter.publish("animateBackward")}static _getMenuContentFromResponse(e){let t=new DOMParser().parseFromString(e,"text/html");return g._getOverlayContent(t)}static _getOverlayContent(e){if(!e)return"";let t=e.querySelector(this.options.overlayContentSelector);return t?t.innerHTML:""}_createOverlayElements(){let e=g._getOffcanvasMenu();e&&(this._placeholder=g._createPlaceholder(e),this._overlay=g._createNavigationOverlay(e)),this.$emitter.publish("createOverlayElements")}static _createNavigationOverlay(e){let t=g._getOffcanvas(),s=t.querySelector(this.options.overlayClass);if(s)return s;let i=document.createElement("div");return i.classList.add(this.options.overlayClass.substr(1)),i.style.minHeight="".concat(t.clientHeight,"px"),e.appendChild(i),i}static _createPlaceholder(e){let t=g._getOffcanvas(),s=t.querySelector(this.options.placeholderClass);if(s)return s;let i=document.createElement("div");return i.classList.add(this.options.placeholderClass.substr(1)),i.style.minHeight="".concat(t.clientHeight,"px"),e.appendChild(i),i}_fetchMenu(e,t){return!!e&&(this._cache[e]&&"function"==typeof t?t(this._cache[e]):void(this.$emitter.publish("beforeFetchMenu"),this._client.get(e,s=>{this._cache[e]=s,"function"==typeof t&&t(s)})))}_replaceOffcanvasContent(e){this._content=e,h.setContent(this._content),this._registerEvents(),this.$emitter.publish("replaceOffcanvasContent")}static _stopEvent(e){e.preventDefault(),e.stopImmediatePropagation()}static _getOffcanvas(){return h.getOffCanvas()[0]}static _getOffcanvasMenu(){return g._getOffcanvas().querySelector(this.options.menuSelector)}}g.options={navigationUrl:window.router["frontend.menu.offcanvas"],position:"left",tiggerEvent:"click",additionalOffcanvasClass:"navigation-offcanvas",linkSelector:".js-navigation-offcanvas-link",loadingIconSelector:".js-navigation-offcanvas-loading-icon",linkLoadingClass:"is-loading",menuSelector:".js-navigation-offcanvas",overlayContentSelector:".js-navigation-offcanvas-overlay-content",initialContentSelector:".js-navigation-offcanvas-initial-content",homeBtnClass:"is-home-link",backBtnClass:"is-back-link",transitionClass:"has-transition",overlayClass:".navigation-offcanvas-overlay",placeholderClass:".navigation-offcanvas-placeholder",forwardAnimationType:"forwards",backwardAnimationType:"backwards"};class m extends g{init(){super.init(),this.$emitter.subscribe("animateBackward",this.onAnimateBackward.bind(this)),this.$emitter.subscribe("animateForward",this.onAnimateForward.bind(this))}_createOverlayElements(){super._createOverlayElements();let e=g._getOffcanvas(),t=e?e.clientHeight:0;if(!this.headerHeight){if(e){let t=e.querySelectorAll(".navigation-offcanvas-list")[0];if(t){let e=t.getBoundingClientRect();this.headerHeight=e?e.top:0}else this.headerHeight=0}else this.headerHeight=0}let s=t-this.headerHeight;this._overlay.style.minHeight="".concat(s,"px"),this._placeholder.style.minHeight="".concat(s,"px")}_animateForward(e,t){this.hideOverlayContentDOM(),this._placeholder.classList.remove("hidden"),super._animateForward(e,t)}_animateBackward(e,t){this.hideOverlayContentDOM(),this._overlay.classList.remove("hidden"),super._animateBackward(e,t)}onAnimateBackward(){setTimeout(()=>{this._overlay.classList.add("hidden"),this._placeholder.classList.remove("hidden")},500)}onAnimateForward(){setTimeout(()=>{this._placeholder.classList.add("hidden"),this._overlay.classList.remove("hidden")},500)}hideOverlayContentDOM(){let e=g._getOffcanvas(),t=null;if(e){let s=e.querySelector(this.options.overlayContentSelector);s&&s.classList&&(t=s.classList)}t&&!t.contains("hidden")&&t.add("hidden")}}},49:(e,t,s)=>{s.d(t,{Z:()=>n});var i=s(140);class n{static isNode(e){return"object"==typeof e&&null!==e&&(e===document||e===window||e instanceof Node)}static hasAttribute(e,t){if(!n.isNode(e))throw Error("The element must be a valid HTML Node!");return"function"==typeof e.hasAttribute&&e.hasAttribute(t)}static getAttribute(e,t){let s=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(s&&!1===n.hasAttribute(e,t))throw Error('The required property "'.concat(t,'" does not exist!'));if("function"!=typeof e.getAttribute){if(s)throw Error("This node doesn't support the getAttribute function!");return}return e.getAttribute(t)}static getDataAttribute(e,t){let s=!(arguments.length>2)||void 0===arguments[2]||arguments[2],r=t.replace(/^data(|-)/,""),a=i.Z.toLowerCamelCase(r,"-");if(!n.isNode(e)){if(s)throw Error("The passed node is not a valid HTML Node!");return}if(void 0===e.dataset){if(s)throw Error("This node doesn't support the dataset attribute!");return}let o=e.dataset[a];if(void 0===o){if(s)throw Error('The required data attribute "'.concat(t,'" does not exist on ').concat(e,"!"));return o}return i.Z.parsePrimitive(o)}static querySelector(e,t){let s=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(s&&!n.isNode(e))throw Error("The parent node is not a valid HTML Node!");let i=e.querySelector(t)||!1;if(s&&!1===i)throw Error('The required element "'.concat(t,'" does not exist in parent node!'));return i}static querySelectorAll(e,t){let s=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(s&&!n.isNode(e))throw Error("The parent node is not a valid HTML Node!");let i=e.querySelectorAll(t);if(0===i.length&&(i=!1),s&&!1===i)throw Error('At least one item of "'.concat(t,'" must exist in parent node!'));return i}static getFocusableElements(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:document.body;return e.querySelectorAll('\n            input:not([tabindex^="-"]):not([disabled]):not([type="hidden"]),\n            select:not([tabindex^="-"]):not([disabled]),\n            textarea:not([tabindex^="-"]):not([disabled]),\n            button:not([tabindex^="-"]):not([disabled]),\n            a[href]:not([tabindex^="-"]):not([disabled]),\n            [tabindex]:not([tabindex^="-"]):not([disabled])\n        ')}static getFirstFocusableElement(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:document.body;return this.getFocusableElements(e)[0]}static getLastFocusableElement(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:document,t=this.getFocusableElements(e);return t[t.length-1]}}},830:(e,t,s)=>{s.d(t,{Z:()=>i});class i{publish(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},s=arguments.length>2&&void 0!==arguments[2]&&arguments[2],i=new CustomEvent(e,{detail:t,cancelable:s});return this.el.dispatchEvent(i),i}subscribe(e,t){let s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},i=this,n=e.split("."),r=s.scope?t.bind(s.scope):t;if(s.once&&!0===s.once){let t=r;r=function(s){i.unsubscribe(e),t(s)}}return this.el.addEventListener(n[0],r),this.listeners.push({splitEventName:n,opts:s,cb:r}),!0}unsubscribe(e){let t=e.split(".");return this.listeners=this.listeners.reduce((e,s)=>([...s.splitEventName].sort().toString()===t.sort().toString()?this.el.removeEventListener(s.splitEventName[0],s.cb):e.push(s),e),[]),!0}reset(){return this.listeners.forEach(e=>{this.el.removeEventListener(e.splitEventName[0],e.cb)}),this.listeners=[],!0}get el(){return this._el}set el(e){this._el=e}get listeners(){return this._listeners}set listeners(e){this._listeners=e}constructor(e=document){this._el=e,e.$emitter=this,this._listeners=[]}}},140:(e,t,s)=>{s.d(t,{Z:()=>i});class i{static ucFirst(e){return e.charAt(0).toUpperCase()+e.slice(1)}static lcFirst(e){return e.charAt(0).toLowerCase()+e.slice(1)}static toDashCase(e){return e.replace(/([A-Z])/g,"-$1").replace(/^-/,"").toLowerCase()}static toLowerCamelCase(e,t){let s=i.toUpperCamelCase(e,t);return i.lcFirst(s)}static toUpperCamelCase(e,t){return t?e.split(t).map(e=>i.ucFirst(e.toLowerCase())).join(""):i.ucFirst(e.toLowerCase())}static parsePrimitive(e){try{return/^\d+(.|,)\d+$/.test(e)&&(e=e.replace(",",".")),JSON.parse(e)}catch(t){return e.toString()}}}},568:(e,t,s)=>{s.d(t,{Z:()=>l});var i=s(857),n=s.n(i),r=s(49),a=s(140),o=s(830);class l{init(){throw Error('The "init" method for the plugin "'.concat(this._pluginName,'" is not defined.'))}update(){}_init(){this._initialized||(this.init(),this._initialized=!0)}_update(){this._initialized&&this.update()}_mergeOptions(e){let t=a.Z.toDashCase(this._pluginName),s=r.Z.getDataAttribute(this.el,"data-".concat(t,"-config"),!1),i=r.Z.getAttribute(this.el,"data-".concat(t,"-options"),!1),o=[this.constructor.options,this.options,e];s&&o.push(window.PluginConfigManager.get(this._pluginName,s));try{i&&o.push(JSON.parse(i))}catch(e){throw console.error(this.el),Error('The data attribute "data-'.concat(t,'-options" could not be parsed to json: ').concat(e.message))}return n().all(o.filter(e=>e instanceof Object&&!(e instanceof Array)).map(e=>e||{}))}_registerInstance(){window.PluginManager.getPluginInstancesFromElement(this.el).set(this._pluginName,this),window.PluginManager.getPlugin(this._pluginName,!1).get("instances").push(this)}_getPluginName(e){return e||(e=this.constructor.name),e}constructor(e,t={},s=!1){if(!r.Z.isNode(e))throw Error("There is no valid element given.");this.el=e,this.$emitter=new o.Z(this.el),this._pluginName=this._getPluginName(s),this.options=this._mergeOptions(t),this._initialized=!1,this._registerInstance(),this._init()}}}},t={};function s(i){var n=t[i];if(void 0!==n)return n.exports;var r=t[i]={exports:{}};return e[i](r,r.exports,s),r.exports}(()=>{s.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return s.d(t,{a:t}),t}})(),(()=>{s.d=(e,t)=>{for(var i in t)s.o(t,i)&&!s.o(e,i)&&Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}})(),(()=>{s.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t)})(),(()=>{s.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}})(),(()=>{let e=Object.freeze(new class{init(e){Object.entries(e).forEach(e=>{let[t,s]=e;this.flags[t]=s})}isActive(e){return!!Object.prototype.hasOwnProperty.call(this.flags,e)&&this.flags[e]}constructor(){this.flags={},window.features&&this.init(window.features)}});var t=s(50),i=s(381);let n=window.PluginManager;(class{static init(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};e.init(t)}static isActive(t){return e.isActive(t)}constructor(){window.Feature=this}}).isActive("v6.6.0.0")?(n.override("OffCanvasMenu",()=>Promise.resolve().then(s.bind(s,50)),"[data-off-canvas-menu]"),n.register("BrainstHoverImageChange",()=>Promise.resolve().then(s.bind(s,381)),"[data-brainst-hover-image-change]")):(n.override("OffcanvasMenu",t.default,"[data-offcanvas-menu]"),n.register("BrainstHoverImageChange",i.default,"[data-brainst-hover-image-change]"))})()})();