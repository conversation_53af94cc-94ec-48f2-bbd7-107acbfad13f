<?php declare(strict_types=1);

namespace Brainst\Navigation;

use Shopware\Core\Framework\Plugin;
use Shopware\Core\Framework\Plugin\Context\ActivateContext;
use Shopware\Core\Framework\Plugin\Context\DeactivateContext;
use Shopware\Core\Framework\Plugin\Context\InstallContext;
use Shopware\Core\Framework\Plugin\Context\UninstallContext;
use Brainst\Navigation\Service\CustomFieldService;

/**
 * Class BrainstNavigation
 * @package BrainstNavigation
 * <AUTHOR>
 * @link www.brainstreamtechnolabs.com
 */
class BrainstNavigation extends Plugin
{
    /**
     * @param InstallContext $installContext
     * @return void
     */
    public function install(InstallContext $installContext): void
    {
        parent::install($installContext);

        $this->getCustomFieldsService()->addCustomFields($installContext->getContext());
    }

    /**
     * @param UninstallContext $uninstallContext
     * @return void
     */
    public function uninstall(UninstallContext $uninstallContext) : void
    {
        parent::uninstall($uninstallContext);

        if ($uninstallContext->keepUserData()) {
            return;
        }
        $this->getCustomFieldsService()->removeCustomFields($uninstallContext->getContext());
    }

    /**
     * @param ActivateContext $activateContext
     * @return void
     */
    public function activate(ActivateContext $activateContext) : void
    {
        parent::activate($activateContext);

        $this->getCustomFieldsService()->activateCustomFields($activateContext->getContext());
    }

    /**
     * @param DeactivateContext $deactivateContext
     * @return void
     */
    public function deactivate(DeactivateContext $deactivateContext) : void
    {
        parent::deactivate($deactivateContext);

        $this->getCustomFieldsService()->deactivateCustomFields($deactivateContext->getContext());
    }

    /**
     * @return CustomFieldService
     */
    private function getCustomFieldsService(): CustomFieldService
    {
        if ($this->container->has(CustomFieldService::class)) {
            return $this->container->get(CustomFieldService::class);
        }

        return new CustomFieldService(
            $this->container->get('custom_field_set.repository'),
            $this->container->get('custom_field_set_relation.repository')
        );
    }
}
