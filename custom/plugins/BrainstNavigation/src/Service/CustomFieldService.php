<?php
declare(strict_types=1);

namespace Brainst\Navigation\Service;

use Shopware\Core\Content\Category\CategoryDefinition;
use Shopware\Core\Defaults;
use Shopware\Core\Framework\Context;
use Shopware\Core\Framework\DataAbstractionLayer\EntityRepository;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Criteria;
use Shopware\Core\Framework\DataAbstractionLayer\Search\EntitySearchResult;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Filter\EqualsAnyFilter;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Filter\EqualsFilter;
use Shopware\Core\Framework\Uuid\Uuid;
use Shopware\Core\System\CustomField\Aggregate\CustomFieldSet\CustomFieldSetEntity;
use Shopware\Core\System\CustomField\CustomFieldEntity;
use Shopware\Core\System\CustomField\CustomFieldTypes;

/**
 * Class CustomFieldService
 * @package BrainstNavigation
 * <AUTHOR>
 * @link www.brainstreamtechnolabs.com
 */
class CustomFieldService
{
    private const CUSTOM_FIELDSET_NAME = 'bs_navigation_set';
    private EntityRepository $customFieldSetRepository;
    private EntityRepository $customFieldSetRelationRepository;

    /**
     * CustomFieldService constructor.
     *
     * @param EntityRepository $customFieldSetRepository
     * @param EntityRepository $customFieldSetRelationRepository
     */
    public function __construct(
        EntityRepository $customFieldSetRepository,
        EntityRepository $customFieldSetRelationRepository
    ) {
        $this->customFieldSetRepository = $customFieldSetRepository;
        $this->customFieldSetRelationRepository = $customFieldSetRelationRepository;
    }

    /**
     * @param Context $context
     */
    public function addCustomFields(Context $context): void
    {
        $customFieldOptions = $this->getCustomFieldSet();
        $this->customFieldSetRepository->upsert([$customFieldOptions], $context);
    }

    /**
     * @param Context $context
     */
    public function removeCustomFields(Context $context): void
    {
        $customFieldSet = $this->customFieldSetExist($context);

        if ($customFieldSet instanceof CustomFieldSetEntity) {
            $this->customFieldSetRepository->delete([['id' => $customFieldSet->getId()]], $context);
        }
    }

    /**
     * @param Context $context
     */
    public function activateCustomFields(Context $context): void
    {
        $customFieldSet = $this->customFieldSetExist($context);
        if ($customFieldSet instanceof CustomFieldSetEntity) {
            $arrUpdate = [];
            $customFields = $this->getCustomFields($context, $customFieldSet);

            /** @var CustomFieldEntity $customField */
            foreach ($customFields as $customField) {
                $arrUpdate[] = ['id' => $customField->getId(), 'active' => true];
            }
            $this->customFieldSetRelationRepository->update($arrUpdate, $context);
        }
    }

    /**
     * @param Context $context
     */
    public function deactivateCustomFields(Context $context): void
    {
        $customFieldSet = $this->customFieldSetExist($context);
        if ($customFieldSet instanceof CustomFieldSetEntity) {
            $arrUpdate = [];
            $customFields = $this->getCustomFields($context, $customFieldSet);

            /** @var CustomFieldEntity $customField */
            foreach ($customFields as $customField) {
                $arrUpdate[] = ['id' => $customField->getId(), 'active' => false];
            }
            $this->customFieldSetRelationRepository->update($arrUpdate, $context);
        }
    }

    /**
     * @param Context $context
     * @return CustomFieldSetEntity|null
     */
    private function customFieldSetExist(Context $context): ?CustomFieldSetEntity
    {
        $criteria = new Criteria();
        $criteria->addFilter(new EqualsAnyFilter('name', [self::CUSTOM_FIELDSET_NAME]));

        $customFieldSet = $this->customFieldSetRepository->search($criteria, $context)->first();

        return $customFieldSet ?? null;
    }

    /**
     * @param Context $context
     * @param CustomFieldSetEntity $customFieldSetEntity
     * @return EntitySearchResult|null
     */
    private function getCustomFields(Context $context, CustomFieldSetEntity $customFieldSetEntity): ?EntitySearchResult
    {
        $criteria = new Criteria();
        $criteria->addFilter(new EqualsFilter('customFieldSetId', $customFieldSetEntity->getId()));

        $customFields = $this->customFieldSetRelationRepository->search($criteria, $context);

        return $customFields ?? null;
    }

    /**
     * @return array
     */
    private function getCustomFieldSet(): array
    {
        $fieldSetId = Uuid::randomHex();
        
        return [
            'id' => $fieldSetId,
            'name' => self::CUSTOM_FIELDSET_NAME,
            'config' => [
                'label' => [
                    'en-GB' => 'Offcanvas Navigation Options',
                    'de-DE' => 'Offcanvas Navigation Options',
                    Defaults::LANGUAGE_SYSTEM => 'Navigation Options'
                ]
            ],
            'customFields' => [
                [
                    'name' => 'show_subcategory_images',
                    'type' => CustomFieldTypes::SWITCH,
                    'config' => [
                        'componentName' => 'sw-field',
                        'customFieldType' => CustomFieldTypes::SWITCH,
                        'label' => [
                            'en-GB' => 'Show sub category images',
                            'de-DE' => 'Show sub category images',
                            Defaults::LANGUAGE_SYSTEM => 'Show sub category images'
                        ],
                        'customFieldPosition' => 1
                    ]
                ]
            ],
            'relations' => [
                [
                    'id' => $fieldSetId,
                    'entityName' => CategoryDefinition::ENTITY_NAME
                ]
            ]
        ];
    }
}
