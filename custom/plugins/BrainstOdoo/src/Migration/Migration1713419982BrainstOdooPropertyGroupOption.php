<?php declare(strict_types=1);

namespace Brainst\Odoo\Migration;

use Doctrine\DBAL\Connection;
use Shopware\Core\Framework\Migration\MigrationStep;

class Migration1713419982BrainstOdooPropertyGroupOption extends MigrationStep
{
    public function getCreationTimestamp(): int
    {
        return 1713419982;
    }

    public function update(Connection $connection): void
    {
        $sql = <<<SQL
CREATE TABLE IF NOT EXISTS `brainst_odoo_property_group_option` (
                `property_group_option_id` BINARY(16) NOT NULL,
                `odoo_property_group_option_id` INT NOT NULL,
                PRIMARY KEY (`property_group_option_id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
SQL;
        $connection->executeStatement($sql);
    }

    public function updateDestructive(Connection $connection): void
    {
    }
}
