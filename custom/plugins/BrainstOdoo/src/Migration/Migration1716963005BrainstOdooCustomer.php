<?php declare(strict_types=1);

namespace Brainst\Odoo\Migration;

use Doctrine\DBAL\Connection;
use Shopware\Core\Framework\Log\Package;
use Shopware\Core\Framework\Migration\MigrationStep;

/**
 * @internal
 */
#[Package('core')]
class Migration1716963005BrainstOdooCustomer extends MigrationStep
{
    public function getCreationTimestamp(): int
    {
        return 1716963005;
    }

    public function update(Connection $connection): void
    {
        $sql = <<<SQL
CREATE TABLE IF NOT EXISTS `brainst_odoo_customer` (
                `customer_id` BINARY(16) NOT NULL,
                `odoo_customer_id` INT NOT NULL,
                PRIMARY KEY (`customer_id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
SQL;
        $connection->executeStatement($sql);
    }

    public function updateDestructive(Connection $connection): void
    {
    }
}
