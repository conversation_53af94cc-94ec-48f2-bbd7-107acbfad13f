<?php

declare(strict_types=1);

namespace Brainst\Odoo\Service\SyncQueue;

use Brainst\Odoo\MessageQueue\Message\CategoryDataMessage;
use Brainst\Odoo\Model\Operation;
use Symfony\Component\Messenger\Stamp\DispatchAfterCurrentBusStamp;

/**
 * Class CategorySyncQueueService
 * @package BrainstOdoo
 * <AUTHOR>
 * @link www.brainstreamtechnolabs.com
 */
class CategorySyncQueueService extends AbstractSyncQueue
{
    public final const LIMIT = 10;

    public function __invoke(): void
    {
        $iterator = $this->getIterator(self::LIMIT);

        while ($searchResult = $iterator->fetch()) {
            $this->messageBus->dispatch(
                CategoryDataMessage::forCategoryIdsWithOperation(
                    $searchResult->getIds(),
                    Operation::update()->type(),
                ),
            )->with(new DispatchAfterCurrentBusStamp());
        }
    }

}
