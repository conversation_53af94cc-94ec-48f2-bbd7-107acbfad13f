<?php

declare(strict_types=1);

namespace Brainst\Odoo\Service\SyncHelper;

use Brainst\Odoo\Service\OdooService;
use PhpXmlRpc\Exception\StateErrorException;
use PhpXmlRpc\Exception\ValueErrorException;

/**
 * Class JournalAccountServiceV18
 * @package BrainstOdoo
 * <AUTHOR>
 * @link www.brainstreamtechnolabs.com
 */
class JournalAccountServiceV18
{
    /**
     * Check/Create account/journal of Odoo company
     *
     * @param OdooService $odooService
     * @param int $companyId
     * @return array
     * @throws StateErrorException
     * @throws ValueErrorException
     */
    public static function createAccounts(OdooService $odooService, int $companyId): array
    {
        $miscellaneousJournalId = $invoiceJournalId = $cashJournalId = $inboundPaymentMethodId = $taxPayableAccountId = $taxReceivableAccountId = $taxReceivedAccountId = $outgoingPickingTypeId = false;
        $existedAccounts = [];
        $isValid = $odooService->odooUid != false;

        if ($isValid) {
            $miscellaneousJournalId = self::upsertMiscellaneousJournal($odooService, $companyId);
            $exchangeDifferenceJournalId = self::upsertExchangeDifferenceJournalId($odooService, $companyId);
            $accountPaymentTypes = self::getAccountPaymentTypes($odooService);

            $accountCodes = self::getCodeList();
            $listData = self::getExistingAccountList($odooService, $companyId, $accountCodes);

            $createAccountData = [];
            foreach ($accountCodes as $code) {
                if (array_key_exists($code, $listData)) {
                    $existedAccounts[$code] = $listData[$code];
                } else {
                    $createAccountData[] = self::getDataForAccount($companyId, (int)$code);
                }
            }

            if (!empty($createAccountData)) {
                $odooService->executeRpc('execute_kw', 'account.account', 'create',
                    [$createAccountData]);
                $existedAccounts = self::getExistingAccountList($odooService, $companyId, $accountCodes);
            }

            $taxPayableAccountId = $existedAccounts['252000'];
            $taxReceivableAccountId = $existedAccounts['132000'];
            $taxReceivedAccountId = $existedAccounts['251000'];

            $invoiceJournalId = self::getInvoiceJournalId($odooService, $companyId, $existedAccounts);
            $cashJournalId = self::getCashJournalId($odooService, $companyId, $existedAccounts, $accountPaymentTypes);
            $inboundPaymentMethodId = self::getInboundPaymentMethodId($odooService, $companyId);
            $outgoingPickingTypeId = self::getOutgoingPickingTypeId($odooService, $companyId);

            $latestId = $odooService->executeRpc('execute_kw', 'res.config.settings', 'search',
                [[['company_id', '=', $companyId]]], ['order' => 'id desc', 'limit' => 1]);

            $settingData = [
                'company_id' => $companyId,
                // Accounting default account and journals
                // Exchanges Accounts
                'currency_exchange_journal_id' => $exchangeDifferenceJournalId,
                'income_currency_exchange_account_id' => $existedAccounts['441000'],
                'expense_currency_exchange_account_id' => $existedAccounts['641000'],

                // Post bank transactions and payments Accounts
                'account_journal_suspense_account_id' => $existedAccounts['101402'],
                'transfer_account_id' => $existedAccounts['101701'],

                // Post discounts Accounts
                'account_journal_early_pay_discount_gain_account_id' => $existedAccounts['643000'],
                'account_journal_early_pay_discount_loss_account_id' => $existedAccounts['443000'],

                // Order Delivery validate issue of second API call need
                'stock_move_sms_validation' => false,
                'stock_move_email_validation' => false
            ];
            if (!$latestId) {
                $odooService->executeRpc('execute', 'res.config.settings', 'create', $settingData);
            } else {
                $odooService->executeRpc('execute', 'res.config.settings', 'write', [$latestId[0]->scalarVal()], $settingData);
            }
        }

        return [
            'cashJournalId' => $cashJournalId,
            'invoiceJournalId' => $invoiceJournalId,
            'inboundPaymentMethodId' => $inboundPaymentMethodId,
            'taxPayableAccountId' => $taxPayableAccountId,
            'taxReceivableAccountId' => $taxReceivableAccountId,
            'taxReceivedAccountId' => $taxReceivedAccountId,
            'outgoingPickingTypeId' => $outgoingPickingTypeId,
            'miscellaneousJournalId' => $miscellaneousJournalId
        ];
    }

    /**
     * @param OdooService $odooService
     * @param int $companyId
     * @param array $accountCodes
     * @return array
     * @throws ValueErrorException
     */
    private static function getExistingAccountList(OdooService $odooService, int $companyId, array $accountCodes): array
    {
        $accountList = $odooService->executeRpc('execute_kw', 'account.account',
            'web_search_read',
            [
                [],
            ],
            [
                'context' => [
                    'allowed_company_ids' => [$companyId],
                    'current_company_id' => $companyId
                ],
//                    'domain' => [],
                'limit' => 2000,
                'offset' => 0,
                'order' => '',
                'specification' => [
                    'account_type' => [],
                    'code' => []
                ]
            ]
        );

        return self::getAccountCodes($accountList, $accountCodes);
    }

    /**
     * @param OdooService $odooService
     * @param int $companyId
     * @return int|null
     * @throws ValueErrorException
     */
    private static function upsertMiscellaneousJournal(OdooService $odooService, int $companyId): ?int
    {
        // Check if not exist then generate Miscellaneous journal
        $miscellaneousJournal = $odooService->executeRpc('execute_kw', 'account.journal', 'search',
            [
                [
                    ['name', '=', 'Miscellaneous Operations'],
                    ['code', '=', 'MISC'],
                    ['type', '=', 'general'],
                    ['company_id', '=', $companyId],
                ]
            ]);

        if (!$miscellaneousJournal) {
            $miscellaneousJournalId = $odooService->executeRpc('execute', 'account.journal', 'create', [
                'display_name' => 'Miscellaneous Operations',
                'name' => 'Miscellaneous Operations',
                'company_id' => $companyId,
                'code' => 'MISC',
                'type' => 'general',
            ]);
        } else {
            $miscellaneousJournalId = $miscellaneousJournal[0]->scalarVal();
        }

        return $miscellaneousJournalId;
    }

    /**
     * @param OdooService $odooService
     * @param int $companyId
     * @return int|null
     * @throws ValueErrorException
     */
    private static function upsertExchangeDifferenceJournalId(OdooService $odooService, int $companyId): ?int
    {
        $exchangeDifferenceJournal = $odooService->executeRpc('execute_kw', 'account.journal', 'search',
            [
                [
                    ['name', '=', 'Exchange Difference'],
                    ['code', '=', 'EXCH'],
                    ['type', '=', 'general'],
                    ['company_id', '=', $companyId]
                ]
            ]);

        if (!$exchangeDifferenceJournal) {
            $exchangeDifferenceJournalId = $odooService->executeRpc('execute', 'account.journal', 'create', [
                'display_name' => 'Exchange Difference',
                'name' => 'Exchange Difference',
                'company_id' => $companyId,
                'code' => 'EXCH',
                'type' => 'general',
            ]);
        } else {
            $exchangeDifferenceJournalId = $exchangeDifferenceJournal[0]->scalarVal();
        }
        return $exchangeDifferenceJournalId;
    }

    /**
     * @param OdooService $odooService
     * @return array|null
     * @throws ValueErrorException
     */
    private static function getAccountPaymentTypes(OdooService $odooService): ?array
    {
        // Odoo company - default account and account journal sync
        $accountPaymentMethods = $odooService->executeRpc('execute_kw', 'account.payment.method',
            'search_read',
            [
                [
                    ['code', '=', 'manual'],
                    ['payment_type', 'in', ['outbound', 'inbound']]
                ],
                ['payment_type']
            ]);

        return OdooService::getPluck($accountPaymentMethods, 'payment_type');
    }

    /**
     * @param OdooService $odooService
     * @param int $companyId
     * @param array $existedAccounts
     * @return int|null
     * @throws ValueErrorException
     */
    private static function getInvoiceJournalId(OdooService $odooService, int $companyId, array $existedAccounts): ?int
    {
        $invoiceJournal = $odooService->executeRpc('execute_kw', 'account.journal', 'search',
            [
                [
                    ['name', '=', 'Customer Invoices'],
                    ['code', '=', 'INV'],
                    ['type', '=', 'sale'],
                    ['company_id', '=', $companyId]
                ]
            ]);

        if ($invoiceJournal) {
            $invoiceJournalId = $invoiceJournal[0]->offsetGet('int');
        } else {
            $invoiceJournalId = $odooService->executeRpc('execute', 'account.journal', 'create', [
                'display_name' => 'Customer Invoices',
                'name' => 'Customer Invoices',
                'company_id' => $companyId,
                'code' => 'INV',
                'type' => 'sale',
                'refund_sequence' => true,
                'invoice_reference_type' => 'invoice',
                'invoice_reference_model' => 'odoo',
                'default_account_id' => $existedAccounts['400000']
            ]);
        }

        return $invoiceJournalId;
    }

    /**
     * @param OdooService $odooService
     * @param int $companyId
     * @param array $existedAccounts
     * @param array $accountPaymentTypes
     * @return int|null
     * @throws ValueErrorException
     */
    private static function getCashJournalId(
        OdooService $odooService,
        int         $companyId,
        array       $existedAccounts,
        array       $accountPaymentTypes
    ): ?int
    {
        $cashJournal = $odooService->executeRpc('execute_kw', 'account.journal', 'search',
            [
                [
                    ['name', '=', 'Cash'],
                    ['code', '=', 'CSH1'],
                    ['type', '=', 'cash'],
                    ['company_id', '=', $companyId]
                ]
            ]);

        if ($cashJournal) {
            $cashJournalId = $cashJournal[0]->offsetGet('int');
        } else {
            $cashJournalId = $odooService->executeRpc('execute', 'account.journal', 'create', [
                'display_name' => 'Cash',
                'name' => 'Cash',
                'company_id' => $companyId,
                'code' => 'CSH1',
                'type' => 'cash',
                'refund_sequence' => true,
                'default_account_id' => $existedAccounts['101501'],
                'suspense_account_id' => $existedAccounts['101402'],
                'profit_account_id' => $existedAccounts['442000'],
                'loss_account_id' => $existedAccounts['642000'],
                'outbound_payment_method_line_ids' => [
                    [
                        0,
                        0,
                        [
                            'payment_method_id' => $accountPaymentTypes['outbound'],
                            'name' => 'Manual',
                            'payment_account_id' => false
                        ]
                    ]
                ],
                'inbound_payment_method_line_ids' => [
                    [
                        0,
                        0,
                        [
                            'payment_method_id' => $accountPaymentTypes['inbound'],
                            'name' => 'Manual',
                            'payment_account_id' => false
                        ]
                    ]
                ],

            ]);
        }

        return $cashJournalId;
    }

    /**
     * @param OdooService $odooService
     * @param int $companyId
     * @return int|null
     * @throws ValueErrorException
     */
    private static function getInboundPaymentMethodId(OdooService $odooService, int $companyId): ?int
    {
        $cashAccountJournal = $odooService->executeRpc('execute_kw', 'account.journal', 'search_read',
            [
                [
                    ['name', '=', 'Cash'],
                    ['code', '=', 'CSH1'],
                    ['type', '=', 'cash'],
                    ['company_id', '=', $companyId]
                ],
                ['inbound_payment_method_line_ids']
            ]);
        return $cashAccountJournal[0]->offsetGet('inbound_payment_method_line_ids')->offsetGet(0)->scalarVal();
    }

    /**
     * @param OdooService $odooService
     * @param int $companyId
     * @return int|bool|null
     * @throws ValueErrorException
     */
    private static function getOutgoingPickingTypeId(OdooService $odooService, int $companyId): int|bool|null
    {
        $outgoingPickingTypeId = false;
        $deliveryType = $odooService->executeRpc('execute_kw', 'stock.picking.type', 'search',
            [[['company_id', '=', $companyId], ['code', '=', 'outgoing']]]);

        if ($deliveryType) {
            $outgoingPickingTypeId = $deliveryType[0]->scalarVal();
        }

        return $outgoingPickingTypeId;
    }

    /**
     * @param array $object
     * @param array $codes
     * @return array
     */
    public static function getAccountCodes(array $object, array $codes): array
    {
        $returnData = [];

        foreach ($object['records'] as $item) {
            $code = $item->offsetGet('code')->scalarVal();
            if (in_array($code, $codes)) {
                $returnData[$code] = $item->offsetGet('id')->scalarVal();
            }
        }
        return $returnData;
    }

    /**
     * Get list of account codes
     * @return string[]
     */
    private static function getCodeList(): array
    {
        return [
            '101402',
            '101501',
            '101701',
            '121000',
            '132000',
            '211000',
            '251000',
            '252000',
            '400000',
            '441000',
            '442000',
            '443000',
            '641000',
            '642000',
            '643000'
        ];
    }

    /**
     * Get data for creating odoo account
     *
     * @param int $companyId
     * @param int $code
     * @return array
     */
    private static function getDataForAccount(int $companyId, int $code): array
    {
        $accounts = [
            101501 => [
                'code' => '101501',
                'display_name' => 'Cash',
                'name' => 'Cash',
                'account_type' => 'asset_cash',
                'company_ids' => [$companyId],
                'reconcile' => false,
                'deprecated' => false,
            ],
            101402 => [
                'code' => '101402',
                'display_name' => 'Bank Suspense Account',
                'name' => 'Bank Suspense Account',
                'account_type' => 'asset_current',
                'company_ids' => [$companyId],
                'reconcile' => false,
                'deprecated' => false,
            ],
            442000 => [
                'code' => '442000',
                'display_name' => 'Cash Difference Gain',
                'name' => 'Cash Difference Gain',
                'account_type' => 'income',
                'company_ids' => [$companyId],
                'reconcile' => false,
                'deprecated' => false,
            ],
            642000 => [
                'code' => '642000',
                'display_name' => 'Cash Difference Loss',
                'name' => 'Cash Difference Loss',
                'account_type' => 'expense',
                'company_ids' => [$companyId],
                'reconcile' => false,
                'deprecated' => false,
            ],
            400000 => [
                'code' => '400000',
                'display_name' => 'Product Sales',
                'name' => 'Product Sales',
                'account_type' => 'income',
                'company_ids' => [$companyId],
                'reconcile' => false,
                'deprecated' => false,
            ],
            252000 => [
                'code' => '252000',
                'display_name' => 'Tax Payable',
                'name' => 'Tax Payable',
                'account_type' => 'liability_current',
                'company_ids' => [$companyId],
                'reconcile' => false,
                'deprecated' => false,
            ],
            132000 => [
                'code' => '132000',
                'display_name' => 'Tax Receivable',
                'name' => 'Tax Receivable',
                'account_type' => 'asset_current',
                'company_ids' => [$companyId],
                'reconcile' => false,
                'deprecated' => false,
            ],
            251000 => [
                'code' => '251000',
                'display_name' => 'Tax Received',
                'name' => 'Tax Received',
                'account_type' => 'liability_current',
                'company_ids' => [$companyId],
                'reconcile' => false,
                'deprecated' => false,
            ],
            121000 => [
                'code' => '121000',
                'display_name' => 'Account Receivable',
                'name' => 'Account Receivable',
                'account_type' => 'asset_receivable',
                'company_ids' => [$companyId],
                'reconcile' => true,
                'deprecated' => false,
            ],
            211000 => [
                'code' => '211000',
                'display_name' => 'Account Payable',
                'name' => 'Account Payable',
                'account_type' => 'liability_payable',
                'company_ids' => [$companyId],
                'reconcile' => true,
                'deprecated' => false,
            ],
            101701 => [
                'code' => '101701',
                'display_name' => 'Liquidity Transfer',
                'name' => 'Liquidity Transfer',
                'account_type' => 'asset_current',
                'company_ids' => [$companyId],
                'reconcile' => true,
                'deprecated' => false,
            ],
            441000 => [
                'code' => '441000',
                'display_name' => 'Foreign Exchange Gain',
                'name' => 'Foreign Exchange Gain',
                'account_type' => 'income',
                'company_ids' => [$companyId],
                'reconcile' => false,
                'deprecated' => false,
            ],
            443000 => [
                'code' => '443000',
                'display_name' => 'Cash Discount Loss',
                'name' => 'Cash Discount Loss',
                'account_type' => 'expense',
                'company_ids' => [$companyId],
                'reconcile' => false,
                'deprecated' => false,
            ],
            641000 => [
                'code' => '641000',
                'display_name' => 'Foreign Exchange Loss',
                'name' => 'Foreign Exchange Loss',
                'account_type' => 'expense',
                'company_ids' => [$companyId],
                'reconcile' => false,
                'deprecated' => false,
            ],
            643000 => [
                'code' => '643000',
                'display_name' => 'Cash Discount Gain',
                'name' => 'Cash Discount Gain',
                'account_type' => 'income',
                'company_ids' => [$companyId],
                'reconcile' => false,
                'deprecated' => false,
            ]
        ];
        return $accounts[$code];
    }
}
