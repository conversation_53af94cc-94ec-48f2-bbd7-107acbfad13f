<?php

declare(strict_types=1);

namespace Brainst\Odoo\Service\SyncHelper;

use Brainst\Odoo\Service\OdooService;
use PhpXmlRpc\Exception\ValueErrorException;
use Shopware\Core\System\Currency\CurrencyEntity;

/**
 * Class CurrencyService
 * @package BrainstOdoo
 * <AUTHOR>
 * @link www.brainstreamtechnolabs.com
 */
class CurrencyService
{
    /**
     * Get/Generate Currency id from odoo
     *
     * @param OdooService $odooService
     * @param CurrencyEntity $currencyEntity
     * @return int
     * @throws ValueErrorException
     */
    public static function getOdooCountryId(OdooService $odooService, CurrencyEntity $currencyEntity): int
    {
        $odooRecord = $odooService->executeRpc('execute_kw', 'res.currency', 'search',
            [[['name', '=', $currencyEntity->getIsoCode()], ['active', 'in', [true, false]]]]);

        if (!$odooRecord) {
            $currencyData = [
                'active' => true,
                'name' => $currencyEntity->getIsoCode(),
                "position" => "after",
                "full_name" => $currencyEntity->getName(),
                "rounding" => $currencyEntity->getItemRounding()->getDecimals(),
                "symbol" => $currencyEntity->getSymbol(),
                "rate_ids" => [
                    [
                        0,
                        0,
                        [
                            "company_id" => false,
                            "company_rate" => 1,
                            "name" => date("Y-m-d"),
                            "rate" => 1
                        ]
                    ]
                ]
            ];
            return $odooService->executeRpc('execute', 'res.currency', 'create', $currencyData);
        }
        return $odooRecord[0]->scalarVal();
    }
}
