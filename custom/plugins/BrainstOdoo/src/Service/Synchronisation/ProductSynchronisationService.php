<?php

declare(strict_types=1);

namespace Brainst\Odoo\Service\Synchronisation;

use Brainst\Odoo\Exception\OdooSyncException;
use Brainst\Odoo\Extension\Content\Product\BrainstOdooExtensionEntity as BrainstOdooProductEntity;
use Brainst\Odoo\Extension\System\SalesChannel\BrainstOdooExtensionEntity as BrainstOdooSalesChannelEntity;
use Brainst\Odoo\Service\OdooService;
use PhpXmlRpc\Exception\StateErrorException;
use PhpXmlRpc\Exception\ValueErrorException;
use PhpXmlRpc\Value;
use Shopware\Core\Content\Category\CategoryEntity;
use Shopware\Core\Content\Product\ProductEntity;
use Shopware\Core\Framework\Context;
use Shopware\Core\Framework\DataAbstractionLayer\EntityRepository;
use Shopware\Core\Framework\DataAbstractionLayer\Pricing\Price;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Criteria;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Filter\EqualsAnyFilter;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Filter\EqualsFilter;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Sorting\FieldSorting;

/**
 * Class ProductSynchronisationService
 * @package BrainstOdoo
 * <AUTHOR>
 * @link www.brainstreamtechnolabs.com
 */
class ProductSynchronisationService implements SynchronisationInterface
{

    /**
     * @var int
     */
    private int $productTemplateId;
    /**
     * @var bool
     */
    private bool $updateVariants;

    /**
     * @var OdooService|null
     */
    private ?OdooService $odooServiceConnected;

    public function __construct(
        private readonly OdooService                        $odooService,
        private readonly EntityRepository                   $productRepository,
        private readonly EntityRepository                   $categoryRepository,
        private readonly EntityRepository                   $odooProductRepository,
        private readonly CategorySynchronisationService     $categorySynchronisationService,
        private readonly PropertySynchronisationService     $propertySynchronisationService,
        private readonly SalesChannelSynchronisationService $salesChannelSynchronisationService,
    )
    {
        $this->odooServiceConnected = null;
    }

    /**
     * Connect to odoo service
     *
     * @return void
     * @throws OdooSyncException|ValueErrorException
     */
    private function odooServiceConnect(): void
    {
        $this->odooServiceConnected = ($this->odooService)();
    }

    /**
     * Add/Update product changes to odoo and set odoo id to product
     *
     * @param string $recordId
     * @param Context $context
     * @return void
     * @throws OdooSyncException|ValueErrorException|StateErrorException
     */
    public function updateRecord(string $recordId, Context $context): void
    {
        if (!$this->odooServiceConnected) {
            $this->odooServiceConnect();
        }
        if (!$this->odooServiceConnected->hasService) {
            return;
        }
        echo "\nProduct sync start\n";

        $productCriteria = new Criteria([$recordId]);
        $productCriteria->addAssociations(['categories', 'visibilities', 'visibilities.salesChannel', 'children', 'children.options', 'children.options.group']);

        /** @var ProductEntity $product */
        $product = $this->productRepository->search($productCriteria, $context)->first();

        $odooCompanyId = 0;
        if ($product->getVisibilities()->count() === 1) {
            $salesChannel = $product->getVisibilities()->first()->getSalesChannel();
            if ($salesChannel->hasExtension('brainstOdooSalesChannel')) {

                /** @var BrainstOdooSalesChannelEntity $odooSalesChannel */
                $odooSalesChannel = $salesChannel->getExtension('brainstOdooSalesChannel');

                $odooCompanyId = $odooSalesChannel->getOdooCompanyId();
            } else {
                $this->salesChannelSynchronisationService->updateRecord($salesChannel->getId(), $context);

                /** @var ProductEntity $product */
                $product = $this->productRepository->search($productCriteria, $context)->first();
                $salesChannel = $product->getVisibilities()->first()->getSalesChannel();

                /** @var BrainstOdooSalesChannelEntity $odooSalesChannel */
                $odooSalesChannel = $salesChannel->getExtension('brainstOdooSalesChannel');
                $odooCompanyId = $odooSalesChannel->getOdooCompanyId();
            }
        }

        $parentId = $product->getParentId();
        $odooId = $this->getProductOdooId($product);
        if ($parentId && !$this->checkOdooRecordId($odooId)) {
            echo "\n Self product update parent\n";
            self::updateRecord($parentId, $context);
            $product = $this->productRepository->search($productCriteria, $context)->first();
            $odooId = $this->getProductOdooId($product);
        }

        $productCategoryId = $product->getCategories()->first()?->getId();

        // If category is not available in record then select random for odoo everytime it's updated
        if (!$productCategoryId) {
            $criteria = (new Criteria())
                ->addFields(['id'])
                ->addFilter(new EqualsFilter('active', true))
                ->addSorting(new FieldSorting('createdAt', FieldSorting::ASCENDING))
                ->setLimit(1);

            /** @var CategoryEntity $category */
            $category = $this->categoryRepository->search($criteria, $context)->first();
            $productCategoryId = $category ? $category->getId() : 0;
        }

        echo "\n Get or update category by product\n";
        $odooCategoryId = $this->getOrCreateOdooCategoryId($productCategoryId, $context);


        /** @var Price $price */
        $price = $product->getPrice()?->first();
        $purchasePrice = $product->getPurchasePrices()?->first();

        $product_data = [];
        if ($product->getName()) {
            $product_data['name'] = $product->getName();
        }

        if ($odooCategoryId) {
            $product_data['categ_id'] = $odooCategoryId;
        }

        if ($price && !$parentId) {
            $product_data['list_price'] = $price->getNet();
        }

        if ($purchasePrice && !$parentId) {
            $product_data['standard_price'] = $purchasePrice->getNet();
        }

        if (empty($product_data)) {
            return;
        }

        $description = $product->getDescription() ?: '';
        $product_data['description'] = $description;
        $product_data['description_purchase'] = $description;
        $product_data['description_sale'] = $description;
        $product_data['company_id'] = $odooCompanyId ?: "";
        $product_data['purchase_ok'] = true;
        $product_data['sale_ok'] = true;

        // Check and update for variant
        $variants = $product->getChildren()->getElements();
        $variantOptions = [];
        $existingVariants = [];
        $variantOptionIds = [];
        $updateGroupIds = [];
        $checkGroups = [];
        if (!$parentId && !empty($variants)) {
            /** @var ProductEntity $variant */
            foreach ($variants as $variant) {
                $existingVariants[] = $this->getProductOdooId($variant);
                $options = $variant->getOptions()->getElements();
                foreach ($options as $option) {
                    $attributeId = $this->propertySynchronisationService->getOdooPropertyGroupId($option->getGroup());
                    $attributeValueId = $this->propertySynchronisationService->getOdooPropertyGroupOptionId($option);
                    $variantOptions[$attributeId][] = $attributeValueId;

                    if (!$attributeId || !$attributeValueId) {
                        $updateGroupIds[] = $option->getGroupId();
                    } else {
                        $checkGroups[$option->getGroupId()][] = $attributeValueId;
                    }

                    $variantOptionIds[$variant->getId()][] = $attributeValueId;
                }
            }

            $checkGroupValues = array_map(fn($value) => array_values(array_unique($value)), $checkGroups);

            $mergedArrayValues = array_reduce($checkGroupValues, 'array_merge', []);


            $attrs = $this->odooServiceConnected->executeRpc('execute_kw', 'product.attribute.value', 'search',
                [[['id', 'in', $mergedArrayValues]]]);

            $attrIds = (is_array($attrs) && !empty($attrs)) ? array_map(fn($value) => $value?->scalarVal(),
                $attrs) : [];

            if (count($mergedArrayValues) !== count($attrIds)) {
                $diffArrayValues = array_diff($mergedArrayValues, $attrIds);
                foreach ($checkGroupValues as $checkGroup => $checkDiffGroupValue) {
                    $intersection = array_intersect($checkDiffGroupValue, $diffArrayValues);
                    if (!empty($intersection)) {
                        $updateGroupIds[] = $checkGroup;
                    }
                }
            }
            if (!empty($updateGroupIds)) {
                $updateGroupIds = array_unique($updateGroupIds);
                foreach ($updateGroupIds as $updateGroupId) {
                    echo "\n property update start by product \n";
                    $this->propertySynchronisationService->updateRecord($updateGroupId, $context);
                    echo "\n  property update end by product \n";
                }
                echo "\n Self product update restart for property \n";
                self::updateRecord($recordId, $context);
                return;
            }

            $attributeLineIds = [];
            foreach ($variantOptions as $key => $value) {
                $attributeLineIds[] = [
                    0,
                    0,
                    ['attribute_id' => $key, 'value_ids' => [[6, 0, array_values(array_unique($value))]]]
                ];
            }
            $product_data['attribute_line_ids'] = $attributeLineIds;
        }

        $newOdooId = $this->upsertOdoo($product_data, $odooId, $existingVariants, $parentId);
        $this->updateOdooId($recordId, $context, $newOdooId);

        // Variant Update START
        if (!$parentId && $this->updateVariants) {
            $productTemplateAttributeValues = $this->odooServiceConnected->executeRpc('execute_kw',
                'product.template.attribute.value', 'search_read',
                [
                    [['product_tmpl_id', '=', $this->productTemplateId]],
                    ['product_tmpl_id', 'product_attribute_value_id', 'ptav_product_variant_ids']
                ]);

            $finalOdooOptions = [];
            /** @var Value $attributeValue */
            foreach ($productTemplateAttributeValues as $attributeValue) {
                $variantIds = $attributeValue->offsetGet('ptav_product_variant_ids')->scalarVal();
                $attributeValueId = $attributeValue->offsetGet('product_attribute_value_id')->offsetGet(0)->scalarVal();

                foreach ($variantIds as $value) {
                    $finalOdooOptions[$value->scalarVal()][] = $attributeValueId;
                }
            }

            $updateRecords = [];
            $storedVariants = [];
            foreach ($variantOptionIds as $variantId => $variantOptionId) {
                foreach ($finalOdooOptions as $odooVariantId => $odooVariantOptions) {
                    if (empty(array_diff($odooVariantOptions, $variantOptionId)) && empty(array_diff($variantOptionId,
                            $odooVariantOptions))) {
                        $storedVariants[] = $odooVariantId;
                        $updateRecords[] =
                            [
                                'productId' => $variantId,
                                'odooProductId' => $odooVariantId
                            ];
                        break;
                    }
                }
            }
            $this->odooProductRepository->upsert($updateRecords, $context);

            // Delete extra variants from odoo
            $deleteOdooVariants = array_diff(array_keys($finalOdooOptions), array_unique($storedVariants));

            foreach ($deleteOdooVariants as $variantId) {
                $this->odooServiceConnected->executeRpc('execute', 'product.product', 'unlink',
                    [$variantId]);
            }
        }
        // Variant Update END
        echo "\nProduct sync end\n";
    }

    /**
     * Get odoo product id
     *
     * @param string $recordId
     * @param Context $context
     * @return int
     */
    public function getOdooRecordId(string $recordId, Context $context): int
    {
        $criteria = new Criteria();
        $criteria->addFilter(new EqualsAnyFilter('productId', [$recordId]));

        /** @var BrainstOdooProductEntity $odooProduct */
        $odooProduct = $this->odooProductRepository->search($criteria, $context)->first();

        return $odooProduct?->getOdooProductId() ?: 0;
    }

    /**
     * Check the Odoo record existence
     *
     * @param int $odooRecordId
     * @return bool|int
     * @throws OdooSyncException|ValueErrorException
     */
    public function checkOdooRecordId(int $odooRecordId): bool|int
    {
        if (!$this->odooServiceConnected) {
            $this->odooServiceConnect();
        }

        /** @var Value $odooProductRecord */
        $odooProductRecord = $this->odooServiceConnected->executeRpc('execute_kw', 'product.product', 'search',
            [[['id', '=', $odooRecordId]]]);
        return (!empty($odooProductRecord)) ? $odooProductRecord[0]->scalarVal() : false;
    }

    /**
     * Delete product from odoo
     *
     * @param string $recordId
     * @param Context $context
     * @param int $odooRecordId
     * @param bool $isVariant
     * @return void
     * @throws StateErrorException|ValueErrorException
     */
    public function deleteOdooRecord(
        string  $recordId,
        Context $context,
        int     $odooRecordId,
        bool    $isVariant = false
    ): void
    {
        if (!$this->odooServiceConnected) {
            $this->odooServiceConnect();
        }
        if (!$this->odooServiceConnected->hasService) {
            return;
        }

        /** @var ProductEntity $availableRecord */
        $availableRecord = $this->productRepository->search(new Criteria([$recordId]), $context)->first();

        if ($odooRecordId && !$availableRecord) {
            /** @var Value $product */
            $product = $this->odooServiceConnected->executeRpc('execute_kw', 'product.product', 'search_read',
                [[['id', '=', $odooRecordId]], ['product_tmpl_id']]);
            if (!$isVariant) {
                $this->odooServiceConnected->executeRpc('execute', 'product.template', 'unlink',
                    [$product[0]->offsetGet('product_tmpl_id')->offsetGet(0)->scalarVal()]);
            }
            $this->odooProductRepository->delete([['productId' => $recordId]], $context);
        }
    }


    /**
     * Create or Update the Odoo record
     *
     * @param array $odooFields
     * @param int $odooProductId
     * @param array $existingVariants
     * @param string|null $parentId
     * @return bool|int
     * @throws OdooSyncException|ValueErrorException|StateErrorException
     */
    public function upsertOdoo(
        array   $odooFields,
        int     $odooProductId,
        array   $existingVariants = [],
        ?string $parentId = null
    ): bool|int
    {
        if (!$this->odooServiceConnected) {
            $this->odooServiceConnect();
        }
        /** @var Value $odooProductRec */
        $odooProductRec = $this->odooServiceConnected->executeRpc('execute_kw', 'product.product', 'search_read',
            [
                [['id', '=', $odooProductId]],
                ['product_tmpl_id', 'product_variant_ids', 'attribute_line_ids']
            ]);
        if (!empty($odooProductRec)) {
            $this->productTemplateId = $odooProductRec[0]->offsetGet('product_tmpl_id')->offsetGet(0)->scalarVal();

            if (!empty($existingVariants)) {
                $variantIds = array_map(fn($value) => $value->scalarVal(),
                    $odooProductRec[0]->offsetGet('product_variant_ids')->scalarVal());
                $this->updateVariants = (array_key_exists('attribute_line_ids',
                        $odooFields) && !empty($odooFields['attribute_line_ids']) || !empty(array_diff($variantIds,
                        $existingVariants)) || !empty(array_diff($existingVariants, $variantIds)));

                if ($this->updateVariants) {
                    $variantIds = array_map(fn($value) => [3, $value->scalarVal()],
                        $odooProductRec[0]->offsetGet('attribute_line_ids')->scalarVal());
                    $odooFields['attribute_line_ids'] = array_merge($odooFields['attribute_line_ids'], $variantIds);
                }
            } else {
                $this->updateVariants = false;
            }

            if (!$parentId) {
                $this->odooServiceConnected->executeRpc('execute', 'product.template', 'write',
                    [$this->productTemplateId], $odooFields);
            } elseif (!empty($odooFields)) {
                $this->odooServiceConnected->executeRpc('execute', 'product.product', 'write',
                    [$odooProductId], $odooFields);
            }

            if ($this->updateVariants) {
                /** @var Value $odooProduct */
                $odooProduct = $this->odooServiceConnected->executeRpc('execute_kw', 'product.product', 'search', [[['product_tmpl_id', '=', $this->productTemplateId]]]);
                return $odooProduct[0]->scalarVal();
            }
            return false;
        } else {
            $templateId = $this->odooServiceConnected->executeRpc('execute', 'product.template', 'create', $odooFields);
            $this->productTemplateId = $templateId ?: 0;
            $this->updateVariants = !empty($existingVariants);

            /** @var Value $odooProduct */
            $odooProduct = $this->odooServiceConnected->executeRpc('execute_kw', 'product.product', 'search', [[['product_tmpl_id', '=', $templateId]]]);
            return $odooProduct[0]->scalarVal();
        }
    }


    /**
     * Add Odoo id to product custom field
     *
     * @param string $productId
     * @param Context $context
     * @param bool|int $odooId
     * @return void
     */
    private function updateOdooId(string $productId, Context $context, bool|int $odooId): void
    {
        if ($odooId) {
            $this->odooProductRepository->upsert([
                [
                    'productId' => $productId,
                    'odooProductId' => $odooId
                ]
            ], $context);
        }
    }

    /**
     * Get odoo category id for product
     *
     * @param string|null $categoryId
     * @param Context $context
     * @return int
     * @throws OdooSyncException|ValueErrorException
     */
    private function getOrCreateOdooCategoryId(?string $categoryId, Context $context): int
    {
        $odooCategoryId = 0;
        if ($categoryId) {
            $odooCategoryId = $this->categorySynchronisationService->getOdooRecordId($categoryId, $context);
            $odooRecordExist = $odooCategoryId ? $this->categorySynchronisationService->checkOdooRecordId($odooCategoryId) : false;
            if (!$odooRecordExist) {
                $this->categorySynchronisationService->updateRecord($categoryId, $context);
                $odooCategoryId = $this->categorySynchronisationService->getOdooRecordId($categoryId, $context);
            }
        }
        return $odooCategoryId;
    }


    /**
     * @param ProductEntity|null $productEntity
     * @return int
     */
    public function getProductOdooId(?ProductEntity $productEntity): int
    {
        $odooId = 0;
        if ($productEntity && $productEntity->hasExtension('brainstOdooProduct')) {

            /** @var BrainstOdooProductEntity $odooProduct */
            $odooProduct = $productEntity->getExtension('brainstOdooProduct');

            $odooId = $odooProduct?->getOdooProductId();
        }

        return $odooId ?: 0;
    }
}
