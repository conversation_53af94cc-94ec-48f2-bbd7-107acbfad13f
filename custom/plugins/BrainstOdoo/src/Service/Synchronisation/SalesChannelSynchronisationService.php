<?php

declare(strict_types=1);

namespace Brainst\Odoo\Service\Synchronisation;

use Brainst\Odoo\Exception\OdooSyncException;
use Brainst\Odoo\Extension\System\SalesChannel\BrainstOdooExtensionEntity as BrainstOdooSalesChannelEntity;
use Brainst\Odoo\Service\OdooService;
use Brainst\Odoo\Service\SyncHelper\CountryStateService;
use Brainst\Odoo\Service\SyncHelper\CurrencyService;
use Brainst\Odoo\Service\SyncHelper\JournalAccountServiceV17;
use Brainst\Odoo\Service\SyncHelper\JournalAccountServiceV18;
use Brainst\Odoo\Service\SyncHelper\WarehouseService;
use PhpXmlRpc\Exception\StateErrorException;
use PhpXmlRpc\Exception\ValueErrorException;
use Shopware\Core\Framework\Context;
use Shopware\Core\Framework\DataAbstractionLayer\EntityRepository;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Criteria;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Filter\EqualsAnyFilter;
use Shopware\Core\System\SalesChannel\SalesChannelEntity;
use Shopware\Core\System\SystemConfig\SystemConfigService;

/**
 * Class SalesChannelSynchronisationService
 * @package BrainstOdoo
 * <AUTHOR>
 * @link www.brainstreamtechnolabs.com
 */
class SalesChannelSynchronisationService implements SynchronisationInterface
{
    public function __construct(
        private readonly OdooService      $odooService,
        private readonly EntityRepository $salesChannelRepository,
        private readonly EntityRepository $odooSalesChannelRepository,
        private readonly SystemConfigService $configService,
    )
    {
        $this->odooServiceConnected = null;
    }

    /**
     * Connect to odoo service
     * @return void
     * @throws OdooSyncException|ValueErrorException
     */
    private function odooServiceConnect(): void
    {
        $this->odooServiceConnected = ($this->odooService)();
    }

    /**
     * Add/Update sales channel changes to odoo and set odoo id to sales channel
     *
     * @param string $recordId
     * @param Context $context
     * @return void
     * @throws OdooSyncException|ValueErrorException|StateErrorException
     */
    public function updateRecord(string $recordId, Context $context): void
    {
        if (!$this->odooServiceConnected) {
            $this->odooServiceConnect();
        }
        if (!$this->odooServiceConnected->hasService) return;

        echo "\nSales channel sync start\n";
        $criteria = new Criteria([$recordId]);
        $criteria->addAssociations(['country', 'currency']);

        /** @var SalesChannelEntity $salesChannelData */
        $salesChannelData = $this->salesChannelRepository->search($criteria, $context)->first();

        $odooCountryId = CountryStateService::getOdooCountryId($this->odooServiceConnected, $salesChannelData->getCountry());
        $odooCurrencyId = CurrencyService::getOdooCountryId($this->odooServiceConnected, $salesChannelData->getCurrency());


        $odooId = $this->getOdooId($salesChannelData);

        $createFields = [
            'name' => $this->getUniqueName($salesChannelData->getName(), $odooId),
            'country_id' => $odooCountryId,
            'currency_id' => $odooCurrencyId
        ];
        $newOdooId = $this->upsertOdoo($createFields, $odooId);

        $this->updateOdooId($recordId, $context, $newOdooId);

        $companyId = $newOdooId ?: $odooId;
        if ($this->configService->get('BrainstOdoo.config.odooVersion') === 'v18') {
            WarehouseService::createWarehouse($this->odooServiceConnected, $companyId);
            $customFields = JournalAccountServiceV18::createAccounts($this->odooServiceConnected, $companyId);
        } else {
            $customFields = JournalAccountServiceV17::createAccounts($this->odooServiceConnected, $companyId);
        }

        $this->updateOdooId($recordId, $context, $companyId, $customFields);
        echo "\nSales channel sync end\n";
    }

    /**
     * Get odoo company id
     *
     * @param string $recordId
     * @param Context $context
     * @return int
     */
    public function getOdooRecordId(string $recordId, Context $context): int
    {
        $criteria = new Criteria();
        $criteria->addFilter(new EqualsAnyFilter('salesChannelId', [$recordId]));

        /** @var BrainstOdooSalesChannelEntity $salesChannel */
        $salesChannel = $this->odooSalesChannelRepository->search($criteria, $context)->first();

        return $salesChannel?->getOdooCompanyId() ?: 0;
    }

    /**
     * Delete company from odoo
     *
     * @param string $recordId
     * @param Context $context
     * @param int $odooRecordId
     * @return void
     * @throws OdooSyncException|ValueErrorException
     */
    public function deleteOdooRecord(string $recordId, Context $context, int $odooRecordId): void
    {
        if (!$this->odooServiceConnected) {
            $this->odooServiceConnect();
        }
        if (!$this->odooServiceConnected->hasService) return;

        $availableRecord = $this->salesChannelRepository->search(new Criteria([$recordId]), $context)->first();

        if ($odooRecordId && !$availableRecord) {
            $this->odooServiceConnected->executeRpc('execute', 'res.company', 'unlink', [$odooRecordId]);
            $this->odooSalesChannelRepository->delete([['salesChannelId' => $recordId]], $context);
        }
    }

    /**
     * Create or Update the Odoo record
     *
     * @param array $odooData
     * @param int $odooId
     * @return bool|int
     * @throws OdooSyncException|ValueErrorException
     */
    private function upsertOdoo(array $odooData, int $odooId): bool|int
    {
        $odooRec = $this->odooServiceConnected->executeRpc('execute_kw', 'res.company', 'search',
            [[['id', '=', $odooId]]]);

        if (!empty($odooRec)) {
            $this->odooServiceConnected->executeRpc('execute', 'res.company', 'write', [$odooId], $odooData);
            return false;
        } else {
            return $this->odooServiceConnected->executeRpc('execute', 'res.company', 'create', $odooData);
        }
    }

    /**
     * Add Odoo id to sales channel extension
     *
     * @param string $salesChannelId
     * @param Context $context
     * @param bool|int $odooId
     * @param array $customFields
     * @return void
     */
    private function updateOdooId(string $salesChannelId, Context $context, bool|int $odooId, array $customFields = []): void
    {
        if ($odooId) {
            $criteria = new Criteria();
            $criteria->addFilter(new EqualsAnyFilter('salesChannelId', [$salesChannelId]));

            /** @var BrainstOdooSalesChannelEntity $odooSalesChannel */
            $odooSalesChannel = $this->odooSalesChannelRepository->search($criteria, $context)->first();

            $availableCustomFields = $odooSalesChannel?->getCustomFields() ?: [];
            $updateCustomFields = array_merge($availableCustomFields, $customFields);

            $updateData = ['salesChannelId' => $salesChannelId, 'odooCompanyId' => $odooId];
            if (!empty($updateCustomFields)) {
                $updateData['customFields'] = $updateCustomFields;
            }

            $this->odooSalesChannelRepository->upsert([$updateData], $context);
        }
    }

    /**
     * @param SalesChannelEntity $salesChannelEntity
     * @return int
     */
    private function getOdooId(SalesChannelEntity $salesChannelEntity): int
    {
        $odooId = 0;
        if ($salesChannelEntity->hasExtension('brainstOdooSalesChannel')) {
            /** @var BrainstOdooSalesChannelEntity $odooSalesChannel */
            $odooSalesChannel = $salesChannelEntity->getExtension('brainstOdooSalesChannel');
            $odooId = $odooSalesChannel?->getOdooCompanyId();
        }

        return $odooId ?: 0;
    }


    /**
     * Get Unique name for odoo company
     *
     * @param string $name
     * @param int $odooId
     * @return string
     * @throws ValueErrorException
     */
    private function getUniqueName(string $name, int $odooId): string
    {
        $searchConditions = [['name', 'ilike', $name]];
        if ($odooId) {
            $searchConditions[] = ['id', '!=', $odooId];
        }
        $existingOdooCompanies = $this->odooServiceConnected->executeRpc('execute_kw', 'res.company', 'search_read',
            [$searchConditions, ['name']]);
        if (!empty($existingOdooCompanies)) {
            $existingOdooCompanyList = array_keys(OdooService::getPluck($existingOdooCompanies, 'name'));
            return self::createUniqueName($existingOdooCompanyList, $name);
        }
        return $name;
    }

    /**
     * Check and if exist same name then create new name for company
     *
     * @param array $existingOdooCompanyList
     * @param string $name
     * @param int $increment
     * @return string
     */
    private static function createUniqueName(array $existingOdooCompanyList, string $name, int $increment = 0): string
    {
        $newName = $increment ? $name . $increment : $name;
        if (in_array($newName, $existingOdooCompanyList)) {
            return self::createUniqueName($existingOdooCompanyList, $name, $increment + 1);
        }
        return $newName;
    }
}
