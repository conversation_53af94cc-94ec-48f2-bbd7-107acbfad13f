<?php

declare(strict_types=1);

namespace Brainst\Odoo\Service\Synchronisation;

use Shopware\Core\Framework\Context;

/**
 * Class OrderStatusChangeInterface
 * @package BrainstOdoo
 * <AUTHOR>
 * @link www.brainstreamtechnolabs.com
 */
interface OrderStatusChangeInterface
{
    /**
     * Order status changes to odoo
     *
     * @param string $orderId
     * @param Context $context
     * @param string $statusType
     * @return void
     */
    public function orderStatusChange(string $orderId, Context $context, string $statusType): void;

}