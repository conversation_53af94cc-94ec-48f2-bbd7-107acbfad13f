<?php

declare(strict_types=1);

namespace Brainst\Odoo\MessageQueue\Handler;

use Brainst\Odoo\MessageQueue\Message\SalesChannelDataMessage;
use Brainst\Odoo\Model\Operation;
use Shopware\Core\Framework\Api\Context\SystemSource;
use Shopware\Core\Framework\Context;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;

/**
 * Class SalesChannelDataHandler
 * @package BrainstOdoo
 * <AUTHOR>
 * @link www.brainstreamtechnolabs.com
 */
#[AsMessageHandler]
class SalesChannelDataHandler extends AbstractDataHandler
{
    /**
     * Handle sales channel sync with odoo
     *
     * @param SalesChannelDataMessage $message
     * @return void
     */
    public function __invoke(SalesChannelDataMessage $message): void
    {
        $operation = Operation::load($message->getOperation());
        $context = new Context(new SystemSource());

        switch ($operation) {
            case $operation->equals(Operation::update()):
            case $operation->equals(Operation::insert()):
                foreach ($message->getSalesChannelIds() as $salesChannelId) {
                    $this->synchronisationService->updateRecord($salesChannelId, $context);
                }
                break;
            case $operation->equals(Operation::delete()):
                $odooCompanyIds = $message->getOdooCompanyIds();
                foreach ($message->getSalesChannelIds() as $salesChannelId) {
                    $this->synchronisationService->deleteOdooRecord($salesChannelId, $context,
                        $odooCompanyIds[$salesChannelId]);
                }
        }
    }

}