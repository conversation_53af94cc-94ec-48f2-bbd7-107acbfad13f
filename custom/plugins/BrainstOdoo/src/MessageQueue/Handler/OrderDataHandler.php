<?php

declare(strict_types=1);

namespace Brainst\Odoo\MessageQueue\Handler;

use Brainst\Odoo\MessageQueue\Message\OrderDataMessage;
use Brainst\Odoo\Model\Operation;
use Shopware\Core\Framework\Api\Context\SystemSource;
use Shopware\Core\Framework\Context;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;

/**
 * Class OrderDataHandler
 * @package BrainstOdoo
 * <AUTHOR>
 * @link www.brainstreamtechnolabs.com
 */
#[AsMessageHandler]
class OrderDataHandler extends AbstractDataHandler
{
    /**
     * Handle order sync with odoo
     *
     * @param OrderDataMessage $message
     * @return void
     */
    public function __invoke(OrderDataMessage $message): void
    {
        $operation = Operation::load($message->getOperation());
        $context = new Context(new SystemSource());

        switch ($operation) {
            case $operation->equals(Operation::update()):
            case $operation->equals(Operation::insert()):
                foreach ($message->getOrderIds() as $orderId) {
                    $this->synchronisationService->updateRecord($orderId, $context);
                }
                break;
            case $operation->equals(Operation::statusChange()):
                $statusType = $message->getStatusTypes();
                foreach ($message->getOrderIds() as $orderId) {
                    $this->synchronisationService->orderStatusChange($orderId, $context, $statusType[$orderId]);
                }
                break;
            case $operation->equals(Operation::delete()):
                $odooOrderIds = $message->getOdooOrderIds();
                foreach ($message->getOrderIds() as $orderId) {
                    $this->synchronisationService->deleteOdooRecord($orderId, $context,
                        $odooOrderIds[$orderId]);
                }
        }
    }
}