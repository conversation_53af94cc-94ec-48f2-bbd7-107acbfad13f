<?php
declare(strict_types=1);

namespace Brainst\Odoo\MessageQueue\Message;

use Shopware\Core\Framework\MessageQueue\AsyncMessageInterface;

/**
 * Class ProductDataMessage
 * @package BrainstOdoo
 * <AUTHOR>
 * @link www.brainstreamtechnolabs.com
 */
class ProductDataMessage implements AsyncMessageInterface
{
    /**
     * Property added for checking event for log entry
     *
     * @var string
     */
    public string $messageType = 'odoo_sync_message';

    public function __construct(
        private readonly array  $productIds,
        private readonly string $operation,
        private readonly array  $odooProductIds,
        private readonly array  $isVariants
    )
    {
    }

    /**
     * Create Object statically
     * @param array $productIds
     * @param string $operation
     * @param array $odooProductIds
     * @param array $isVariants
     * @return ProductDataMessage
     */
    public static function forProductIdsWithOperation(
        array  $productIds,
        string $operation,
        array  $odooProductIds = [],
        array  $isVariants = [],
    ): ProductDataMessage
    {
        return new self($productIds, $operation, $odooProductIds, $isVariants);
    }

    /**
     * @return array
     */
    public function getProductIds(): array
    {
        return $this->productIds;
    }

    /**
     * @return array
     */
    public function getOdooProductIds(): array
    {
        return $this->odooProductIds;
    }

    /**
     * @return string
     */
    public function getOperation(): string
    {
        return $this->operation;
    }

    /**
     * @return array
     */
    public function getIsVariants(): array
    {
        return $this->isVariants;
    }
}