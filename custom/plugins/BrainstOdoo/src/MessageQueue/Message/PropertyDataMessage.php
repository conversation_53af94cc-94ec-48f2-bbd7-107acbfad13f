<?php
declare(strict_types=1);

namespace Brainst\Odoo\MessageQueue\Message;

use Shopware\Core\Framework\MessageQueue\AsyncMessageInterface;

/**
 * Class PropertyDataMessage
 * @package BrainstOdoo
 * <AUTHOR>
 * @link www.brainstreamtechnolabs.com
 */
class PropertyDataMessage implements AsyncMessageInterface
{
    /**
     * Property added for checking event for log entry
     *
     * @var string
     */
    public string $messageType = 'odoo_sync_message';

    public function __construct(
        private readonly array  $propertyIds,
        private readonly string $operation,
        private readonly array  $odooPropertyIds
    )
    {
    }

    /**
     * Create Object statically
     * @param array $propertyIds
     * @param string $operation
     * @param array $odooPropertyIds
     * @return PropertyDataMessage
     */
    public static function forPropertyIdsWithOperation(
        array  $propertyIds,
        string $operation,
        array  $odooPropertyIds = [],
    ): PropertyDataMessage
    {
        return new self($propertyIds, $operation, $odooPropertyIds);
    }

    /**
     * @return array
     */
    public function getPropertyIds(): array
    {
        return $this->propertyIds;
    }

    /**
     * @return array
     */
    public function getOdooPropertyIds(): array
    {
        return $this->odooPropertyIds;
    }

    /**
     * @return string
     */
    public function getOperation(): string
    {
        return $this->operation;
    }
}