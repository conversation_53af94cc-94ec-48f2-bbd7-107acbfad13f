<?php
declare(strict_types=1);

namespace Brainst\Odoo\MessageQueue\Message;

use Shopware\Core\Framework\MessageQueue\AsyncMessageInterface;

/**
 * Class SalesChannelDataMessage
 * @package BrainstOdoo
 * <AUTHOR>
 * @link www.brainstreamtechnolabs.com
 */
class SalesChannelDataMessage implements AsyncMessageInterface
{
    /**
     * Property added for checking event for log entry
     *
     * @var string
     */
    public string $messageType = 'odoo_sync_message';

    public function __construct(
        private readonly array  $salesChannelIds,
        private readonly string $operation,
        private readonly array  $odooCompanyIds,
    )
    {
    }

    /**
     * Create Object statically
     *
     * @param array $salesChannelIds
     * @param string $operation
     * @param array $odooCompanyIds
     * @return SalesChannelDataMessage
     */
    public static function forSalesChannelIdsWithOperation(
        array  $salesChannelIds,
        string $operation,
        array  $odooCompanyIds = [],
    ): SalesChannelDataMessage
    {
        return new self($salesChannelIds, $operation, $odooCompanyIds);
    }

    /**
     * @return array
     */
    public function getSalesChannelIds(): array
    {
        return $this->salesChannelIds;
    }

    /**
     * @return array
     */
    public function getOdooCompanyIds(): array
    {
        return $this->odooCompanyIds;
    }

    /**
     * @return string
     */
    public function getOperation(): string
    {
        return $this->operation;
    }
}