<?php

declare(strict_types=1);

namespace Brainst\Odoo;

use Doctrine\DBAL\Exception;
use Shopware\Core\Framework\Plugin;
use Shopware\Core\Framework\Plugin\Context\ActivateContext;
use Shopware\Core\Framework\Plugin\Context\DeactivateContext;
use Shopware\Core\Framework\Plugin\Context\InstallContext;
use Shopware\Core\Framework\Plugin\Context\UninstallContext;
use Shopware\Core\Kernel;

/**
 * Class BrainstOdoo
 * @package BrainstOdoo
 * <AUTHOR>
 * @link www.brainstreamtechnolabs.com
 */
class BrainstOdoo extends Plugin
{
    /**
     * @param InstallContext $installContext
     * @return void
     */
    public function install(InstallContext $installContext): void
    {
        parent::install($installContext);

        $migrationCollection = $installContext->getMigrationCollection();
        $migrationCollection->migrateInPlace(1717575003);
    }

    /**
     * @param UninstallContext $uninstallContext
     * @return void
     * @throws Exception
     */
    public function uninstall(UninstallContext $uninstallContext): void
    {
        parent::uninstall($uninstallContext);

        if ($uninstallContext->keepUserData()) {
            return;
        }

        $connection = Kernel::getConnection();
        $connection->executeStatement('DROP TABLE IF EXISTS `brainst_odoo_category`');
        $connection->executeStatement('DROP TABLE IF EXISTS `brainst_odoo_product`');
        $connection->executeStatement('DROP TABLE IF EXISTS `brainst_odoo_property_group`');
        $connection->executeStatement('DROP TABLE IF EXISTS `brainst_odoo_property_group_option`');
        $connection->executeStatement('DROP TABLE IF EXISTS `brainst_odoo_order`');
        $connection->executeStatement('DROP TABLE IF EXISTS `brainst_odoo_order_delivery`');
        $connection->executeStatement('DROP TABLE IF EXISTS `brainst_odoo_customer`');
        $connection->executeStatement('DROP TABLE IF EXISTS `brainst_odoo_customer_address`');
        $connection->executeStatement('DROP TABLE IF EXISTS `brainst_odoo_sales_channel`');
    }

    /**
     * @param ActivateContext $activateContext
     * @return void
     */
    public function activate(ActivateContext $activateContext): void
    {
        parent::activate($activateContext);
    }

    /**
     * @param DeactivateContext $deactivateContext
     * @return void
     */
    public function deactivate(DeactivateContext $deactivateContext): void
    {
        parent::deactivate($deactivateContext);
    }

    /**
     * @return bool
     */
    public function executeComposerCommands(): bool
    {
        return true;
    }
}
