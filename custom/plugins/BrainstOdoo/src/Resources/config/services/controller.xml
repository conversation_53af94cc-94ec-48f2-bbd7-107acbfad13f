<?xml version="1.0" ?>
<container xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
           xmlns="http://symfony.com/schema/dic/services"
           xsi:schemaLocation="http://symfony.com/schema/dic/services http://symfony.com/schema/dic/services/services-1.0.xsd">
    <services>
        <service id="Brainst\Odoo\Controller\Api\OdooApiController">
            <argument type="service" id="Brainst\Odoo\Util\Logger" on-invalid="ignore"/>
            <argument type="service" id="brainst.odoo_service"/>
            <tag name="controller.service_arguments"/>
        </service>

        <service id="Brainst\Odoo\Controller\Api\SynchronizationController">
            <argument type="service" id="Brainst\Odoo\Util\Logger" on-invalid="ignore"/>
            <argument type="service" id="Shopware\Core\System\SystemConfig\SystemConfigService"/>
            <argument type="service" id="brainst.odoo_service"/>
            <argument type="service" id="brainst.odoo.sync_queue.category_sync_queue_service"/>
            <argument type="service" id="brainst.odoo.sync_queue.product_sync_queue_service"/>
            <argument type="service" id="brainst.odoo.sync_queue.order_sync_queue_service"/>
            <argument type="service" id="brainst.odoo.sync_queue.sales_channel_sync_queue_service"/>
            <argument type="service" id="brainst.odoo.sync_queue.customer_sync_queue_service" />
            <tag name="controller.service_arguments"/>
        </service>
    </services>
</container>