<?xml version="1.0" ?>
<container xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
           xmlns="http://symfony.com/schema/dic/services"
           xsi:schemaLocation="http://symfony.com/schema/dic/services http://symfony.com/schema/dic/services/services-1.0.xsd">
    <services>
        <service id="Brainst\Odoo\Subscriber\ListenToCategoryChanges">
            <argument type="service" id="brainst.odoo.category_synchronisation_service"/>
            <argument type="service" id="brainst.odoo.chunk_dispatch.category_sync_service"/>
            <tag name="kernel.event_subscriber"/>
        </service>

        <service id="Brainst\Odoo\Subscriber\ListenToProductChanges">
            <argument type="service" id="request_stack"/>
            <argument type="service" id="brainst.odoo.product_synchronisation_service"/>
            <argument type="service" id="brainst.odoo.chunk_dispatch.product_sync_service"/>
            <tag name="kernel.event_subscriber"/>
        </service>

        <service id="Brainst\Odoo\Subscriber\ListenToPropertyChanges">
            <argument type="service" id="brainst.odoo.property_synchronisation_service"/>
            <argument type="service" id="brainst.odoo.chunk_dispatch.property_sync_service"/>
            <tag name="kernel.event_subscriber"/>
        </service>

        <service id="Brainst\Odoo\Subscriber\ListenToOrderChanges">
            <argument type="service" id="request_stack"/>
            <argument type="service" id="brainst.odoo.order_synchronisation_service"/>
            <argument type="service" id="brainst.odoo.chunk_dispatch.order_sync_service"/>
            <tag name="kernel.event_subscriber"/>
        </service>

        <service id="Brainst\Odoo\Subscriber\ListenToCustomerChanges">
            <argument type="service" id="request_stack"/>
            <argument type="service" id="brainst.odoo.customer_synchronisation_service"/>
            <argument type="service" id="brainst.odoo.chunk_dispatch.customer_sync_service"/>
            <tag name="kernel.event_subscriber"/>
        </service>

        <service id="Brainst\Odoo\Subscriber\ListenToSalesChannelChanges">
            <argument type="service" id="brainst.odoo.sales_channel_synchronisation_service"/>
            <argument type="service" id="brainst.odoo.chunk_dispatch.sales_channel_sync_service"/>
            <tag name="kernel.event_subscriber"/>
        </service>

        <service id="Brainst\Odoo\Subscriber\ListenToMessageHandlerFails">
            <argument type="service" id="Brainst\Odoo\Util\Logger" on-invalid="ignore"/>
            <tag name="kernel.event_subscriber"/>
        </service>
    </services>
</container>