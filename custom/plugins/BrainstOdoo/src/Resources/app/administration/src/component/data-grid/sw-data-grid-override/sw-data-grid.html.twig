{% block sw_data_grid_columns_render_router_link %}
    <template v-if="column.routerLink">
        <router-link
                v-if="column.routerLink !== null && typeof column.routerLink === 'object' && column.routerLink.id"
                class="sw-data-grid__cell-value"
                :to="{ name: column.routerLink.name, params: { id: getRouteId(item, column.routerLink.id) } }"
        >
            {{ renderColumn(item, column) }}
        </router-link>
        <router-link
                v-else
                class="sw-data-grid__cell-value"
                :to="{ name: column.routerLink, params: { id: item.id } }"
        >
            {{ renderColumn(item, column) }}
        </router-link>
    </template>
{% endblock %}