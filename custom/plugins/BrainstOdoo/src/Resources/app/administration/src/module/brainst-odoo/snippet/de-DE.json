{"brainst-odoo": {"title": "Brainst Odoo", "description": "Shopware Odoo Synchronisierungsdaten von <PERSON>, Vertriebskanälen, Lieferung, Eigentum, Produkten, Kunden und Bestellungen.", "list": {"title": "Brainst Odoo", "sales-channel": "Vertriebskanaltabelle", "customer": "<PERSON><PERSON><PERSON><PERSON>", "customer-address": "Kundenadresstabelle", "category": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "product": "Produkttabelle", "order": "Bestell-/Rechnungstabelle", "delivery": "<PERSON><PERSON><PERSON><PERSON>", "property-group": "Eigenschaftsgruppentabelle", "property-group-option": "Optionstabelle für Eigenschaftsgruppen"}, "sales-channel": {"title": "Vertriebskanaltabelle", "table": {"id": "Vertriebskanal-ID", "odoo-id": "Odoo-Firmen-ID", "name": "Name des Vertriebskanals"}}, "customer": {"title": "<PERSON><PERSON><PERSON><PERSON>", "table": {"id": "Kundennummer", "odoo-id": "Odoo-Kunden-ID", "name": "Kundenname"}}, "customer-address": {"title": "Kundenadresstabelle", "table": {"id": "Kundenadress-ID", "odoo-id": "Odoo-Kundenadress-ID", "name": "Name der Kundenadresse"}}, "category": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "table": {"id": "Kategorie ID", "odoo-id": "Odoo-Kategorie-ID", "name": "<PERSON><PERSON><PERSON><PERSON>"}}, "product": {"title": "Produkttabelle", "table": {"id": "Produkt ID", "odoo-id": "Odoo-Produkt-ID", "name": "Produktname", "parent-id": "Parent Id"}}, "order": {"title": "Bestelltabelle", "table": {"id": "Bestell ID", "odoo-id": "Odoo-Bestell-ID", "odoo-invoice-id": "Odoo-Rechnungs-ID", "number": "Bestellnummer"}}, "delivery": {"title": "<PERSON><PERSON><PERSON><PERSON>", "table": {"id": "<PERSON><PERSON><PERSON><PERSON>", "odoo-id": "Odoo-Liefer-ID", "order-number": "Bestellnummer"}}, "property-group": {"title": "Eigenschaftsgruppentabelle", "table": {"id": "Eigenschaftsgruppen-ID", "odoo-id": "Odoo-Attribut-ID", "name": "Name der Eigenschaftsgruppe"}}, "property-group-option": {"title": "Optionstabelle für Eigenschaftsgruppen", "table": {"id": "Options-ID der Eigenschaftsgruppe", "odoo-id": "Odoo-Attributwert-ID", "name": "Name der Eigenschaftsoption", "group-name": "Name der Eigenschaftsgruppe"}}, "api-verify-button": {"title": "API-Test", "buttonLabel": "Überprüfen Sie die API-Verbindung", "success": "Verbindung mit Erfolg getestet", "error": "Die Verbindung konnte nicht hergestellt werden. Bitte überprüfen Sie die Zugangsdaten", "error-company": "Odoo-Firma nicht gefunden. Bitte überprüfen Sie die Zugangsdaten"}, "synchronize-button": {"title": "Synchroni<PERSON><PERSON>", "description": "Die Verarbeitung der Warteschlange wird einige Zeit in Anspruch nehmen. Führen Sie dies daher bitte aus, wenn die Nutzung Ihrer Website für einige Zeit minimal ist.<br/>Die geschätzte Zeit beträgt 1000 Produkte pro Stunde*.<br/><br/>Dies funktioniert genauso wie Befehle.<br/>Das Gleiche können Sie mit Befehlen in derselben Reihenfolge erreichen<br/><br/>1. <b>brainst-odoo:sales-channel-sync</b><br/>2. <b>brainst-odoo:customer-sync</b><br/>3. <b>brainst-odoo:category-sync</b><br/>4. <b>brainst-odoo:product-sync</b><br/>5. <b>brainst-odoo:order-sync</b>", "success": "Alle Entitäten werden zur Synchronisierung in die Warteschlange gestellt", "error_1": "<PERSON>te richten Sie die Odoo-Konfiguration ein und versuchen Sie es dann erneut", "error_2": "Bei der Synchronisierung ist ein Fehler aufgetreten. Bitte versuchen Sie es erneut", "error_3": "Sie haben die erste Synchronisierung bereits durchgeführt oder sind dabei"}}}