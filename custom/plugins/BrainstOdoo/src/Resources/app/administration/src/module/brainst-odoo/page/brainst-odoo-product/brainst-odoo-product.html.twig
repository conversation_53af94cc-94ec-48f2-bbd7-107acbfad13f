<sw-page>
    <template #smart-bar-header>
        <h2>{{ $tc('brainst-odoo.product.title') }}</h2>
    </template>

    <template #content>
        {% block postal_code_list_content %}
            <sw-entity-listing
                    v-if="brainstOdooEntries"
                    :items="brainstOdooEntries"
                    :repository="brainstOdooRepository"
                    :showSelection="false"
                    :columns="columns"
                    :allowDelete="false">
                <template #column-product.name="{ item, column }">
                    <router-link
                            class="sw-data-grid__cell-value"
                            :to="{ name: column?.routerLink, params: { id: item.id } }"
                    >
                        <span v-if="item?.product?.name || !item?.product?.parentId">
                            {{ item?.product?.name }}
                        </span>
                        <span v-else>
                        <template v-for="(value, key) in item?.product?.options">
                            {{ value.group.name }}: {{ value.name }}
                            {{ item?.product?.options.length - 1 > key ? "|" : "" }}
                        </template>
                    </span>
                    </router-link>
                </template>
            </sw-entity-listing>
        {% endblock %}
    </template>
</sw-page>
