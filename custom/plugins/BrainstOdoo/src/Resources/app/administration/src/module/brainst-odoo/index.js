import './page/brainst-odoo-list';
import './page/brainst-odoo-sales-channel';
import './page/brainst-odoo-customer';
import './page/brainst-odoo-customer-address';
import './page/brainst-odoo-category';
import './page/brainst-odoo-product';
import './page/brainst-odoo-order';
import './page/brainst-odoo-delivery';
import './page/brainst-odoo-property-group';
import './page/brainst-odoo-property-group-option';
import './component/brainst-odoo-api-verify-button';
import './component/brainst-odoo-synchronize-button'

import deDE from './snippet/de-DE';
import enGB from './snippet/en-GB';

const { Module } = Shopware;

Module.register('brainst-odoo', {
    type: 'plugin',
    name: 'brainst-odoo.title',
    title: 'brainst-odoo.title',
    description: 'brainst-odoo.description',
    color: '#9AA8B5',
    icon: 'regular-database',
    favicon: 'icon-module-settings.png',

    snippets: {
        'de-DE': deDE,
        'en-GB': enGB
    },

    routes: {
        list: {
            component: 'brainst-odoo-list',
            path: 'list',
            meta: {
                parentPath: 'sw.settings.index.plugins'
            }
        },
        salesChannel: {
            component: 'brainst-odoo-sales-channel',
            path: 'sales-channel',
            meta: {
                parentPath: 'brainst.odoo.list'
            }
        },
        customer: {
            component: 'brainst-odoo-customer',
            path: 'customer',
            meta: {
                parentPath: 'brainst.odoo.list'
            }
        },
        customerAddress: {
            component: 'brainst-odoo-customer-address',
            path: 'customer-address',
            meta: {
                parentPath: 'brainst.odoo.list'
            }
        },
        category: {
            component: 'brainst-odoo-category',
            path: 'category',
            meta: {
                parentPath: 'brainst.odoo.list'
            }
        },
        product: {
            component: 'brainst-odoo-product',
            path: 'product',
            meta: {
                parentPath: 'brainst.odoo.list'
            }
        },
        order: {
            component: 'brainst-odoo-order',
            path: 'order',
            meta: {
                parentPath: 'brainst.odoo.list'
            }
        },
        delivery: {
            component: 'brainst-odoo-delivery',
            path: 'delivery',
            meta: {
                parentPath: 'brainst.odoo.list'
            }
        },
        propertyGroup: {
            component: 'brainst-odoo-property-group',
            path: 'property-group',
            meta: {
                parentPath: 'brainst.odoo.list'
            }
        },
        propertyGroupOption: {
            component: 'brainst-odoo-property-group-option',
            path: 'property-group-option',
            meta: {
                parentPath: 'brainst.odoo.list'
            }
        }
    },

    settingsItem: [
        {
            group: 'plugins',
            to: 'brainst.odoo.list',
            icon: 'regular-database',
            label: 'brainst-odoo.title'
        }
    ],

    extensionEntryRoute: {
        extensionName: 'BrainstOdoo',
        route: 'brainst.odoo.list'
    }
});
