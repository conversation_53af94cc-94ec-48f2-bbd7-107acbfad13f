import template from './brainst-odoo-delivery.html.twig';

// const {Component, Mixin} = Shopware;
const Criteria = Shopware.Data.Criteria;

Shopware.Component.register('brainst-odoo-delivery', {
    template,

    inject: ['repositoryFactory'],

    data() {
        return {
            page: 1,
            limit: 25,
            brainstOdooEntries: null,
            total: 0,
            isLoading: true,
            currentLanguageId: Shopware.Context.api.languageId,
        };
    },

    metaInfo() {
        return {
            title: this.$createTitle(),
        };
    },

    created() {
        this.getList();
    },

    computed: {
        brainstOdooRepository() {
            return this.repositoryFactory.create('brainst_odoo_order_delivery');
        },

        columns() {
            return [
                {
                    allowResize: true,
                    dataIndex: "orderDeliveryId",
                    label: this.$tc('brainst-odoo.delivery.table.id'),
                    primary: true,
                    property: "orderDeliveryId",
                },
                {
                    allowResize: true,
                    dataIndex: "orderDelivery.order.orderNumber",
                    label: this.$tc('brainst-odoo.delivery.table.order-number'),
                    property: "orderDelivery.order.orderNumber",
                    routerLink: {name: 'sw.category.detail', id: 'orderDelivery.order.id'},
                },
                {
                    allowResize: true,
                    dataIndex: "odooOrderDeliveryId",
                    label: this.$tc('brainst-odoo.delivery.table.odoo-id'),
                    property: "odooOrderDeliveryId",
                },
            ];
        },
    },

    methods: {
        getList() {
            this.isLoading = true;
            const criteria = new Criteria(this.page, this.limit);
            criteria.addAssociation('orderDelivery');
            criteria.addFields('orderDeliveryId', 'odooOrderDeliveryId', 'orderDelivery.order.orderNumber', 'order.orderNumber', 'orderNumber');
            criteria.addSorting(Criteria.sort('orderDeliveryId', 'DESC', false));

            return this.brainstOdooRepository.search(criteria, Shopware.Context.api).then((result) => {
                this.total = result.total;
                this.brainstOdooEntries = result;
                this.isLoading = false;
            });
        },
    },
});

