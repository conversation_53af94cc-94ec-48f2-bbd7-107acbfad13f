<?php
declare(strict_types=1);

namespace Brainst\Odoo\Extension\Checkout\Customer;

use Shopware\Core\Checkout\Customer\CustomerEntity;
use Shopware\Core\Framework\DataAbstractionLayer\Entity;
use Shopware\Core\Framework\DataAbstractionLayer\EntityIdTrait;

/**
 * Class BrainstOdooExtensionEntity
 * @package BrainstOdoo
 * <AUTHOR>
 * @link www.brainstreamtechnolabs.com
 */
class BrainstOdooExtensionEntity extends Entity
{
    use EntityIdTrait;

    /**
     * @var string
     */
    protected string $customerId;

    /**
     * @var int
     */
    protected int $odooCustomerId;

    /**
     * @var ?CustomerEntity
     */
    protected ?CustomerEntity $customer = null;

    public function getCustomerId(): ?string
    {
        return $this->customerId;
    }

    public function setCustomerId(string $customerId): void
    {
        $this->customerId = $customerId;
    }

    public function getOdooCustomerId(): ?int
    {
        return $this->odooCustomerId;
    }

    public function setOdooCustomerId(int $odooCustomerId): void
    {
        $this->odooCustomerId = $odooCustomerId;
    }

    public function getCustomer(): ?CustomerEntity
    {
        return $this->customer;
    }

    public function setCustomer(CustomerEntity $customer): void
    {
        $this->customer = $customer;
    }
}
