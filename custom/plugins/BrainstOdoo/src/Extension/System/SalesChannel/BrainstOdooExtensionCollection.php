<?php declare(strict_types=1);

namespace Brainst\Odoo\Extension\System\SalesChannel;

use Shopware\Core\Framework\DataAbstractionLayer\EntityCollection;

/**
 * Class BrainstOdooExtensionDefinition
 *
 * @method void                             add(BrainstOdooExtensionEntity $entity)
 * @method void                             set(string $key, BrainstOdooExtensionEntity $entity)
 * @method BrainstOdooExtensionEntity[]     getIterator()
 * @method BrainstOdooExtensionEntity[]     getElements()
 * @method BrainstOdooExtensionEntity|null  get(string $key)
 * @method BrainstOdooExtensionEntity|null  first()
 * @method BrainstOdooExtensionEntity|null  last()
 *
 * @package BrainstOdoo
 * <AUTHOR>
 * @link www.brainstreamtechnolabs.com
 */
class BrainstOdooExtensionCollection extends EntityCollection
{
    protected function getExpectedClass(): string
    {
        return BrainstOdooExtensionEntity::class;
    }
}