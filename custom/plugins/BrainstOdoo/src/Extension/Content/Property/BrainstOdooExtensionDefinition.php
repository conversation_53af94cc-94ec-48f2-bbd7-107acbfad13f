<?php declare(strict_types=1);

namespace Brainst\Odoo\Extension\Content\Property;

use Shopware\Core\Content\Property\PropertyGroupDefinition;
use Shopware\Core\Framework\DataAbstractionLayer\EntityDefinition;
use Shopware\Core\Framework\DataAbstractionLayer\Field\FkField;
use Shopware\Core\Framework\DataAbstractionLayer\Field\Flag\PrimaryKey;
use Shopware\Core\Framework\DataAbstractionLayer\Field\Flag\Required;
use Shopware\Core\Framework\DataAbstractionLayer\Field\IntField;
use Shopware\Core\Framework\DataAbstractionLayer\Field\OneToOneAssociationField;
use Shopware\Core\Framework\DataAbstractionLayer\FieldCollection;

/**
 * Class BrainstOdooExtensionDefinition
 * @package BrainstOdoo
 * <AUTHOR>
 * @link www.brainstreamtechnolabs.com
 */
class BrainstOdooExtensionDefinition extends EntityDefinition
{
    public const ENTITY_NAME = 'brainst_odoo_property_group';

    public function getEntityName(): string
    {
        return self::ENTITY_NAME;
    }

    protected function defaultFields(): array
    {
        return [];
    }

    public function getCollectionClass(): string
    {
        return BrainstOdooExtensionCollection::class;
    }

    public function getEntityClass(): string
    {
        return BrainstOdooExtensionEntity::class;
    }

    /**
     * @return FieldCollection
     */
    protected function defineFields(): FieldCollection
    {
        return new FieldCollection([
            (new FkField('property_group_id', 'propertyGroupId', PropertyGroupDefinition::class))->addFlags(new Required(), new PrimaryKey()),
            (new IntField('odoo_property_group_id', 'odooPropertyGroupId'))->addFlags(new Required()),
            new OneToOneAssociationField('propertyGroup', 'property_group_id', 'id', PropertyGroupDefinition::class, false)
        ]);
    }
}