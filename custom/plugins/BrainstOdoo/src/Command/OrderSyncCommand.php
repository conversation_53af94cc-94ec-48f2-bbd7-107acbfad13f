<?php

declare(strict_types=1);

namespace Brainst\Odoo\Command;

use Brainst\Odoo\Service\OdooService;
use Brainst\Odoo\Service\SyncQueue\OrderSyncQueueService;
use PhpXmlRpc\Exception\ValueErrorException;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

/**
 * Class OrderSyncCommand
 * @package BrainstOdoo
 * <AUTHOR>
 * @link www.brainstreamtechnolabs.com
 */
class OrderSyncCommand extends Command
{
    public function __construct(
        private readonly OrderSyncQueueService $syncQueueService,
        private readonly OdooService           $odooService,
        string                                 $name = null
    )
    {
        parent::__construct($name);
    }

    /**
     * Provides a description, printed out in bin/console
     *
     * @return void
     */
    protected function configure(): void
    {
        parent::configure();
        $this->setName('brainst-odoo:order-sync')->setDescription('Sync all orders to odoo client.');
    }

    /**
     * Actual code executed in the command
     *
     * @param InputInterface $input
     * @param OutputInterface $output
     * @return int
     * @throws ValueErrorException
     */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        if (!($this->odooService)()->hasService) {
            $output->writeln('Failed: Please check Odoo API key correct and integration enabled');
            return 0;
        }

        $output->writeln('START: adding orders to the queue');
        ($this->syncQueueService)();
        $output->writeln('FINISHED: orders added to the queue, odoo orders will be up-to-date after the queue is processed');
        return 0;
    }
}
