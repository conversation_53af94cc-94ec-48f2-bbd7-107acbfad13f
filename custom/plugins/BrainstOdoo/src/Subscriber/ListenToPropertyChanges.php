<?php

declare(strict_types=1);

namespace Brainst\Odoo\Subscriber;

use Brainst\Odoo\Model\Operation;
use Brainst\Odoo\Service\ChunkDispatch\PropertyDispatchService;
use Brainst\Odoo\Service\Synchronisation\PropertySynchronisationService;
use Shopware\Core\Content\Property\PropertyEvents;
use Shopware\Core\Content\Property\PropertyGroupDefinition;
use Shopware\Core\Framework\DataAbstractionLayer\EntityWriteResult;
use Shopware\Core\Framework\DataAbstractionLayer\Event\EntityDeletedEvent;
use Shopware\Core\Framework\DataAbstractionLayer\Event\EntityWrittenEvent;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;

/**
 * Class ListenToPropertyChanges
 * @package BrainstOdoo
 * <AUTHOR>
 * @link www.brainstreamtechnolabs.com
 */
class ListenToPropertyChanges implements EventSubscriberInterface
{
    public function __construct(
        private readonly PropertySynchronisationService $propertySynchronisationService,
        private readonly PropertyDispatchService        $propertyDispatchService
    )
    {
    }

    public static function getSubscribedEvents(): array
    {
        return [
            PropertyEvents::PROPERTY_GROUP_DELETED_EVENT => 'onPropertyDelete',
            PropertyEvents::PROPERTY_GROUP_WRITTEN_EVENT => 'onPropertyWritten'
        ];
    }

    /**
     * Add or update Odoo when property attribute or attribute value added or updated
     *
     * @param EntityWrittenEvent $event
     * @return void
     */
    public function onPropertyWritten(EntityWrittenEvent $event): void
    {
        $propertyIds = [];
        foreach ($event->getWriteResults() as $writeResult) {
            if ($writeResult->getOperation() === EntityWriteResult::OPERATION_INSERT || $writeResult->getOperation() === EntityWriteResult::OPERATION_UPDATE) {
                $propertyIds[] = $writeResult->getPrimaryKey();
            }
        }
        if (!empty($propertyIds)) {
            ($this->propertyDispatchService)($propertyIds, Operation::update());
        }
    }

    /**
     * Delete records from Odoo
     *
     * @param EntityDeletedEvent $event
     * @return void
     */
    public function onPropertyDelete(EntityDeletedEvent $event): void
    {
        $propertyIds = [];
        $deletedIds = [];
        foreach ($event->getWriteResults() as $writeResult) {
            if ($writeResult->getEntityName() !== PropertyGroupDefinition::ENTITY_NAME) {
                continue;
            }

            $deleteId = $this->propertySynchronisationService->getOdooRecordId($writeResult->getPrimaryKey(),
                $event->getContext());
            $deletedIds[$writeResult->getPrimaryKey()] = $deleteId;
            $propertyIds[] = $writeResult->getPrimaryKey();
        }
        if (!empty($propertyIds)) {
            ($this->propertyDispatchService)($propertyIds, Operation::delete(), $deletedIds);
        }
    }
}