<?php

declare(strict_types=1);

namespace Brainst\Odoo\Subscriber;

use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\Messenger\Event\WorkerMessageFailedEvent;

/**
 * Class ListenToMessageHandlerFails
 * @package BrainstOdoo
 * <AUTHOR>
 * @link www.brainstreamtechnolabs.com
 */
class ListenToMessageHandlerFails implements EventSubscriberInterface
{
    public function __construct(
        private readonly LoggerInterface $logger
    )
    {
    }

    public static function getSubscribedEvents(): array
    {
        return [
            WorkerMessageFailedEvent::class => 'onMessageFailed'
        ];
    }

    public function onMessageFailed(WorkerMessageFailedEvent $event): void
    {
        $message = $event->getEnvelope()->getMessage();
        if (!$event->willRetry() && isset($message->messageType) && $message->messageType === 'odoo_sync_message') {
            $previousError = $event->getThrowable()->getPrevious();
            $this->logger->error($previousError->getMessage(),
                [
                    "message" => $previousError->getMessage(),
                    "file" => $previousError->getFile(),
                    "line" => $previousError->getLine(),
                    'exception' => $previousError->getTraceAsString()
                ]);
        }
    }


}