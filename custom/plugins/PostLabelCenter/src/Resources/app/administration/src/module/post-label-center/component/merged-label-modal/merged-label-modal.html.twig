{% block plc_merged_label__modal %}
    <sw-modal
            class="plc-merged-label-modal"
            :title="$tc('plc.mergedLabel.modalTitle')"
            variant="large"
            @modal-close="$emit('modal-close')"
    >
        {% block plc_merged_label__modal_body %}
            <mt-loader v-if="isLoading && feature.isActive('v6.6.0.0')"/>
            <sw-loader v-if="isLoading && !feature.isActive('v6.6.0.0')"/>
            <template v-else-if="!isLoading && initialOrderList && initialOrderList.length > 0">
                <h3 class="datagrid-headline">{{ $tc('plc.mergedLabel.label.chosenOrders') }}</h3>

                <sw-data-grid
                        :dataSource="initialOrderList"
                        :columns="orderColumns"
                        :showSelection="false"
                        :compactMode="true"
                        :show-actions="false">

                    <template #column-orderDateTime="{ item }">
                        <span>{{ formatDate(item.orderDateTime) }}</span>
                    </template>
                </sw-data-grid>
            </template>

            <template v-if="generatedOrderLabels && generatedOrderLabels.length > 0">
                <h3 class="datagrid-headline">{{ $tc('plc.mergedLabel.label.generatedLabels') }}</h3>

                <sw-data-grid
                        :dataSource="generatedOrderLabels"
                        :columns="labelColumns"
                        :showSelection="false"
                        :compactMode="true"
                        :show-actions="false">
                </sw-data-grid>
            </template>
        {% endblock %}

        <!-- eslint-disable-next-line sw-deprecation-rules/no-twigjs-blocks -->
        {% block plc_merged_label__modal_footer %}
            <template #modal-footer>
                {% block plc_merged_label__modal_footer_save_button %}
                    <sw-button v-if="initialOrderList.length > 0"
                               variant="primary"
                               size="small"
                               :disabled="isLoading"
                               @click="downloadZip"
                    >
                        {{ $tc('plc.modal.generateAndDownload') }}
                    </sw-button>
                {% endblock %}
            </template>
        {% endblock %}
    </sw-modal>
{% endblock %}