(()=>{var t={857:t=>{"use strict";var e=function(t){var e;return!!t&&"object"==typeof t&&"[object RegExp]"!==(e=Object.prototype.toString.call(t))&&"[object Date]"!==e&&t.$$typeof!==i},i="function"==typeof Symbol&&Symbol.for?Symbol.for("react.element"):60103;function n(t,e){return!1!==e.clone&&e.isMergeableObject(t)?a(Array.isArray(t)?[]:{},t,e):t}function o(t,e,i){return t.concat(e).map(function(t){return n(t,i)})}function s(t){return Object.keys(t).concat(Object.getOwnPropertySymbols?Object.getOwnPropertySymbols(t).filter(function(e){return Object.propertyIsEnumerable.call(t,e)}):[])}function r(t,e){try{return e in t}catch(t){return!1}}function a(t,i,h){(h=h||{}).arrayMerge=h.arrayMerge||o,h.isMergeableObject=h.isMergeableObject||e,h.cloneUnlessOtherwiseSpecified=n;var l,u,c=Array.isArray(i);return c!==Array.isArray(t)?n(i,h):c?h.arrayMerge(t,i,h):(u={},(l=h).isMergeableObject(t)&&s(t).forEach(function(e){u[e]=n(t[e],l)}),s(i).forEach(function(e){(!r(t,e)||Object.hasOwnProperty.call(t,e)&&Object.propertyIsEnumerable.call(t,e))&&(r(t,e)&&l.isMergeableObject(i[e])?u[e]=(function(t,e){if(!e.customMerge)return a;var i=e.customMerge(t);return"function"==typeof i?i:a})(e,l)(t[e],i[e],l):u[e]=n(i[e],l))}),u)}a.all=function(t,e){if(!Array.isArray(t))throw Error("first argument should be an array");return t.reduce(function(t,i){return a(t,i,e)},{})},t.exports=a},820:function(t,e){/* @preserve
 * Leaflet 1.8.0, a JS library for interactive maps. https://leafletjs.com
 * (c) 2010-2022 Vladimir Agafonkin, (c) 2010-2011 CloudMade
 */!function(t){"use strict";function e(t){for(var e,i,n=1,o=arguments.length;n<o;n++)for(e in i=arguments[n])t[e]=i[e];return t}var i=Object.create||function(t){return n.prototype=t,new n};function n(){}function o(t,e){var i=Array.prototype.slice;if(t.bind)return t.bind.apply(t,i.call(arguments,1));var n=i.call(arguments,2);return function(){return t.apply(e,n.length?n.concat(i.call(arguments)):arguments)}}var s=0;function r(t){return"_leaflet_id"in t||(t._leaflet_id=++s),t._leaflet_id}function a(t,e,i){var n,o,s=function(){n=!1,o&&(r.apply(i,o),o=!1)},r=function(){n?o=arguments:(t.apply(i,arguments),setTimeout(s,e),n=!0)};return r}function h(t,e,i){var n=e[1],e=e[0],o=n-e;return t===n&&i?t:((t-e)%o+o)%o+e}function l(){return!1}function u(t,e){return!1===e?t:Math.round(t*(e=Math.pow(10,void 0===e?6:e)))/e}function c(t){return t.trim?t.trim():t.replace(/^\s+|\s+$/g,"")}function d(t){return c(t).split(/\s+/)}function _(t,e){for(var n in Object.prototype.hasOwnProperty.call(t,"options")||(t.options=t.options?i(t.options):{}),e)t.options[n]=e[n];return t.options}function p(t,e,i){var n,o=[];for(n in t)o.push(encodeURIComponent(i?n.toUpperCase():n)+"="+encodeURIComponent(t[n]));return(e&&-1!==e.indexOf("?")?"&":"?")+o.join("&")}var m=/\{ *([\w_ -]+) *\}/g;function f(t,e){return t.replace(m,function(t,i){if(void 0===(i=e[i]))throw Error("No value provided for variable "+t);return i="function"==typeof i?i(e):i})}var g=Array.isArray||function(t){return"[object Array]"===Object.prototype.toString.call(t)};function v(t,e){for(var i=0;i<t.length;i++)if(t[i]===e)return i;return -1}var y="data:image/gif;base64,R0lGODlhAQABAAD/ACwAAAAAAQABAAACADs=";function x(t){return window["webkit"+t]||window["moz"+t]||window["ms"+t]}var b=0;function w(t){var e=+new Date,i=Math.max(0,16-(e-b));return b=e+i,window.setTimeout(t,i)}var P=window.requestAnimationFrame||x("RequestAnimationFrame")||w,T=window.cancelAnimationFrame||x("CancelAnimationFrame")||x("CancelRequestAnimationFrame")||function(t){window.clearTimeout(t)};function M(t,e,i){if(!i||P!==w)return P.call(window,o(t,e));t.call(e)}function z(t){t&&T.call(window,t)}function C(){}C.extend=function(t){function n(){_(this),this.initialize&&this.initialize.apply(this,arguments),this.callInitHooks()}var o,s=n.__super__=this.prototype,r=i(s);for(o in(r.constructor=n).prototype=r,this)Object.prototype.hasOwnProperty.call(this,o)&&"prototype"!==o&&"__super__"!==o&&(n[o]=this[o]);if(t.statics&&e(n,t.statics),t.includes){var a=t.includes;if("undefined"!=typeof L&&L&&L.Mixin){a=g(a)?a:[a];for(var h=0;h<a.length;h++)a[h]===L.Mixin.Events&&console.warn("Deprecated include of L.Mixin.Events: this property will be removed in future releases, please inherit from L.Evented instead.",Error().stack)}e.apply(null,[r].concat(t.includes))}return e(r,t),delete r.statics,delete r.includes,r.options&&(r.options=s.options?i(s.options):{},e(r.options,t.options)),r._initHooks=[],r.callInitHooks=function(){if(!this._initHooksCalled){s.callInitHooks&&s.callInitHooks.call(this),this._initHooksCalled=!0;for(var t=0,e=r._initHooks.length;t<e;t++)r._initHooks[t].call(this)}},n},C.include=function(t){var i=this.prototype.options;return e(this.prototype,t),t.options&&(this.prototype.options=i,this.mergeOptions(t.options)),this},C.mergeOptions=function(t){return e(this.prototype.options,t),this},C.addInitHook=function(t){var e=Array.prototype.slice.call(arguments,1),i="function"==typeof t?t:function(){this[t].apply(this,e)};return this.prototype._initHooks=this.prototype._initHooks||[],this.prototype._initHooks.push(i),this};var S={on:function(t,e,i){if("object"==typeof t)for(var n in t)this._on(n,t[n],e);else for(var o=0,s=(t=d(t)).length;o<s;o++)this._on(t[o],e,i);return this},off:function(t,e,i){if(arguments.length){if("object"==typeof t)for(var n in t)this._off(n,t[n],e);else{t=d(t);for(var o=1==arguments.length,s=0,r=t.length;s<r;s++)o?this._off(t[s]):this._off(t[s],e,i)}}else delete this._events;return this},_on:function(t,e,i){if("function"!=typeof e)console.warn("wrong listener type: "+typeof e);else{this._events=this._events||{};for(var n=this._events[t],t=(n||(this._events[t]=n=[]),{fn:e,ctx:i=i===this?void 0:i}),o=n,s=0,r=o.length;s<r;s++)if(o[s].fn===e&&o[s].ctx===i)return;o.push(t)}},_off:function(t,e,i){var n,o,s;if(this._events&&(n=this._events[t])){if(1==arguments.length){if(this._firingCount)for(o=0,s=n.length;o<s;o++)n[o].fn=l;delete this._events[t]}else if(i===this&&(i=void 0),"function"!=typeof e)console.warn("wrong listener type: "+typeof e);else{for(o=0,s=n.length;o<s;o++){var r=n[o];if(r.ctx===i&&r.fn===e)return this._firingCount&&(r.fn=l,this._events[t]=n=n.slice()),void n.splice(o,1)}console.warn("listener not found")}}},fire:function(t,i,n){if(!this.listens(t,n))return this;var o=e({},i,{type:t,target:this,sourceTarget:i&&i.sourceTarget||this});if(this._events){var s=this._events[t];if(s){this._firingCount=this._firingCount+1||1;for(var r=0,a=s.length;r<a;r++){var h=s[r];h.fn.call(h.ctx||this,o)}this._firingCount--}}return n&&this._propagateEvent(o),this},listens:function(t,e){"string"!=typeof t&&console.warn('"string" type argument expected');var i=this._events&&this._events[t];if(i&&i.length)return!0;if(e){for(var n in this._eventParents)if(this._eventParents[n].listens(t,e))return!0}return!1},once:function(t,e,i){if("object"==typeof t){for(var n in t)this.once(n,t[n],e);return this}var s=o(function(){this.off(t,e,i).off(t,s,i)},this);return this.on(t,e,i).on(t,s,i)},addEventParent:function(t){return this._eventParents=this._eventParents||{},this._eventParents[r(t)]=t,this},removeEventParent:function(t){return this._eventParents&&delete this._eventParents[r(t)],this},_propagateEvent:function(t){for(var i in this._eventParents)this._eventParents[i].fire(t.type,e({layer:t.target,propagatedFrom:t.target},t),!0)}},E=(S.addEventListener=S.on,S.removeEventListener=S.clearAllEventListeners=S.off,S.addOneTimeEventListener=S.once,S.fireEvent=S.fire,S.hasEventListeners=S.listens,C.extend(S));function Z(t,e,i){this.x=i?Math.round(t):t,this.y=i?Math.round(e):e}var k=Math.trunc||function(t){return 0<t?Math.floor(t):Math.ceil(t)};function B(t,e,i){return t instanceof Z?t:g(t)?new Z(t[0],t[1]):null==t?t:"object"==typeof t&&"x"in t&&"y"in t?new Z(t.x,t.y):new Z(t,e,i)}function A(t,e){if(t)for(var i=e?[t,e]:t,n=0,o=i.length;n<o;n++)this.extend(i[n])}function O(t,e){return!t||t instanceof A?t:new A(t,e)}function I(t,e){if(t)for(var i=e?[t,e]:t,n=0,o=i.length;n<o;n++)this.extend(i[n])}function N(t,e){return t instanceof I?t:new I(t,e)}function R(t,e,i){if(isNaN(t)||isNaN(e))throw Error("Invalid LatLng object: ("+t+", "+e+")");this.lat=+t,this.lng=+e,void 0!==i&&(this.alt=+i)}function D(t,e,i){return t instanceof R?t:g(t)&&"object"!=typeof t[0]?3===t.length?new R(t[0],t[1],t[2]):2===t.length?new R(t[0],t[1]):null:null==t?t:"object"==typeof t&&"lat"in t?new R(t.lat,"lng"in t?t.lng:t.lon,t.alt):void 0===e?null:new R(t,e,i)}Z.prototype={clone:function(){return new Z(this.x,this.y)},add:function(t){return this.clone()._add(B(t))},_add:function(t){return this.x+=t.x,this.y+=t.y,this},subtract:function(t){return this.clone()._subtract(B(t))},_subtract:function(t){return this.x-=t.x,this.y-=t.y,this},divideBy:function(t){return this.clone()._divideBy(t)},_divideBy:function(t){return this.x/=t,this.y/=t,this},multiplyBy:function(t){return this.clone()._multiplyBy(t)},_multiplyBy:function(t){return this.x*=t,this.y*=t,this},scaleBy:function(t){return new Z(this.x*t.x,this.y*t.y)},unscaleBy:function(t){return new Z(this.x/t.x,this.y/t.y)},round:function(){return this.clone()._round()},_round:function(){return this.x=Math.round(this.x),this.y=Math.round(this.y),this},floor:function(){return this.clone()._floor()},_floor:function(){return this.x=Math.floor(this.x),this.y=Math.floor(this.y),this},ceil:function(){return this.clone()._ceil()},_ceil:function(){return this.x=Math.ceil(this.x),this.y=Math.ceil(this.y),this},trunc:function(){return this.clone()._trunc()},_trunc:function(){return this.x=k(this.x),this.y=k(this.y),this},distanceTo:function(t){var e=(t=B(t)).x-this.x,t=t.y-this.y;return Math.sqrt(e*e+t*t)},equals:function(t){return(t=B(t)).x===this.x&&t.y===this.y},contains:function(t){return Math.abs((t=B(t)).x)<=Math.abs(this.x)&&Math.abs(t.y)<=Math.abs(this.y)},toString:function(){return"Point("+u(this.x)+", "+u(this.y)+")"}},A.prototype={extend:function(t){return t=B(t),this.min||this.max?(this.min.x=Math.min(t.x,this.min.x),this.max.x=Math.max(t.x,this.max.x),this.min.y=Math.min(t.y,this.min.y),this.max.y=Math.max(t.y,this.max.y)):(this.min=t.clone(),this.max=t.clone()),this},getCenter:function(t){return new Z((this.min.x+this.max.x)/2,(this.min.y+this.max.y)/2,t)},getBottomLeft:function(){return new Z(this.min.x,this.max.y)},getTopRight:function(){return new Z(this.max.x,this.min.y)},getTopLeft:function(){return this.min},getBottomRight:function(){return this.max},getSize:function(){return this.max.subtract(this.min)},contains:function(t){var e,i;return(t=("number"==typeof t[0]||t instanceof Z?B:O)(t))instanceof A?(e=t.min,i=t.max):e=i=t,e.x>=this.min.x&&i.x<=this.max.x&&e.y>=this.min.y&&i.y<=this.max.y},intersects:function(t){t=O(t);var e=this.min,i=this.max,n=t.min,t=t.max,o=t.x>=e.x&&n.x<=i.x,t=t.y>=e.y&&n.y<=i.y;return o&&t},overlaps:function(t){t=O(t);var e=this.min,i=this.max,n=t.min,t=t.max,o=t.x>e.x&&n.x<i.x,t=t.y>e.y&&n.y<i.y;return o&&t},isValid:function(){return!(!this.min||!this.max)}},I.prototype={extend:function(t){var e,i,n=this._southWest,o=this._northEast;if(t instanceof R)i=e=t;else{if(!(t instanceof I))return t?this.extend(D(t)||N(t)):this;if(e=t._southWest,i=t._northEast,!e||!i)return this}return n||o?(n.lat=Math.min(e.lat,n.lat),n.lng=Math.min(e.lng,n.lng),o.lat=Math.max(i.lat,o.lat),o.lng=Math.max(i.lng,o.lng)):(this._southWest=new R(e.lat,e.lng),this._northEast=new R(i.lat,i.lng)),this},pad:function(t){var e=this._southWest,i=this._northEast,n=Math.abs(e.lat-i.lat)*t,t=Math.abs(e.lng-i.lng)*t;return new I(new R(e.lat-n,e.lng-t),new R(i.lat+n,i.lng+t))},getCenter:function(){return new R((this._southWest.lat+this._northEast.lat)/2,(this._southWest.lng+this._northEast.lng)/2)},getSouthWest:function(){return this._southWest},getNorthEast:function(){return this._northEast},getNorthWest:function(){return new R(this.getNorth(),this.getWest())},getSouthEast:function(){return new R(this.getSouth(),this.getEast())},getWest:function(){return this._southWest.lng},getSouth:function(){return this._southWest.lat},getEast:function(){return this._northEast.lng},getNorth:function(){return this._northEast.lat},contains:function(t){t=("number"==typeof t[0]||t instanceof R||"lat"in t?D:N)(t);var e,i,n=this._southWest,o=this._northEast;return t instanceof I?(e=t.getSouthWest(),i=t.getNorthEast()):e=i=t,e.lat>=n.lat&&i.lat<=o.lat&&e.lng>=n.lng&&i.lng<=o.lng},intersects:function(t){t=N(t);var e=this._southWest,i=this._northEast,n=t.getSouthWest(),t=t.getNorthEast(),o=t.lat>=e.lat&&n.lat<=i.lat,t=t.lng>=e.lng&&n.lng<=i.lng;return o&&t},overlaps:function(t){t=N(t);var e=this._southWest,i=this._northEast,n=t.getSouthWest(),t=t.getNorthEast(),o=t.lat>e.lat&&n.lat<i.lat,t=t.lng>e.lng&&n.lng<i.lng;return o&&t},toBBoxString:function(){return[this.getWest(),this.getSouth(),this.getEast(),this.getNorth()].join(",")},equals:function(t,e){return!!t&&(t=N(t),this._southWest.equals(t.getSouthWest(),e)&&this._northEast.equals(t.getNorthEast(),e))},isValid:function(){return!(!this._southWest||!this._northEast)}};var j={latLngToPoint:function(t,e){return t=this.projection.project(t),e=this.scale(e),this.transformation._transform(t,e)},pointToLatLng:function(t,e){return e=this.scale(e),t=this.transformation.untransform(t,e),this.projection.unproject(t)},project:function(t){return this.projection.project(t)},unproject:function(t){return this.projection.unproject(t)},scale:function(t){return 256*Math.pow(2,t)},zoom:function(t){return Math.log(t/256)/Math.LN2},getProjectedBounds:function(t){if(this.infinite)return null;var e=this.projection.bounds,t=this.scale(t);return new A(this.transformation.transform(e.min,t),this.transformation.transform(e.max,t))},infinite:(R.prototype={equals:function(t,e){return!!t&&(t=D(t),Math.max(Math.abs(this.lat-t.lat),Math.abs(this.lng-t.lng))<=(void 0===e?1e-9:e))},toString:function(t){return"LatLng("+u(this.lat,t)+", "+u(this.lng,t)+")"},distanceTo:function(t){return H.distance(this,D(t))},wrap:function(){return H.wrapLatLng(this)},toBounds:function(t){var t=180*t/40075017,e=t/Math.cos(Math.PI/180*this.lat);return N([this.lat-t,this.lng-e],[this.lat+t,this.lng+e])},clone:function(){return new R(this.lat,this.lng,this.alt)}},!1),wrapLatLng:function(t){var e=this.wrapLng?h(t.lng,this.wrapLng,!0):t.lng;return new R(this.wrapLat?h(t.lat,this.wrapLat,!0):t.lat,e,t.alt)},wrapLatLngBounds:function(t){var e=t.getCenter(),i=this.wrapLatLng(e),n=e.lat-i.lat,e=e.lng-i.lng;return 0==n&&0==e?t:(i=t.getSouthWest(),t=t.getNorthEast(),new I(new R(i.lat-n,i.lng-e),new R(t.lat-n,t.lng-e)))}},H=e({},j,{wrapLng:[-180,180],R:6371e3,distance:function(t,e){var i=Math.PI/180,n=t.lat*i,o=e.lat*i,s=Math.sin((e.lat-t.lat)*i/2),e=Math.sin((e.lng-t.lng)*i/2),t=s*s+Math.cos(n)*Math.cos(o)*e*e,i=2*Math.atan2(Math.sqrt(t),Math.sqrt(1-t));return this.R*i}}),F=6378137,F={R:F,MAX_LATITUDE:85.0511287798,project:function(t){var e=Math.PI/180,i=this.MAX_LATITUDE,i=Math.max(Math.min(i,t.lat),-i),i=Math.sin(i*e);return new Z(this.R*t.lng*e,this.R*Math.log((1+i)/(1-i))/2)},unproject:function(t){var e=180/Math.PI;return new R((2*Math.atan(Math.exp(t.y/this.R))-Math.PI/2)*e,t.x*e/this.R)},bounds:new A([-(F*=Math.PI),-F],[F,F])};function W(t,e,i,n){if(g(t))return this._a=t[0],this._b=t[1],this._c=t[2],void(this._d=t[3]);this._a=t,this._b=e,this._c=i,this._d=n}function q(t,e,i,n){return new W(t,e,i,n)}W.prototype={transform:function(t,e){return this._transform(t.clone(),e)},_transform:function(t,e){return t.x=(e=e||1)*(this._a*t.x+this._b),t.y=e*(this._c*t.y+this._d),t},untransform:function(t,e){return new Z((t.x/(e=e||1)-this._b)/this._a,(t.y/e-this._d)/this._c)}};var U=e({},H,{code:"EPSG:3857",projection:F,transformation:q(U=.5/(Math.PI*F.R),.5,-U,.5)}),V=e({},U,{code:"EPSG:900913"});function G(t){return document.createElementNS("http://www.w3.org/2000/svg",t)}function K(t,e){for(var i,n,o,s,r="",a=0,h=t.length;a<h;a++){for(i=0,n=(o=t[a]).length;i<n;i++)r+=(i?"L":"M")+(s=o[i]).x+" "+s.y;r+=e?tC.svg?"z":"x":""}return r||"M0 0"}var Y=document.documentElement.style,J="ActiveXObject"in window,X=J&&!document.addEventListener,$="msLaunchUri"in navigator&&!("documentMode"in document),Q=tz("webkit"),tt=tz("android"),te=tz("android 2")||tz("android 3"),ti=parseInt(/WebKit\/([0-9]+)|$/.exec(navigator.userAgent)[1],10),ti=tt&&tz("Google")&&ti<537&&!("AudioNode"in window),tn=!!window.opera,to=!$&&tz("chrome"),ts=tz("gecko")&&!Q&&!tn&&!J,tr=!to&&tz("safari"),ta=tz("phantom"),th="OTransition"in Y,tl=0===navigator.platform.indexOf("Win"),tu=J&&"transition"in Y,tc="WebKitCSSMatrix"in window&&"m11"in new window.WebKitCSSMatrix&&!te,Y="MozPerspective"in Y,td=!window.L_DISABLE_3D&&(tu||tc||Y)&&!th&&!ta,t_="undefined"!=typeof orientation||tz("mobile"),tp=t_&&Q,tm=t_&&tc,tf=!window.PointerEvent&&window.MSPointerEvent,tg=!(!window.PointerEvent&&!tf),tv="ontouchstart"in window||!!window.TouchEvent,ty=!window.L_NO_TOUCH&&(tv||tg),tx=t_&&tn,tb=t_&&ts,tw=1<(window.devicePixelRatio||window.screen.deviceXDPI/window.screen.logicalXDPI),tP=function(){var t=!1;try{var e=Object.defineProperty({},"passive",{get:function(){t=!0}});window.addEventListener("testPassiveEventSupport",l,e),window.removeEventListener("testPassiveEventSupport",l,e)}catch(t){}return t}(),tL=!!document.createElement("canvas").getContext,tT=!(!document.createElementNS||!G("svg").createSVGRect),tM=!!tT&&((tM=document.createElement("div")).innerHTML="<svg/>","http://www.w3.org/2000/svg"===(tM.firstChild&&tM.firstChild.namespaceURI));function tz(t){return 0<=navigator.userAgent.toLowerCase().indexOf(t)}var tC={ie:J,ielt9:X,edge:$,webkit:Q,android:tt,android23:te,androidStock:ti,opera:tn,chrome:to,gecko:ts,safari:tr,phantom:ta,opera12:th,win:tl,ie3d:tu,webkit3d:tc,gecko3d:Y,any3d:td,mobile:t_,mobileWebkit:tp,mobileWebkit3d:tm,msPointer:tf,pointer:tg,touch:ty,touchNative:tv,mobileOpera:tx,mobileGecko:tb,retina:tw,passiveEvents:tP,canvas:tL,svg:tT,vml:!tT&&function(){try{var t=document.createElement("div"),e=(t.innerHTML='<v:shape adj="1"/>',t.firstChild);return e.style.behavior="url(#default#VML)",e&&"object"==typeof e.adj}catch(t){return!1}}(),inlineSvg:tM},tS=tC.msPointer?"MSPointerDown":"pointerdown",tE=tC.msPointer?"MSPointerMove":"pointermove",tZ=tC.msPointer?"MSPointerUp":"pointerup",tk=tC.msPointer?"MSPointerCancel":"pointercancel",tB={touchstart:tS,touchmove:tE,touchend:tZ,touchcancel:tk},tA={touchstart:function(t,e){e.MSPOINTER_TYPE_TOUCH&&e.pointerType===e.MSPOINTER_TYPE_TOUCH&&ey(e),tj(t,e)},touchmove:tj,touchend:tj,touchcancel:tj},tO={},tI=!1;function tN(t){tO[t.pointerId]=t}function tR(t){tO[t.pointerId]&&(tO[t.pointerId]=t)}function tD(t){delete tO[t.pointerId]}function tj(t,e){if(e.pointerType!==(e.MSPOINTER_TYPE_MOUSE||"mouse")){for(var i in e.touches=[],tO)e.touches.push(tO[i]);e.changedTouches=[e],t(e)}}var tH,tF,tW,tq,tU,tV,tG=t9(["transform","webkitTransform","OTransform","MozTransform","msTransform"]),tK=t9(["webkitTransition","transition","OTransition","MozTransition","msTransition"]),tY="webkitTransition"===tK||"OTransition"===tK?tK+"End":"transitionend";function tJ(t){return"string"==typeof t?document.getElementById(t):t}function tX(t,e){var i=t.style[e]||t.currentStyle&&t.currentStyle[e];return"auto"===(i=i&&"auto"!==i||!document.defaultView?i:(t=document.defaultView.getComputedStyle(t,null))?t[e]:null)?null:i}function t$(t,e,i){return(t=document.createElement(t)).className=e||"",i&&i.appendChild(t),t}function tQ(t){var e=t.parentNode;e&&e.removeChild(t)}function t0(t){for(;t.firstChild;)t.removeChild(t.firstChild)}function t1(t){var e=t.parentNode;e&&e.lastChild!==t&&e.appendChild(t)}function t2(t){var e=t.parentNode;e&&e.firstChild!==t&&e.insertBefore(t,e.firstChild)}function t3(t,e){return void 0!==t.classList?t.classList.contains(e):0<(t=t7(t)).length&&RegExp("(^|\\s)"+e+"(\\s|$)").test(t)}function t8(t,e){var i;if(void 0!==t.classList)for(var n=d(e),o=0,s=n.length;o<s;o++)t.classList.add(n[o]);else t3(t,e)||t4(t,((i=t7(t))?i+" ":"")+e)}function t5(t,e){void 0!==t.classList?t.classList.remove(e):t4(t,c((" "+t7(t)+" ").replace(" "+e+" "," ")))}function t4(t,e){void 0===t.className.baseVal?t.className=e:t.className.baseVal=e}function t7(t){return void 0===(t=t.correspondingElement?t.correspondingElement:t).className.baseVal?t.className:t.className.baseVal}function t6(t,e){if("opacity"in t.style)t.style.opacity=e;else if("filter"in t.style){var i=!1,n="DXImageTransform.Microsoft.Alpha";try{i=t.filters.item(n)}catch(t){if(1===e)return}e=Math.round(100*e),i?(i.Enabled=100!==e,i.Opacity=e):t.style.filter+=" progid:"+n+"(opacity="+e+")"}}function t9(t){for(var e=document.documentElement.style,i=0;i<t.length;i++)if(t[i]in e)return t[i];return!1}function et(t,e,i){e=e||new Z(0,0),t.style[tG]=(tC.ie3d?"translate("+e.x+"px,"+e.y+"px)":"translate3d("+e.x+"px,"+e.y+"px,0)")+(i?" scale("+i+")":"")}function ee(t,e){t._leaflet_pos=e,tC.any3d?et(t,e):(t.style.left=e.x+"px",t.style.top=e.y+"px")}function ei(t){return t._leaflet_pos||new Z(0,0)}function en(){el(window,"dragstart",ey)}function eo(){ec(window,"dragstart",ey)}function es(t){for(;-1===t.tabIndex;)t=t.parentNode;t.style&&(er(),tV=(tU=t).style.outline,t.style.outline="none",el(window,"keydown",er))}function er(){tU&&(tU.style.outline=tV,tV=tU=void 0,ec(window,"keydown",er))}function ea(t){for(;!((t=t.parentNode).offsetWidth&&t.offsetHeight||t===document.body););return t}function eh(t){var e=t.getBoundingClientRect();return{x:e.width/t.offsetWidth||1,y:e.height/t.offsetHeight||1,boundingClientRect:e}}function el(t,e,i,n){if(e&&"object"==typeof e)for(var o in e)ep(t,o,e[o],i);else for(var s=0,r=(e=d(e)).length;s<r;s++)ep(t,e[s],i,n);return this}tq="onselectstart"in document?(tW=function(){el(window,"selectstart",ey)},function(){ec(window,"selectstart",ey)}):(tF=t9(["userSelect","WebkitUserSelect","OUserSelect","MozUserSelect","msUserSelect"]),tW=function(){var t;tF&&(tH=(t=document.documentElement.style)[tF],t[tF]="none")},function(){tF&&(document.documentElement.style[tF]=tH,tH=void 0)}),J={__proto__:null,TRANSFORM:tG,TRANSITION:tK,TRANSITION_END:tY,get:tJ,getStyle:tX,create:t$,remove:tQ,empty:t0,toFront:t1,toBack:t2,hasClass:t3,addClass:t8,removeClass:t5,setClass:t4,getClass:t7,setOpacity:t6,testProp:t9,setTransform:et,setPosition:ee,getPosition:ei,get disableTextSelection(){return tW},get enableTextSelection(){return tq},disableImageDrag:en,enableImageDrag:eo,preventOutline:es,restoreOutline:er,getSizedParentNode:ea,getScale:eh};var eu="_leaflet_events";function ec(t,e,i,n){if(1==arguments.length)ed(t),delete t[eu];else if(e&&"object"==typeof e)for(var o in e)em(t,o,e[o],i);else if(e=d(e),2==arguments.length)ed(t,function(t){return -1!==v(e,t)});else for(var s=0,r=e.length;s<r;s++)em(t,e[s],i,n);return this}function ed(t,e){for(var i in t[eu]){var n=i.split(/\d/)[0];e&&!e(n)||em(t,n,null,null,i)}}var e_={mouseenter:"mouseover",mouseleave:"mouseout",wheel:!("onwheel"in window)&&"mousewheel"};function ep(t,e,i,n){var o,s,a=e+r(i)+(n?"_"+r(n):"");t[eu]&&t[eu][a]||(s=o=function(e){return i.call(n||t,e||window.event)},!tC.touchNative&&tC.pointer&&0===e.indexOf("touch")?o=function(t,e,i){return"touchstart"!==e||tI||(document.addEventListener(tS,tN,!0),document.addEventListener(tE,tR,!0),document.addEventListener(tZ,tD,!0),document.addEventListener(tk,tD,!0),tI=!0),tA[e]?(i=tA[e].bind(this,i),t.addEventListener(tB[e],i,!1),i):(console.warn("wrong event specified:",e),L.Util.falseFn)}(t,e,o):tC.touch&&"dblclick"===e?o=function(t,e){t.addEventListener("dblclick",e);var i,n=0;function o(t){var o;1!==t.detail?i=t.detail:"mouse"===t.pointerType||t.sourceCapabilities&&!t.sourceCapabilities.firesTouchEvents||((o=Date.now())-n<=200?2==++i&&e(function(t){var e,i,n={};for(i in t)e=t[i],n[i]=e&&e.bind?e.bind(t):e;return(t=n).type="dblclick",n.detail=2,n.isTrusted=!1,n._simulated=!0,n}(t)):i=1,n=o)}return t.addEventListener("click",o),{dblclick:e,simDblclick:o}}(t,o):"addEventListener"in t?"touchstart"===e||"touchmove"===e||"wheel"===e||"mousewheel"===e?t.addEventListener(e_[e]||e,o,!!tC.passiveEvents&&{passive:!1}):"mouseenter"===e||"mouseleave"===e?t.addEventListener(e_[e],o=function(e){eL(t,e=e||window.event)&&s(e)},!1):t.addEventListener(e,s,!1):t.attachEvent("on"+e,o),t[eu]=t[eu]||{},t[eu][a]=o)}function em(t,e,i,n,o){o=o||e+r(i)+(n?"_"+r(n):"");var s,i=t[eu]&&t[eu][o];i&&(!tC.touchNative&&tC.pointer&&0===e.indexOf("touch")?(n=t,s=i,tB[e]?n.removeEventListener(tB[e],s,!1):console.warn("wrong event specified:",e)):tC.touch&&"dblclick"===e?(n=i,(s=t).removeEventListener("dblclick",n.dblclick),s.removeEventListener("click",n.simDblclick)):"removeEventListener"in t?t.removeEventListener(e_[e]||e,i,!1):t.detachEvent("on"+e,i),t[eu][o]=null)}function ef(t){return t.stopPropagation?t.stopPropagation():t.originalEvent?t.originalEvent._stopped=!0:t.cancelBubble=!0,this}function eg(t){return ep(t,"wheel",ef),this}function ev(t){return el(t,"mousedown touchstart dblclick contextmenu",ef),t._leaflet_disable_click=!0,this}function ey(t){return t.preventDefault?t.preventDefault():t.returnValue=!1,this}function ex(t){return ey(t),ef(t),this}function eb(t,e){if(!e)return new Z(t.clientX,t.clientY);var i=eh(e),n=i.boundingClientRect;return new Z((t.clientX-n.left)/i.x-e.clientLeft,(t.clientY-n.top)/i.y-e.clientTop)}var ew=tC.win&&tC.chrome?2*window.devicePixelRatio:tC.gecko?window.devicePixelRatio:1;function eP(t){return tC.edge?t.wheelDeltaY/2:t.deltaY&&0===t.deltaMode?-t.deltaY/ew:t.deltaY&&1===t.deltaMode?-(20*t.deltaY):t.deltaY&&2===t.deltaMode?-(60*t.deltaY):t.deltaX||t.deltaZ?0:t.wheelDelta?(t.wheelDeltaY||t.wheelDelta)/2:t.detail&&32765>Math.abs(t.detail)?-(20*t.detail):t.detail?-(t.detail/32765*60):0}function eL(t,e){var i=e.relatedTarget;if(!i)return!0;try{for(;i&&i!==t;)i=i.parentNode}catch(t){return!1}return i!==t}var X={__proto__:null,on:el,off:ec,stopPropagation:ef,disableScrollPropagation:eg,disableClickPropagation:ev,preventDefault:ey,stop:ex,getMousePosition:eb,getWheelDelta:eP,isExternalTarget:eL,addListener:el,removeListener:ec},eT=E.extend({run:function(t,e,i,n){this.stop(),this._el=t,this._inProgress=!0,this._duration=i||.25,this._easeOutPower=1/Math.max(n||.5,.2),this._startPos=ei(t),this._offset=e.subtract(this._startPos),this._startTime=+new Date,this.fire("start"),this._animate()},stop:function(){this._inProgress&&(this._step(!0),this._complete())},_animate:function(){this._animId=M(this._animate,this),this._step()},_step:function(t){var e=+new Date-this._startTime,i=1e3*this._duration;e<i?this._runFrame(this._easeOut(e/i),t):(this._runFrame(1),this._complete())},_runFrame:function(t,e){t=this._startPos.add(this._offset.multiplyBy(t)),e&&t._round(),ee(this._el,t),this.fire("step")},_complete:function(){z(this._animId),this._inProgress=!1,this.fire("end")},_easeOut:function(t){return 1-Math.pow(1-t,this._easeOutPower)}}),eM=E.extend({options:{crs:U,center:void 0,zoom:void 0,minZoom:void 0,maxZoom:void 0,layers:[],maxBounds:void 0,renderer:void 0,zoomAnimation:!0,zoomAnimationThreshold:4,fadeAnimation:!0,markerZoomAnimation:!0,transform3DLimit:8388608,zoomSnap:1,zoomDelta:1,trackResize:!0},initialize:function(t,e){e=_(this,e),this._handlers=[],this._layers={},this._zoomBoundLayers={},this._sizeChanged=!0,this._initContainer(t),this._initLayout(),this._onResize=o(this._onResize,this),this._initEvents(),e.maxBounds&&this.setMaxBounds(e.maxBounds),void 0!==e.zoom&&(this._zoom=this._limitZoom(e.zoom)),e.center&&void 0!==e.zoom&&this.setView(D(e.center),e.zoom,{reset:!0}),this.callInitHooks(),this._zoomAnimated=tK&&tC.any3d&&!tC.mobileOpera&&this.options.zoomAnimation,this._zoomAnimated&&(this._createAnimProxy(),el(this._proxy,tY,this._catchTransitionEnd,this)),this._addLayers(this.options.layers)},setView:function(t,i,n){return(i=void 0===i?this._zoom:this._limitZoom(i),t=this._limitCenter(D(t),i,this.options.maxBounds),n=n||{},this._stop(),this._loaded&&!n.reset&&!0!==n)&&(void 0!==n.animate&&(n.zoom=e({animate:n.animate},n.zoom),n.pan=e({animate:n.animate,duration:n.duration},n.pan)),this._zoom!==i?this._tryAnimatedZoom&&this._tryAnimatedZoom(t,i,n.zoom):this._tryAnimatedPan(t,n.pan))?clearTimeout(this._sizeTimer):this._resetView(t,i),this},setZoom:function(t,e){return this._loaded?this.setView(this.getCenter(),t,{zoom:e}):(this._zoom=t,this)},zoomIn:function(t,e){return t=t||(tC.any3d?this.options.zoomDelta:1),this.setZoom(this._zoom+t,e)},zoomOut:function(t,e){return t=t||(tC.any3d?this.options.zoomDelta:1),this.setZoom(this._zoom-t,e)},setZoomAround:function(t,e,i){var n=this.getZoomScale(e),o=this.getSize().divideBy(2),t=(t instanceof Z?t:this.latLngToContainerPoint(t)).subtract(o).multiplyBy(1-1/n),n=this.containerPointToLatLng(o.add(t));return this.setView(n,e,{zoom:i})},_getBoundsCenterZoom:function(t,e){e=e||{},t=t.getBounds?t.getBounds():N(t);var i=B(e.paddingTopLeft||e.padding||[0,0]),n=B(e.paddingBottomRight||e.padding||[0,0]),o=this.getBoundsZoom(t,!1,i.add(n));return(o="number"==typeof e.maxZoom?Math.min(e.maxZoom,o):o)===1/0?{center:t.getCenter(),zoom:o}:(e=n.subtract(i).divideBy(2),n=this.project(t.getSouthWest(),o),i=this.project(t.getNorthEast(),o),{center:this.unproject(n.add(i).divideBy(2).add(e),o),zoom:o})},fitBounds:function(t,e){if(!(t=N(t)).isValid())throw Error("Bounds are not valid.");return t=this._getBoundsCenterZoom(t,e),this.setView(t.center,t.zoom,e)},fitWorld:function(t){return this.fitBounds([[-90,-180],[90,180]],t)},panTo:function(t,e){return this.setView(t,this._zoom,{pan:e})},panBy:function(t,e){var i;return e=e||{},(t=B(t).round()).x||t.y?(!0===e.animate||this.getSize().contains(t)?(this._panAnim||(this._panAnim=new eT,this._panAnim.on({step:this._onPanTransitionStep,end:this._onPanTransitionEnd},this)),e.noMoveStart||this.fire("movestart"),!1!==e.animate?(t8(this._mapPane,"leaflet-pan-anim"),i=this._getMapPanePos().subtract(t).round(),this._panAnim.run(this._mapPane,i,e.duration||.25,e.easeLinearity)):(this._rawPanBy(t),this.fire("move").fire("moveend"))):this._resetView(this.unproject(this.project(this.getCenter()).add(t)),this.getZoom()),this):this.fire("moveend")},flyTo:function(t,e,i){if(!1===(i=i||{}).animate||!tC.any3d)return this.setView(t,e,i);this._stop();var n=this.project(this.getCenter()),o=this.project(t),s=this.getSize(),r=this._zoom,a=(t=D(t),e=void 0===e?r:e,Math.max(s.x,s.y)),h=a*this.getZoomScale(r,e),l=o.distanceTo(n)||1;function u(t){return(t=Math.sqrt((t=(h*h-a*a+(t?-1:1)*2.0164*2.0164*l*l)/(2*(t?h:a)*2.0164*l))*t+1)-t)<1e-9?-18:Math.log(t)}function c(t){return(Math.exp(t)-Math.exp(-t))/2}function d(t){return(Math.exp(t)+Math.exp(-t))/2}var _=u(0),p=Date.now(),m=(u(1)-_)/1.42,f=i.duration?1e3*i.duration:1e3*m*.8;return this._moveStart(!0,i.noMoveStart),(function i(){var s,h=(Date.now()-p)/f,u=(1-Math.pow(1-h,1.5))*m;h<=1?(this._flyToFrame=M(i,this),this._move(this.unproject(n.add(o.subtract(n).multiplyBy((s=u,a*(d(_)*(c(s=_+1.42*s)/d(s))-c(_))/2.0164/l))),r),this.getScaleZoom(a/(a*(d(_)/d(_+1.42*(h=u)))),r),{flyTo:!0})):this._move(t,e)._moveEnd(!0)}).call(this),this},flyToBounds:function(t,e){return t=this._getBoundsCenterZoom(t,e),this.flyTo(t.center,t.zoom,e)},setMaxBounds:function(t){return(t=N(t)).isValid()?(this.options.maxBounds&&this.off("moveend",this._panInsideMaxBounds),this.options.maxBounds=t,this._loaded&&this._panInsideMaxBounds(),this.on("moveend",this._panInsideMaxBounds)):(this.options.maxBounds=null,this.off("moveend",this._panInsideMaxBounds))},setMinZoom:function(t){var e=this.options.minZoom;return this.options.minZoom=t,this._loaded&&e!==t&&(this.fire("zoomlevelschange"),this.getZoom()<this.options.minZoom)?this.setZoom(t):this},setMaxZoom:function(t){var e=this.options.maxZoom;return this.options.maxZoom=t,this._loaded&&e!==t&&(this.fire("zoomlevelschange"),this.getZoom()>this.options.maxZoom)?this.setZoom(t):this},panInsideBounds:function(t,e){this._enforcingBounds=!0;var i=this.getCenter(),t=this._limitCenter(i,this._zoom,N(t));return i.equals(t)||this.panTo(t,e),this._enforcingBounds=!1,this},panInside:function(t,e){var i=B((e=e||{}).paddingTopLeft||e.padding||[0,0]),n=B(e.paddingBottomRight||e.padding||[0,0]),o=this.project(this.getCenter()),t=this.project(t),s=this.getPixelBounds(),i=O([s.min.add(i),s.max.subtract(n)]),s=i.getSize();return i.contains(t)||(this._enforcingBounds=!0,n=t.subtract(i.getCenter()),i=i.extend(t).getSize().subtract(s),o.x+=n.x<0?-i.x:i.x,o.y+=n.y<0?-i.y:i.y,this.panTo(this.unproject(o),e),this._enforcingBounds=!1),this},invalidateSize:function(t){if(!this._loaded)return this;t=e({animate:!1,pan:!0},!0===t?{animate:!0}:t);var i=this.getSize(),n=(this._sizeChanged=!0,this._lastCenter=null,this.getSize()),s=i.divideBy(2).round(),r=n.divideBy(2).round(),s=s.subtract(r);return s.x||s.y?(t.animate&&t.pan?this.panBy(s):(t.pan&&this._rawPanBy(s),this.fire("move"),t.debounceMoveend?(clearTimeout(this._sizeTimer),this._sizeTimer=setTimeout(o(this.fire,this,"moveend"),200)):this.fire("moveend")),this.fire("resize",{oldSize:i,newSize:n})):this},stop:function(){return this.setZoom(this._limitZoom(this._zoom)),this.options.zoomSnap||this.fire("viewreset"),this._stop()},locate:function(t){if(t=this._locateOptions=e({timeout:1e4,watch:!1},t),!("geolocation"in navigator))return this._handleGeolocationError({code:0,message:"Geolocation not supported."}),this;var i=o(this._handleGeolocationResponse,this),n=o(this._handleGeolocationError,this);return t.watch?this._locationWatchId=navigator.geolocation.watchPosition(i,n,t):navigator.geolocation.getCurrentPosition(i,n,t),this},stopLocate:function(){return navigator.geolocation&&navigator.geolocation.clearWatch&&navigator.geolocation.clearWatch(this._locationWatchId),this._locateOptions&&(this._locateOptions.setView=!1),this},_handleGeolocationError:function(t){var e;this._container._leaflet_id&&(e=t.code,t=t.message||(1===e?"permission denied":2===e?"position unavailable":"timeout"),this._locateOptions.setView&&!this._loaded&&this.fitWorld(),this.fire("locationerror",{code:e,message:"Geolocation error: "+t+"."}))},_handleGeolocationResponse:function(t){if(this._container._leaflet_id){var e,i,n=new R(t.coords.latitude,t.coords.longitude),o=n.toBounds(2*t.coords.accuracy),s=this._locateOptions,r=(s.setView&&(e=this.getBoundsZoom(o),this.setView(n,s.maxZoom?Math.min(e,s.maxZoom):e)),{latlng:n,bounds:o,timestamp:t.timestamp});for(i in t.coords)"number"==typeof t.coords[i]&&(r[i]=t.coords[i]);this.fire("locationfound",r)}},addHandler:function(t,e){return e&&(e=this[t]=new e(this),this._handlers.push(e),this.options[t]&&e.enable()),this},remove:function(){if(this._initEvents(!0),this.options.maxBounds&&this.off("moveend",this._panInsideMaxBounds),this._containerId!==this._container._leaflet_id)throw Error("Map container is being reused by another instance");try{delete this._container._leaflet_id,delete this._containerId}catch(t){this._container._leaflet_id=void 0,this._containerId=void 0}for(var e in void 0!==this._locationWatchId&&this.stopLocate(),this._stop(),tQ(this._mapPane),this._clearControlPos&&this._clearControlPos(),this._resizeRequest&&(z(this._resizeRequest),this._resizeRequest=null),this._clearHandlers(),this._loaded&&this.fire("unload"),this._layers)this._layers[e].remove();for(t in this._panes)tQ(this._panes[t]);return this._layers=[],this._panes=[],delete this._mapPane,delete this._renderer,this},createPane:function(t,e){return e=t$("div","leaflet-pane"+(t?" leaflet-"+t.replace("Pane","")+"-pane":""),e||this._mapPane),t&&(this._panes[t]=e),e},getCenter:function(){return this._checkIfLoaded(),this._lastCenter&&!this._moved()?this._lastCenter:this.layerPointToLatLng(this._getCenterLayerPoint())},getZoom:function(){return this._zoom},getBounds:function(){var t=this.getPixelBounds();return new I(this.unproject(t.getBottomLeft()),this.unproject(t.getTopRight()))},getMinZoom:function(){return void 0===this.options.minZoom?this._layersMinZoom||0:this.options.minZoom},getMaxZoom:function(){return void 0===this.options.maxZoom?void 0===this._layersMaxZoom?1/0:this._layersMaxZoom:this.options.maxZoom},getBoundsZoom:function(t,e,i){t=N(t),i=B(i||[0,0]);var n=this.getZoom()||0,o=this.getMinZoom(),s=this.getMaxZoom(),r=t.getNorthWest(),t=t.getSouthEast(),i=this.getSize().subtract(i),t=O(this.project(t,n),this.project(r,n)).getSize(),r=tC.any3d?this.options.zoomSnap:1,a=i.x/t.x,i=i.y/t.y,t=e?Math.max(a,i):Math.min(a,i),n=this.getScaleZoom(t,n);return r&&(n=Math.round(n/(r/100))*(r/100),n=e?Math.ceil(n/r)*r:Math.floor(n/r)*r),Math.max(o,Math.min(s,n))},getSize:function(){return this._size&&!this._sizeChanged||(this._size=new Z(this._container.clientWidth||0,this._container.clientHeight||0),this._sizeChanged=!1),this._size.clone()},getPixelBounds:function(t,e){return new A(t=this._getTopLeftPoint(t,e),t.add(this.getSize()))},getPixelOrigin:function(){return this._checkIfLoaded(),this._pixelOrigin},getPixelWorldBounds:function(t){return this.options.crs.getProjectedBounds(void 0===t?this.getZoom():t)},getPane:function(t){return"string"==typeof t?this._panes[t]:t},getPanes:function(){return this._panes},getContainer:function(){return this._container},getZoomScale:function(t,e){var i=this.options.crs;return e=void 0===e?this._zoom:e,i.scale(t)/i.scale(e)},getScaleZoom:function(t,e){var i=this.options.crs,t=(e=void 0===e?this._zoom:e,i.zoom(t*i.scale(e)));return isNaN(t)?1/0:t},project:function(t,e){return e=void 0===e?this._zoom:e,this.options.crs.latLngToPoint(D(t),e)},unproject:function(t,e){return e=void 0===e?this._zoom:e,this.options.crs.pointToLatLng(B(t),e)},layerPointToLatLng:function(t){return t=B(t).add(this.getPixelOrigin()),this.unproject(t)},latLngToLayerPoint:function(t){return this.project(D(t))._round()._subtract(this.getPixelOrigin())},wrapLatLng:function(t){return this.options.crs.wrapLatLng(D(t))},wrapLatLngBounds:function(t){return this.options.crs.wrapLatLngBounds(N(t))},distance:function(t,e){return this.options.crs.distance(D(t),D(e))},containerPointToLayerPoint:function(t){return B(t).subtract(this._getMapPanePos())},layerPointToContainerPoint:function(t){return B(t).add(this._getMapPanePos())},containerPointToLatLng:function(t){return t=this.containerPointToLayerPoint(B(t)),this.layerPointToLatLng(t)},latLngToContainerPoint:function(t){return this.layerPointToContainerPoint(this.latLngToLayerPoint(D(t)))},mouseEventToContainerPoint:function(t){return eb(t,this._container)},mouseEventToLayerPoint:function(t){return this.containerPointToLayerPoint(this.mouseEventToContainerPoint(t))},mouseEventToLatLng:function(t){return this.layerPointToLatLng(this.mouseEventToLayerPoint(t))},_initContainer:function(t){if(!(t=this._container=tJ(t)))throw Error("Map container not found.");if(t._leaflet_id)throw Error("Map container is already initialized.");el(t,"scroll",this._onScroll,this),this._containerId=r(t)},_initLayout:function(){var t=this._container,e=(this._fadeAnimated=this.options.fadeAnimation&&tC.any3d,t8(t,"leaflet-container"+(tC.touch?" leaflet-touch":"")+(tC.retina?" leaflet-retina":"")+(tC.ielt9?" leaflet-oldie":"")+(tC.safari?" leaflet-safari":"")+(this._fadeAnimated?" leaflet-fade-anim":"")),tX(t,"position"));"absolute"!==e&&"relative"!==e&&"fixed"!==e&&(t.style.position="relative"),this._initPanes(),this._initControlPos&&this._initControlPos()},_initPanes:function(){var t=this._panes={};this._paneRenderers={},this._mapPane=this.createPane("mapPane",this._container),ee(this._mapPane,new Z(0,0)),this.createPane("tilePane"),this.createPane("overlayPane"),this.createPane("shadowPane"),this.createPane("markerPane"),this.createPane("tooltipPane"),this.createPane("popupPane"),this.options.markerZoomAnimation||(t8(t.markerPane,"leaflet-zoom-hide"),t8(t.shadowPane,"leaflet-zoom-hide"))},_resetView:function(t,e){ee(this._mapPane,new Z(0,0));var i=!this._loaded,n=(this._loaded=!0,e=this._limitZoom(e),this.fire("viewprereset"),this._zoom!==e);this._moveStart(n,!1)._move(t,e)._moveEnd(n),this.fire("viewreset"),i&&this.fire("load")},_moveStart:function(t,e){return t&&this.fire("zoomstart"),e||this.fire("movestart"),this},_move:function(t,e,i,n){void 0===e&&(e=this._zoom);var o=this._zoom!==e;return this._zoom=e,this._lastCenter=t,this._pixelOrigin=this._getNewPixelOrigin(t),n?i&&i.pinch&&this.fire("zoom",i):((o||i&&i.pinch)&&this.fire("zoom",i),this.fire("move",i)),this},_moveEnd:function(t){return t&&this.fire("zoomend"),this.fire("moveend")},_stop:function(){return z(this._flyToFrame),this._panAnim&&this._panAnim.stop(),this},_rawPanBy:function(t){ee(this._mapPane,this._getMapPanePos().subtract(t))},_getZoomSpan:function(){return this.getMaxZoom()-this.getMinZoom()},_panInsideMaxBounds:function(){this._enforcingBounds||this.panInsideBounds(this.options.maxBounds)},_checkIfLoaded:function(){if(!this._loaded)throw Error("Set map center and zoom first.")},_initEvents:function(t){this._targets={};var e=t?ec:el;e((this._targets[r(this._container)]=this)._container,"click dblclick mousedown mouseup mouseover mouseout mousemove contextmenu keypress keydown keyup",this._handleDOMEvent,this),this.options.trackResize&&e(window,"resize",this._onResize,this),tC.any3d&&this.options.transform3DLimit&&(t?this.off:this.on).call(this,"moveend",this._onMoveEnd)},_onResize:function(){z(this._resizeRequest),this._resizeRequest=M(function(){this.invalidateSize({debounceMoveend:!0})},this)},_onScroll:function(){this._container.scrollTop=0,this._container.scrollLeft=0},_onMoveEnd:function(){var t=this._getMapPanePos();Math.max(Math.abs(t.x),Math.abs(t.y))>=this.options.transform3DLimit&&this._resetView(this.getCenter(),this.getZoom())},_findEventTargets:function(t,e){for(var i,n=[],o="mouseout"===e||"mouseover"===e,s=t.target||t.srcElement,a=!1;s;){if((i=this._targets[r(s)])&&("click"===e||"preclick"===e)&&this._draggableMoved(i)){a=!0;break}if(i&&i.listens(e,!0)&&(o&&!eL(s,t)||(n.push(i),o))||s===this._container)break;s=s.parentNode}return n.length||a||o||!this.listens(e,!0)?n:[this]},_isClickDisabled:function(t){for(;t!==this._container;){if(t._leaflet_disable_click)return!0;t=t.parentNode}},_handleDOMEvent:function(t){var e,i=t.target||t.srcElement;!this._loaded||i._leaflet_disable_events||"click"===t.type&&this._isClickDisabled(i)||("mousedown"===(e=t.type)&&es(i),this._fireDOMEvent(t,e))},_mouseEvents:["click","dblclick","mouseover","mouseout","contextmenu"],_fireDOMEvent:function(t,i,n){"click"===t.type&&((h=e({},t)).type="preclick",this._fireDOMEvent(h,h.type,n));var o=this._findEventTargets(t,i);if(n){for(var s=[],r=0;r<n.length;r++)n[r].listens(i,!0)&&s.push(n[r]);o=s.concat(o)}if(o.length){"contextmenu"===i&&ey(t);var a,h=o[0],l={originalEvent:t};for("keypress"!==t.type&&"keydown"!==t.type&&"keyup"!==t.type&&(a=h.getLatLng&&(!h._radius||h._radius<=10),l.containerPoint=a?this.latLngToContainerPoint(h.getLatLng()):this.mouseEventToContainerPoint(t),l.layerPoint=this.containerPointToLayerPoint(l.containerPoint),l.latlng=a?h.getLatLng():this.layerPointToLatLng(l.layerPoint)),r=0;r<o.length;r++)if(o[r].fire(i,l,!0),l.originalEvent._stopped||!1===o[r].options.bubblingMouseEvents&&-1!==v(this._mouseEvents,i))return}},_draggableMoved:function(t){return(t=t.dragging&&t.dragging.enabled()?t:this).dragging&&t.dragging.moved()||this.boxZoom&&this.boxZoom.moved()},_clearHandlers:function(){for(var t=0,e=this._handlers.length;t<e;t++)this._handlers[t].disable()},whenReady:function(t,e){return this._loaded?t.call(e||this,{target:this}):this.on("load",t,e),this},_getMapPanePos:function(){return ei(this._mapPane)||new Z(0,0)},_moved:function(){var t=this._getMapPanePos();return t&&!t.equals([0,0])},_getTopLeftPoint:function(t,e){return(t&&void 0!==e?this._getNewPixelOrigin(t,e):this.getPixelOrigin()).subtract(this._getMapPanePos())},_getNewPixelOrigin:function(t,e){var i=this.getSize()._divideBy(2);return this.project(t,e)._subtract(i)._add(this._getMapPanePos())._round()},_latLngToNewLayerPoint:function(t,e,i){return i=this._getNewPixelOrigin(i,e),this.project(t,e)._subtract(i)},_latLngBoundsToNewLayerBounds:function(t,e,i){return i=this._getNewPixelOrigin(i,e),O([this.project(t.getSouthWest(),e)._subtract(i),this.project(t.getNorthWest(),e)._subtract(i),this.project(t.getSouthEast(),e)._subtract(i),this.project(t.getNorthEast(),e)._subtract(i)])},_getCenterLayerPoint:function(){return this.containerPointToLayerPoint(this.getSize()._divideBy(2))},_getCenterOffset:function(t){return this.latLngToLayerPoint(t).subtract(this._getCenterLayerPoint())},_limitCenter:function(t,e,i){if(!i)return t;var n=this.project(t,e),o=this.getSize().divideBy(2),o=new A(n.subtract(o),n.add(o)),o=this._getBoundsOffset(o,i,e);return o.round().equals([0,0])?t:this.unproject(n.add(o),e)},_limitOffset:function(t,e){if(!e)return t;var i=this.getPixelBounds(),i=new A(i.min.add(t),i.max.add(t));return t.add(this._getBoundsOffset(i,e))},_getBoundsOffset:function(t,e,i){return i=(e=O(this.project(e.getNorthEast(),i),this.project(e.getSouthWest(),i))).min.subtract(t.min),e=e.max.subtract(t.max),new Z(this._rebound(i.x,-e.x),this._rebound(i.y,-e.y))},_rebound:function(t,e){return 0<t+e?Math.round(t-e)/2:Math.max(0,Math.ceil(t))-Math.max(0,Math.floor(e))},_limitZoom:function(t){var e=this.getMinZoom(),i=this.getMaxZoom(),n=tC.any3d?this.options.zoomSnap:1;return n&&(t=Math.round(t/n)*n),Math.max(e,Math.min(i,t))},_onPanTransitionStep:function(){this.fire("move")},_onPanTransitionEnd:function(){t5(this._mapPane,"leaflet-pan-anim"),this.fire("moveend")},_tryAnimatedPan:function(t,e){return!(!0!==(e&&e.animate)&&!this.getSize().contains(t=this._getCenterOffset(t)._trunc()))&&(this.panBy(t,e),!0)},_createAnimProxy:function(){var t=this._proxy=t$("div","leaflet-proxy leaflet-zoom-animated");this._panes.mapPane.appendChild(t),this.on("zoomanim",function(t){var e=this._proxy.style[tG];et(this._proxy,this.project(t.center,t.zoom),this.getZoomScale(t.zoom,1)),e===this._proxy.style[tG]&&this._animatingZoom&&this._onZoomTransitionEnd()},this),this.on("load moveend",this._animMoveEnd,this),this._on("unload",this._destroyAnimProxy,this)},_destroyAnimProxy:function(){tQ(this._proxy),this.off("load moveend",this._animMoveEnd,this),delete this._proxy},_animMoveEnd:function(){var t=this.getCenter(),e=this.getZoom();et(this._proxy,this.project(t,e),this.getZoomScale(e,1))},_catchTransitionEnd:function(t){this._animatingZoom&&0<=t.propertyName.indexOf("transform")&&this._onZoomTransitionEnd()},_nothingToAnimate:function(){return!this._container.getElementsByClassName("leaflet-zoom-animated").length},_tryAnimatedZoom:function(t,e,i){if(this._animatingZoom)return!0;if(i=i||{},!this._zoomAnimated||!1===i.animate||this._nothingToAnimate()||Math.abs(e-this._zoom)>this.options.zoomAnimationThreshold)return!1;var n=this.getZoomScale(e),n=this._getCenterOffset(t)._divideBy(1-1/n);return!(!0!==i.animate&&!this.getSize().contains(n))&&(M(function(){this._moveStart(!0,!1)._animateZoom(t,e,!0)},this),!0)},_animateZoom:function(t,e,i,n){this._mapPane&&(i&&(this._animatingZoom=!0,this._animateToCenter=t,this._animateToZoom=e,t8(this._mapPane,"leaflet-zoom-anim")),this.fire("zoomanim",{center:t,zoom:e,noUpdate:n}),this._tempFireZoomEvent||(this._tempFireZoomEvent=this._zoom!==this._animateToZoom),this._move(this._animateToCenter,this._animateToZoom,void 0,!0),setTimeout(o(this._onZoomTransitionEnd,this),250))},_onZoomTransitionEnd:function(){this._animatingZoom&&(this._mapPane&&t5(this._mapPane,"leaflet-zoom-anim"),this._animatingZoom=!1,this._move(this._animateToCenter,this._animateToZoom,void 0,!0),this._tempFireZoomEvent&&this.fire("zoom"),delete this._tempFireZoomEvent,this.fire("move"),this._moveEnd(!0))}});function ez(t){return new eS(t)}var eC,eS=C.extend({options:{position:"topright"},initialize:function(t){_(this,t)},getPosition:function(){return this.options.position},setPosition:function(t){var e=this._map;return e&&e.removeControl(this),this.options.position=t,e&&e.addControl(this),this},getContainer:function(){return this._container},addTo:function(t){this.remove(),this._map=t;var e=this._container=this.onAdd(t),i=this.getPosition(),t=t._controlCorners[i];return t8(e,"leaflet-control"),-1!==i.indexOf("bottom")?t.insertBefore(e,t.firstChild):t.appendChild(e),this._map.on("unload",this.remove,this),this},remove:function(){return this._map&&(tQ(this._container),this.onRemove&&this.onRemove(this._map),this._map.off("unload",this.remove,this),this._map=null),this},_refocusOnMap:function(t){this._map&&t&&0<t.screenX&&0<t.screenY&&this._map.getContainer().focus()}}),eE=(eM.include({addControl:function(t){return t.addTo(this),this},removeControl:function(t){return t.remove(),this},_initControlPos:function(){var t=this._controlCorners={},e="leaflet-",i=this._controlContainer=t$("div",e+"control-container",this._container);function n(n,o){t[n+o]=t$("div",e+n+" "+e+o,i)}n("top","left"),n("top","right"),n("bottom","left"),n("bottom","right")},_clearControlPos:function(){for(var t in this._controlCorners)tQ(this._controlCorners[t]);tQ(this._controlContainer),delete this._controlCorners,delete this._controlContainer}}),eS.extend({options:{collapsed:!0,position:"topright",autoZIndex:!0,hideSingleBase:!1,sortLayers:!1,sortFunction:function(t,e,i,n){return i<n?-1:n<i?1:0}},initialize:function(t,e,i){for(var n in _(this,i),this._layerControlInputs=[],this._layers=[],this._lastZIndex=0,this._handlingClick=!1,t)this._addLayer(t[n],n);for(n in e)this._addLayer(e[n],n,!0)},onAdd:function(t){this._initLayout(),this._update(),(this._map=t).on("zoomend",this._checkDisabledLayers,this);for(var e=0;e<this._layers.length;e++)this._layers[e].layer.on("add remove",this._onLayerChange,this);return this._container},addTo:function(t){return eS.prototype.addTo.call(this,t),this._expandIfNotCollapsed()},onRemove:function(){this._map.off("zoomend",this._checkDisabledLayers,this);for(var t=0;t<this._layers.length;t++)this._layers[t].layer.off("add remove",this._onLayerChange,this)},addBaseLayer:function(t,e){return this._addLayer(t,e),this._map?this._update():this},addOverlay:function(t,e){return this._addLayer(t,e,!0),this._map?this._update():this},removeLayer:function(t){return t.off("add remove",this._onLayerChange,this),(t=this._getLayer(r(t)))&&this._layers.splice(this._layers.indexOf(t),1),this._map?this._update():this},expand:function(){t8(this._container,"leaflet-control-layers-expanded"),this._section.style.height=null;var t=this._map.getSize().y-(this._container.offsetTop+50);return t<this._section.clientHeight?(t8(this._section,"leaflet-control-layers-scrollbar"),this._section.style.height=t+"px"):t5(this._section,"leaflet-control-layers-scrollbar"),this._checkDisabledLayers(),this},collapse:function(){return t5(this._container,"leaflet-control-layers-expanded"),this},_initLayout:function(){var t="leaflet-control-layers",e=this._container=t$("div",t),i=this.options.collapsed,n=(e.setAttribute("aria-haspopup",!0),ev(e),eg(e),this._section=t$("section",t+"-list")),o=(i&&(this._map.on("click",this.collapse,this),el(e,{mouseenter:function(){el(n,"click",ey),this.expand(),setTimeout(function(){ec(n,"click",ey)})},mouseleave:this.collapse},this)),this._layersLink=t$("a",t+"-toggle",e));o.href="#",o.title="Layers",o.setAttribute("role","button"),el(o,"click",ey),el(o,"focus",this.expand,this),i||this.expand(),this._baseLayersList=t$("div",t+"-base",n),this._separator=t$("div",t+"-separator",n),this._overlaysList=t$("div",t+"-overlays",n),e.appendChild(n)},_getLayer:function(t){for(var e=0;e<this._layers.length;e++)if(this._layers[e]&&r(this._layers[e].layer)===t)return this._layers[e]},_addLayer:function(t,e,i){this._map&&t.on("add remove",this._onLayerChange,this),this._layers.push({layer:t,name:e,overlay:i}),this.options.sortLayers&&this._layers.sort(o(function(t,e){return this.options.sortFunction(t.layer,e.layer,t.name,e.name)},this)),this.options.autoZIndex&&t.setZIndex&&(this._lastZIndex++,t.setZIndex(this._lastZIndex)),this._expandIfNotCollapsed()},_update:function(){if(!this._container)return this;t0(this._baseLayersList),t0(this._overlaysList),this._layerControlInputs=[];for(var t,e,i,n=0,o=0;o<this._layers.length;o++)i=this._layers[o],this._addItem(i),e=e||i.overlay,t=t||!i.overlay,n+=i.overlay?0:1;return this.options.hideSingleBase&&(this._baseLayersList.style.display=(t=t&&1<n)?"":"none"),this._separator.style.display=e&&t?"":"none",this},_onLayerChange:function(t){this._handlingClick||this._update();var e=this._getLayer(r(t.target)),t=e.overlay?"add"===t.type?"overlayadd":"overlayremove":"add"===t.type?"baselayerchange":null;t&&this._map.fire(t,e)},_createRadioElement:function(t,e){return t='<input type="radio" class="leaflet-control-layers-selector" name="'+t+'"'+(e?' checked="checked"':"")+"/>",(e=document.createElement("div")).innerHTML=t,e.firstChild},_addItem:function(t){var e,i=document.createElement("label"),n=this._map.hasLayer(t.layer),n=(t.overlay?((e=document.createElement("input")).type="checkbox",e.className="leaflet-control-layers-selector",e.defaultChecked=n):e=this._createRadioElement("leaflet-base-layers_"+r(this),n),this._layerControlInputs.push(e),e.layerId=r(t.layer),el(e,"click",this._onInputClick,this),document.createElement("span")),o=(n.innerHTML=" "+t.name,document.createElement("span"));return i.appendChild(o),o.appendChild(e),o.appendChild(n),(t.overlay?this._overlaysList:this._baseLayersList).appendChild(i),this._checkDisabledLayers(),i},_onInputClick:function(){var t,e,i=this._layerControlInputs,n=[],o=[];this._handlingClick=!0;for(var s=i.length-1;0<=s;s--)t=i[s],e=this._getLayer(t.layerId).layer,t.checked?n.push(e):t.checked||o.push(e);for(s=0;s<o.length;s++)this._map.hasLayer(o[s])&&this._map.removeLayer(o[s]);for(s=0;s<n.length;s++)this._map.hasLayer(n[s])||this._map.addLayer(n[s]);this._handlingClick=!1,this._refocusOnMap()},_checkDisabledLayers:function(){for(var t,e,i=this._layerControlInputs,n=this._map.getZoom(),o=i.length-1;0<=o;o--)t=i[o],e=this._getLayer(t.layerId).layer,t.disabled=void 0!==e.options.minZoom&&n<e.options.minZoom||void 0!==e.options.maxZoom&&n>e.options.maxZoom},_expandIfNotCollapsed:function(){return this._map&&!this.options.collapsed&&this.expand(),this}})),eZ=eS.extend({options:{position:"topleft",zoomInText:'<span aria-hidden="true">+</span>',zoomInTitle:"Zoom in",zoomOutText:'<span aria-hidden="true">&#x2212;</span>',zoomOutTitle:"Zoom out"},onAdd:function(t){var e="leaflet-control-zoom",i=t$("div",e+" leaflet-bar"),n=this.options;return this._zoomInButton=this._createButton(n.zoomInText,n.zoomInTitle,e+"-in",i,this._zoomIn),this._zoomOutButton=this._createButton(n.zoomOutText,n.zoomOutTitle,e+"-out",i,this._zoomOut),this._updateDisabled(),t.on("zoomend zoomlevelschange",this._updateDisabled,this),i},onRemove:function(t){t.off("zoomend zoomlevelschange",this._updateDisabled,this)},disable:function(){return this._disabled=!0,this._updateDisabled(),this},enable:function(){return this._disabled=!1,this._updateDisabled(),this},_zoomIn:function(t){!this._disabled&&this._map._zoom<this._map.getMaxZoom()&&this._map.zoomIn(this._map.options.zoomDelta*(t.shiftKey?3:1))},_zoomOut:function(t){!this._disabled&&this._map._zoom>this._map.getMinZoom()&&this._map.zoomOut(this._map.options.zoomDelta*(t.shiftKey?3:1))},_createButton:function(t,e,i,n,o){return(i=t$("a",i,n)).innerHTML=t,i.href="#",i.title=e,i.setAttribute("role","button"),i.setAttribute("aria-label",e),ev(i),el(i,"click",ex),el(i,"click",o,this),el(i,"click",this._refocusOnMap,this),i},_updateDisabled:function(){var t=this._map,e="leaflet-disabled";t5(this._zoomInButton,e),t5(this._zoomOutButton,e),this._zoomInButton.setAttribute("aria-disabled","false"),this._zoomOutButton.setAttribute("aria-disabled","false"),(this._disabled||t._zoom===t.getMinZoom())&&(t8(this._zoomOutButton,e),this._zoomOutButton.setAttribute("aria-disabled","true")),(this._disabled||t._zoom===t.getMaxZoom())&&(t8(this._zoomInButton,e),this._zoomInButton.setAttribute("aria-disabled","true"))}}),ek=(eM.mergeOptions({zoomControl:!0}),eM.addInitHook(function(){this.options.zoomControl&&(this.zoomControl=new eZ,this.addControl(this.zoomControl))}),eS.extend({options:{position:"bottomleft",maxWidth:100,metric:!0,imperial:!0},onAdd:function(t){var e="leaflet-control-scale",i=t$("div",e),n=this.options;return this._addScales(n,e+"-line",i),t.on(n.updateWhenIdle?"moveend":"move",this._update,this),t.whenReady(this._update,this),i},onRemove:function(t){t.off(this.options.updateWhenIdle?"moveend":"move",this._update,this)},_addScales:function(t,e,i){t.metric&&(this._mScale=t$("div",e,i)),t.imperial&&(this._iScale=t$("div",e,i))},_update:function(){var t=this._map,e=t.getSize().y/2,t=t.distance(t.containerPointToLatLng([0,e]),t.containerPointToLatLng([this.options.maxWidth,e]));this._updateScales(t)},_updateScales:function(t){this.options.metric&&t&&this._updateMetric(t),this.options.imperial&&t&&this._updateImperial(t)},_updateMetric:function(t){var e=this._getRoundNum(t);this._updateScale(this._mScale,e<1e3?e+" m":e/1e3+" km",e/t)},_updateImperial:function(t){var e,i,t=3.2808399*t;5280<t?(i=this._getRoundNum(e=t/5280),this._updateScale(this._iScale,i+" mi",i/e)):(i=this._getRoundNum(t),this._updateScale(this._iScale,i+" ft",i/t))},_updateScale:function(t,e,i){t.style.width=Math.round(this.options.maxWidth*i)+"px",t.innerHTML=e},_getRoundNum:function(t){var e=Math.pow(10,(Math.floor(t)+"").length-1),t=t/e;return e*(t=10<=t?10:5<=t?5:3<=t?3:2<=t?2:1)}})),eB=eS.extend({options:{position:"bottomright",prefix:'<a href="https://leafletjs.com" title="A JavaScript library for interactive maps">'+(tC.inlineSvg?'<svg aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="12" height="8"><path fill="#4C7BE1" d="M0 0h12v4H0z"/><path fill="#FFD500" d="M0 4h12v3H0z"/><path fill="#E0BC00" d="M0 7h12v1H0z"/></svg> ':"")+"Leaflet</a>"},initialize:function(t){_(this,t),this._attributions={}},onAdd:function(t){for(var e in(t.attributionControl=this)._container=t$("div","leaflet-control-attribution"),ev(this._container),t._layers)t._layers[e].getAttribution&&this.addAttribution(t._layers[e].getAttribution());return this._update(),t.on("layeradd",this._addAttribution,this),this._container},onRemove:function(t){t.off("layeradd",this._addAttribution,this)},_addAttribution:function(t){t.layer.getAttribution&&(this.addAttribution(t.layer.getAttribution()),t.layer.once("remove",function(){this.removeAttribution(t.layer.getAttribution())},this))},setPrefix:function(t){return this.options.prefix=t,this._update(),this},addAttribution:function(t){return t&&(this._attributions[t]||(this._attributions[t]=0),this._attributions[t]++,this._update()),this},removeAttribution:function(t){return t&&this._attributions[t]&&(this._attributions[t]--,this._update()),this},_update:function(){if(this._map){var t,e=[];for(t in this._attributions)this._attributions[t]&&e.push(t);var i=[];this.options.prefix&&i.push(this.options.prefix),e.length&&i.push(e.join(", ")),this._container.innerHTML=i.join(' <span aria-hidden="true">|</span> ')}}}),$=(eM.mergeOptions({attributionControl:!0}),eM.addInitHook(function(){this.options.attributionControl&&(new eB).addTo(this)}),eS.Layers=eE,eS.Zoom=eZ,eS.Scale=ek,eS.Attribution=eB,ez.layers=function(t,e,i){return new eE(t,e,i)},ez.zoom=function(t){return new eZ(t)},ez.scale=function(t){return new ek(t)},ez.attribution=function(t){return new eB(t)},C.extend({initialize:function(t){this._map=t},enable:function(){return this._enabled||(this._enabled=!0,this.addHooks()),this},disable:function(){return this._enabled&&(this._enabled=!1,this.removeHooks()),this},enabled:function(){return!!this._enabled}})),Q=($.addTo=function(t,e){return t.addHandler(e,this),this},{Events:S}),eA=tC.touch?"touchstart mousedown":"mousedown",eO=E.extend({options:{clickTolerance:3},initialize:function(t,e,i,n){_(this,n),this._element=t,this._dragStartTarget=e||t,this._preventOutline=i},enable:function(){this._enabled||(el(this._dragStartTarget,eA,this._onDown,this),this._enabled=!0)},disable:function(){this._enabled&&(eO._dragging===this&&this.finishDrag(!0),ec(this._dragStartTarget,eA,this._onDown,this),this._enabled=!1,this._moved=!1)},_onDown:function(t){var e,i;this._enabled&&(this._moved=!1,t3(this._element,"leaflet-zoom-anim")||(t.touches&&1!==t.touches.length?eO._dragging===this&&this.finishDrag():eO._dragging||t.shiftKey||1!==t.which&&1!==t.button&&!t.touches||((eO._dragging=this)._preventOutline&&es(this._element),en(),tW(),this._moving||(this.fire("down"),i=t.touches?t.touches[0]:t,e=ea(this._element),this._startPoint=new Z(i.clientX,i.clientY),this._startPos=ei(this._element),this._parentScale=eh(e),i="mousedown"===t.type,el(document,i?"mousemove":"touchmove",this._onMove,this),el(document,i?"mouseup":"touchend touchcancel",this._onUp,this)))))},_onMove:function(t){var e;this._enabled&&(t.touches&&1<t.touches.length?this._moved=!0:!(e=new Z((e=t.touches&&1===t.touches.length?t.touches[0]:t).clientX,e.clientY)._subtract(this._startPoint)).x&&!e.y||Math.abs(e.x)+Math.abs(e.y)<this.options.clickTolerance||(e.x/=this._parentScale.x,e.y/=this._parentScale.y,ey(t),this._moved||(this.fire("dragstart"),this._moved=!0,t8(document.body,"leaflet-dragging"),this._lastTarget=t.target||t.srcElement,window.SVGElementInstance&&this._lastTarget instanceof window.SVGElementInstance&&(this._lastTarget=this._lastTarget.correspondingUseElement),t8(this._lastTarget,"leaflet-drag-target")),this._newPos=this._startPos.add(e),this._moving=!0,this._lastEvent=t,this._updatePosition()))},_updatePosition:function(){var t={originalEvent:this._lastEvent};this.fire("predrag",t),ee(this._element,this._newPos),this.fire("drag",t)},_onUp:function(){this._enabled&&this.finishDrag()},finishDrag:function(t){t5(document.body,"leaflet-dragging"),this._lastTarget&&(t5(this._lastTarget,"leaflet-drag-target"),this._lastTarget=null),ec(document,"mousemove touchmove",this._onMove,this),ec(document,"mouseup touchend touchcancel",this._onUp,this),eo(),tq(),this._moved&&this._moving&&this.fire("dragend",{noInertia:t,distance:this._newPos.distanceTo(this._startPos)}),this._moving=!1,eO._dragging=!1}});function eI(t,e){return e&&t.length?(e*=e,t=function(t,e){var i=t.length,n=new("undefined"!=typeof Uint8Array?Uint8Array:Array)(i);n[0]=n[i-1]=1,function t(e,i,n,o,s){var r,a,h,l=0;for(a=o+1;a<=s-1;a++)l<(h=eH(e[a],e[o],e[s],!0))&&(r=a,l=h);n<l&&(i[r]=1,t(e,i,n,o,r),t(e,i,n,r,s))}(t,n,e,0,i-1);var o,s=[];for(o=0;o<i;o++)n[o]&&s.push(t[o]);return s}(t=function(t,e){for(var i=[t[0]],n=1,o=0,s=t.length;n<s;n++)(function(t,e){var i=e.x-t.x,e=e.y-t.y;return i*i+e*e})(t[n],t[o])>e&&(i.push(t[n]),o=n);return o<s-1&&i.push(t[s-1]),i}(t,e),e)):t.slice()}function eN(t,e,i){return Math.sqrt(eH(t,e,i,!0))}function eR(t,e,i,n,o){var s,r,a,h=n?eC:ej(t,i),l=ej(e,i);for(eC=l;;){if(!(h|l))return[t,e];if(h&l)return!1;a=ej(r=eD(t,e,s=h||l,i,o),i),s===h?(t=r,h=a):(e=r,l=a)}}function eD(t,e,i,n,o){var s,r,a=e.x-t.x,e=e.y-t.y,h=n.min,n=n.max;return 8&i?(s=t.x+a*(n.y-t.y)/e,r=n.y):4&i?(s=t.x+a*(h.y-t.y)/e,r=h.y):2&i?(s=n.x,r=t.y+e*(n.x-t.x)/a):1&i&&(s=h.x,r=t.y+e*(h.x-t.x)/a),new Z(s,r,o)}function ej(t,e){var i=0;return t.x<e.min.x?i|=1:t.x>e.max.x&&(i|=2),t.y<e.min.y?i|=4:t.y>e.max.y&&(i|=8),i}function eH(t,e,i,n){var o=e.x,e=e.y,s=i.x-o,r=i.y-e,a=s*s+r*r;return 0<a&&(1<(a=((t.x-o)*s+(t.y-e)*r)/a)?(o=i.x,e=i.y):0<a&&(o+=s*a,e+=r*a)),s=t.x-o,r=t.y-e,n?s*s+r*r:new Z(o,e)}function eF(t){return!g(t[0])||"object"!=typeof t[0][0]&&void 0!==t[0][0]}function eW(t){return console.warn("Deprecated use of _flat, please use L.LineUtil.isFlat instead."),eF(t)}function eq(t,e,i){for(var n,o,s,r,a,h,l,u=[1,4,2,8],c=0,d=t.length;c<d;c++)t[c]._code=ej(t[c],e);for(s=0;s<4;s++){for(h=u[s],n=[],c=0,o=(d=t.length)-1;c<d;o=c++)r=t[c],a=t[o],r._code&h?a._code&h||((l=eD(a,r,h,e,i))._code=ej(l,e),n.push(l)):(a._code&h&&((l=eD(a,r,h,e,i))._code=ej(l,e),n.push(l)),n.push(r));t=n}return t}tt={__proto__:null,simplify:eI,pointToSegmentDistance:eN,closestPointOnSegment:function(t,e,i){return eH(t,e,i)},clipSegment:eR,_getEdgeIntersection:eD,_getBitCode:ej,_sqClosestPointOnSegment:eH,isFlat:eF,_flat:eW};var te={__proto__:null,clipPolygon:eq},ti={project:function(t){return new Z(t.lng,t.lat)},unproject:function(t){return new R(t.y,t.x)},bounds:new A([-180,-90],[180,90])},tn={R:6378137,R_MINOR:6356752.314245179,bounds:new A([-20037508.34279,-15496570.73972],[20037508.34279,18764656.23138]),project:function(t){var e=Math.PI/180,i=this.R,n=t.lat*e,o=this.R_MINOR/i,o=Math.sqrt(1-o*o),s=o*Math.sin(n),s=Math.tan(Math.PI/4-n/2)/Math.pow((1-s)/(1+s),o/2),n=-i*Math.log(Math.max(s,1e-10));return new Z(t.lng*e*i,n)},unproject:function(t){for(var e,i=180/Math.PI,n=this.R,o=this.R_MINOR/n,s=Math.sqrt(1-o*o),r=Math.exp(-t.y/n),a=Math.PI/2-2*Math.atan(r),h=0,l=.1;h<15&&1e-7<Math.abs(l);h++)e=Math.pow((1-(e=s*Math.sin(a)))/(1+e),s/2),a+=l=Math.PI/2-2*Math.atan(r*e)-a;return new R(a*i,t.x*i/n)}},to={__proto__:null,LonLat:ti,Mercator:tn,SphericalMercator:F},tr=e({},H,{code:"EPSG:3395",projection:tn,transformation:q(ts=.5/(Math.PI*tn.R),.5,-ts,.5)}),eU=e({},H,{code:"EPSG:4326",projection:ti,transformation:q(1/180,1,-1/180,.5)}),ta=e({},j,{projection:ti,transformation:q(1,0,-1,0),scale:function(t){return Math.pow(2,t)},zoom:function(t){return Math.log(t)/Math.LN2},distance:function(t,e){var i=e.lng-t.lng,e=e.lat-t.lat;return Math.sqrt(i*i+e*e)},infinite:!0}),th=(j.Earth=H,j.EPSG3395=tr,j.EPSG3857=U,j.EPSG900913=V,j.EPSG4326=eU,j.Simple=ta,E.extend({options:{pane:"overlayPane",attribution:null,bubblingMouseEvents:!0},addTo:function(t){return t.addLayer(this),this},remove:function(){return this.removeFrom(this._map||this._mapToAdd)},removeFrom:function(t){return t&&t.removeLayer(this),this},getPane:function(t){return this._map.getPane(t?this.options[t]||t:this.options.pane)},addInteractiveTarget:function(t){return this._map._targets[r(t)]=this},removeInteractiveTarget:function(t){return delete this._map._targets[r(t)],this},getAttribution:function(){return this.options.attribution},_layerAdd:function(t){var e,i=t.target;i.hasLayer(this)&&(this._map=i,this._zoomAnimated=i._zoomAnimated,this.getEvents&&(e=this.getEvents(),i.on(e,this),this.once("remove",function(){i.off(e,this)},this)),this.onAdd(i),this.fire("add"),i.fire("layeradd",{layer:this}))}})),eV=(eM.include({addLayer:function(t){if(!t._layerAdd)throw Error("The provided object is not a Layer.");var e=r(t);return this._layers[e]||((this._layers[e]=t)._mapToAdd=this,t.beforeAdd&&t.beforeAdd(this),this.whenReady(t._layerAdd,t)),this},removeLayer:function(t){var e=r(t);return this._layers[e]&&(this._loaded&&t.onRemove(this),delete this._layers[e],this._loaded&&(this.fire("layerremove",{layer:t}),t.fire("remove")),t._map=t._mapToAdd=null),this},hasLayer:function(t){return r(t) in this._layers},eachLayer:function(t,e){for(var i in this._layers)t.call(e,this._layers[i]);return this},_addLayers:function(t){for(var e=0,i=(t=t?g(t)?t:[t]:[]).length;e<i;e++)this.addLayer(t[e])},_addZoomLimit:function(t){isNaN(t.options.maxZoom)&&isNaN(t.options.minZoom)||(this._zoomBoundLayers[r(t)]=t,this._updateZoomLevels())},_removeZoomLimit:function(t){t=r(t),this._zoomBoundLayers[t]&&(delete this._zoomBoundLayers[t],this._updateZoomLevels())},_updateZoomLevels:function(){var t,e=1/0,i=-1/0,n=this._getZoomSpan();for(t in this._zoomBoundLayers)var o=this._zoomBoundLayers[t].options,e=void 0===o.minZoom?e:Math.min(e,o.minZoom),i=void 0===o.maxZoom?i:Math.max(i,o.maxZoom);this._layersMaxZoom=i===-1/0?void 0:i,this._layersMinZoom=e===1/0?void 0:e,n!==this._getZoomSpan()&&this.fire("zoomlevelschange"),void 0===this.options.maxZoom&&this._layersMaxZoom&&this.getZoom()>this._layersMaxZoom&&this.setZoom(this._layersMaxZoom),void 0===this.options.minZoom&&this._layersMinZoom&&this.getZoom()<this._layersMinZoom&&this.setZoom(this._layersMinZoom)}}),th.extend({initialize:function(t,e){var i,n;if(_(this,e),this._layers={},t)for(i=0,n=t.length;i<n;i++)this.addLayer(t[i])},addLayer:function(t){var e=this.getLayerId(t);return this._layers[e]=t,this._map&&this._map.addLayer(t),this},removeLayer:function(t){return t=t in this._layers?t:this.getLayerId(t),this._map&&this._layers[t]&&this._map.removeLayer(this._layers[t]),delete this._layers[t],this},hasLayer:function(t){return("number"==typeof t?t:this.getLayerId(t))in this._layers},clearLayers:function(){return this.eachLayer(this.removeLayer,this)},invoke:function(t){var e,i,n=Array.prototype.slice.call(arguments,1);for(e in this._layers)(i=this._layers[e])[t]&&i[t].apply(i,n);return this},onAdd:function(t){this.eachLayer(t.addLayer,t)},onRemove:function(t){this.eachLayer(t.removeLayer,t)},eachLayer:function(t,e){for(var i in this._layers)t.call(e,this._layers[i]);return this},getLayer:function(t){return this._layers[t]},getLayers:function(){var t=[];return this.eachLayer(t.push,t),t},setZIndex:function(t){return this.invoke("setZIndex",t)},getLayerId:r})),eG=eV.extend({addLayer:function(t){return this.hasLayer(t)?this:(t.addEventParent(this),eV.prototype.addLayer.call(this,t),this.fire("layeradd",{layer:t}))},removeLayer:function(t){return this.hasLayer(t)?((t=t in this._layers?this._layers[t]:t).removeEventParent(this),eV.prototype.removeLayer.call(this,t),this.fire("layerremove",{layer:t})):this},setStyle:function(t){return this.invoke("setStyle",t)},bringToFront:function(){return this.invoke("bringToFront")},bringToBack:function(){return this.invoke("bringToBack")},getBounds:function(){var t,e=new I;for(t in this._layers){var i=this._layers[t];e.extend(i.getBounds?i.getBounds():i.getLatLng())}return e}}),eK=C.extend({options:{popupAnchor:[0,0],tooltipAnchor:[0,0],crossOrigin:!1},initialize:function(t){_(this,t)},createIcon:function(t){return this._createIcon("icon",t)},createShadow:function(t){return this._createIcon("shadow",t)},_createIcon:function(t,e){var i=this._getIconUrl(t);if(!i){if("icon"===t)throw Error("iconUrl not set in Icon options (see the docs).");return null}return i=this._createImg(i,e&&"IMG"===e.tagName?e:null),this._setIconStyles(i,t),(this.options.crossOrigin||""===this.options.crossOrigin)&&(i.crossOrigin=!0===this.options.crossOrigin?"":this.options.crossOrigin),i},_setIconStyles:function(t,e){var i=this.options,n=i[e+"Size"],n=B(n="number"==typeof n?[n,n]:n),o=B("shadow"===e&&i.shadowAnchor||i.iconAnchor||n&&n.divideBy(2,!0));t.className="leaflet-marker-"+e+" "+(i.className||""),o&&(t.style.marginLeft=-o.x+"px",t.style.marginTop=-o.y+"px"),n&&(t.style.width=n.x+"px",t.style.height=n.y+"px")},_createImg:function(t,e){return(e=e||document.createElement("img")).src=t,e},_getIconUrl:function(t){return tC.retina&&this.options[t+"RetinaUrl"]||this.options[t+"Url"]}}),eY=eK.extend({options:{iconUrl:"marker-icon.png",iconRetinaUrl:"marker-icon-2x.png",shadowUrl:"marker-shadow.png",iconSize:[25,41],iconAnchor:[12,41],popupAnchor:[1,-34],tooltipAnchor:[16,-28],shadowSize:[41,41]},_getIconUrl:function(t){return"string"!=typeof eY.imagePath&&(eY.imagePath=this._detectIconPath()),(this.options.imagePath||eY.imagePath)+eK.prototype._getIconUrl.call(this,t)},_stripUrl:function(t){function e(t,e,i){return(e=e.exec(t))&&e[i]}return(t=e(t,/^url\((['"])?(.+)\1\)$/,2))&&e(t,/^(.*)marker-icon\.png$/,1)},_detectIconPath:function(){var t=t$("div","leaflet-default-icon-path",document.body),e=tX(t,"background-image")||tX(t,"backgroundImage");return(document.body.removeChild(t),e=this._stripUrl(e))?e:(t=document.querySelector('link[href$="leaflet.css"]'))?t.href.substring(0,t.href.length-11-1):""}}),eJ=$.extend({initialize:function(t){this._marker=t},addHooks:function(){var t=this._marker._icon;this._draggable||(this._draggable=new eO(t,t,!0)),this._draggable.on({dragstart:this._onDragStart,predrag:this._onPreDrag,drag:this._onDrag,dragend:this._onDragEnd},this).enable(),t8(t,"leaflet-marker-draggable")},removeHooks:function(){this._draggable.off({dragstart:this._onDragStart,predrag:this._onPreDrag,drag:this._onDrag,dragend:this._onDragEnd},this).disable(),this._marker._icon&&t5(this._marker._icon,"leaflet-marker-draggable")},moved:function(){return this._draggable&&this._draggable._moved},_adjustPan:function(t){var e=this._marker,i=e._map,n=this._marker.options.autoPanSpeed,o=this._marker.options.autoPanPadding,s=ei(e._icon),r=i.getPixelBounds(),a=i.getPixelOrigin(),a=O(r.min._subtract(a).add(o),r.max._subtract(a).subtract(o));a.contains(s)||(o=B((Math.max(a.max.x,s.x)-a.max.x)/(r.max.x-a.max.x)-(Math.min(a.min.x,s.x)-a.min.x)/(r.min.x-a.min.x),(Math.max(a.max.y,s.y)-a.max.y)/(r.max.y-a.max.y)-(Math.min(a.min.y,s.y)-a.min.y)/(r.min.y-a.min.y)).multiplyBy(n),i.panBy(o,{animate:!1}),this._draggable._newPos._add(o),this._draggable._startPos._add(o),ee(e._icon,this._draggable._newPos),this._onDrag(t),this._panRequest=M(this._adjustPan.bind(this,t)))},_onDragStart:function(){this._oldLatLng=this._marker.getLatLng(),this._marker.closePopup&&this._marker.closePopup(),this._marker.fire("movestart").fire("dragstart")},_onPreDrag:function(t){this._marker.options.autoPan&&(z(this._panRequest),this._panRequest=M(this._adjustPan.bind(this,t)))},_onDrag:function(t){var e=this._marker,i=e._shadow,n=ei(e._icon),o=e._map.layerPointToLatLng(n);i&&ee(i,n),e._latlng=o,t.latlng=o,t.oldLatLng=this._oldLatLng,e.fire("move",t).fire("drag",t)},_onDragEnd:function(t){z(this._panRequest),delete this._oldLatLng,this._marker.fire("moveend").fire("dragend",t)}}),eX=th.extend({options:{icon:new eY,interactive:!0,keyboard:!0,title:"",alt:"Marker",zIndexOffset:0,opacity:1,riseOnHover:!1,riseOffset:250,pane:"markerPane",shadowPane:"shadowPane",bubblingMouseEvents:!1,autoPanOnFocus:!0,draggable:!1,autoPan:!1,autoPanPadding:[50,50],autoPanSpeed:10},initialize:function(t,e){_(this,e),this._latlng=D(t)},onAdd:function(t){this._zoomAnimated=this._zoomAnimated&&t.options.markerZoomAnimation,this._zoomAnimated&&t.on("zoomanim",this._animateZoom,this),this._initIcon(),this.update()},onRemove:function(t){this.dragging&&this.dragging.enabled()&&(this.options.draggable=!0,this.dragging.removeHooks()),delete this.dragging,this._zoomAnimated&&t.off("zoomanim",this._animateZoom,this),this._removeIcon(),this._removeShadow()},getEvents:function(){return{zoom:this.update,viewreset:this.update}},getLatLng:function(){return this._latlng},setLatLng:function(t){var e=this._latlng;return this._latlng=D(t),this.update(),this.fire("move",{oldLatLng:e,latlng:this._latlng})},setZIndexOffset:function(t){return this.options.zIndexOffset=t,this.update()},getIcon:function(){return this.options.icon},setIcon:function(t){return this.options.icon=t,this._map&&(this._initIcon(),this.update()),this._popup&&this.bindPopup(this._popup,this._popup.options),this},getElement:function(){return this._icon},update:function(){var t;return this._icon&&this._map&&(t=this._map.latLngToLayerPoint(this._latlng).round(),this._setPos(t)),this},_initIcon:function(){var t=this.options,e="leaflet-zoom-"+(this._zoomAnimated?"animated":"hide"),i=t.icon.createIcon(this._icon),n=!1,i=(i!==this._icon&&(this._icon&&this._removeIcon(),n=!0,t.title&&(i.title=t.title),"IMG"===i.tagName&&(i.alt=t.alt||"")),t8(i,e),t.keyboard&&(i.tabIndex="0",i.setAttribute("role","button")),this._icon=i,t.riseOnHover&&this.on({mouseover:this._bringToFront,mouseout:this._resetZIndex}),this.options.autoPanOnFocus&&el(i,"focus",this._panOnFocus,this),t.icon.createShadow(this._shadow)),o=!1;i!==this._shadow&&(this._removeShadow(),o=!0),i&&(t8(i,e),i.alt=""),this._shadow=i,t.opacity<1&&this._updateOpacity(),n&&this.getPane().appendChild(this._icon),this._initInteraction(),i&&o&&this.getPane(t.shadowPane).appendChild(this._shadow)},_removeIcon:function(){this.options.riseOnHover&&this.off({mouseover:this._bringToFront,mouseout:this._resetZIndex}),this.options.autoPanOnFocus&&ec(this._icon,"focus",this._panOnFocus,this),tQ(this._icon),this.removeInteractiveTarget(this._icon),this._icon=null},_removeShadow:function(){this._shadow&&tQ(this._shadow),this._shadow=null},_setPos:function(t){this._icon&&ee(this._icon,t),this._shadow&&ee(this._shadow,t),this._zIndex=t.y+this.options.zIndexOffset,this._resetZIndex()},_updateZIndex:function(t){this._icon&&(this._icon.style.zIndex=this._zIndex+t)},_animateZoom:function(t){t=this._map._latLngToNewLayerPoint(this._latlng,t.zoom,t.center).round(),this._setPos(t)},_initInteraction:function(){var t;this.options.interactive&&(t8(this._icon,"leaflet-interactive"),this.addInteractiveTarget(this._icon),eJ&&(t=this.options.draggable,this.dragging&&(t=this.dragging.enabled(),this.dragging.disable()),this.dragging=new eJ(this),t&&this.dragging.enable()))},setOpacity:function(t){return this.options.opacity=t,this._map&&this._updateOpacity(),this},_updateOpacity:function(){var t=this.options.opacity;this._icon&&t6(this._icon,t),this._shadow&&t6(this._shadow,t)},_bringToFront:function(){this._updateZIndex(this.options.riseOffset)},_resetZIndex:function(){this._updateZIndex(0)},_panOnFocus:function(){var t,e,i=this._map;i&&(t=(e=this.options.icon.options).iconSize?B(e.iconSize):B(0,0),e=e.iconAnchor?B(e.iconAnchor):B(0,0),i.panInside(this._latlng,{paddingTopLeft:e,paddingBottomRight:t.subtract(e)}))},_getPopupAnchor:function(){return this.options.icon.options.popupAnchor},_getTooltipAnchor:function(){return this.options.icon.options.tooltipAnchor}}),e$=th.extend({options:{stroke:!0,color:"#3388ff",weight:3,opacity:1,lineCap:"round",lineJoin:"round",dashArray:null,dashOffset:null,fill:!1,fillColor:null,fillOpacity:.2,fillRule:"evenodd",interactive:!0,bubblingMouseEvents:!0},beforeAdd:function(t){this._renderer=t.getRenderer(this)},onAdd:function(){this._renderer._initPath(this),this._reset(),this._renderer._addPath(this)},onRemove:function(){this._renderer._removePath(this)},redraw:function(){return this._map&&this._renderer._updatePath(this),this},setStyle:function(t){return _(this,t),this._renderer&&(this._renderer._updateStyle(this),this.options.stroke&&t&&Object.prototype.hasOwnProperty.call(t,"weight")&&this._updateBounds()),this},bringToFront:function(){return this._renderer&&this._renderer._bringToFront(this),this},bringToBack:function(){return this._renderer&&this._renderer._bringToBack(this),this},getElement:function(){return this._path},_reset:function(){this._project(),this._update()},_clickTolerance:function(){return(this.options.stroke?this.options.weight/2:0)+(this._renderer.options.tolerance||0)}}),eQ=e$.extend({options:{fill:!0,radius:10},initialize:function(t,e){_(this,e),this._latlng=D(t),this._radius=this.options.radius},setLatLng:function(t){var e=this._latlng;return this._latlng=D(t),this.redraw(),this.fire("move",{oldLatLng:e,latlng:this._latlng})},getLatLng:function(){return this._latlng},setRadius:function(t){return this.options.radius=this._radius=t,this.redraw()},getRadius:function(){return this._radius},setStyle:function(t){var e=t&&t.radius||this._radius;return e$.prototype.setStyle.call(this,t),this.setRadius(e),this},_project:function(){this._point=this._map.latLngToLayerPoint(this._latlng),this._updateBounds()},_updateBounds:function(){var t=this._radius,e=this._radiusY||t,i=this._clickTolerance(),t=[t+i,e+i];this._pxBounds=new A(this._point.subtract(t),this._point.add(t))},_update:function(){this._map&&this._updatePath()},_updatePath:function(){this._renderer._updateCircle(this)},_empty:function(){return this._radius&&!this._renderer._bounds.intersects(this._pxBounds)},_containsPoint:function(t){return t.distanceTo(this._point)<=this._radius+this._clickTolerance()}}),e0=eQ.extend({initialize:function(t,i,n){if(_(this,i="number"==typeof i?e({},n,{radius:i}):i),this._latlng=D(t),isNaN(this.options.radius))throw Error("Circle radius cannot be NaN");this._mRadius=this.options.radius},setRadius:function(t){return this._mRadius=t,this.redraw()},getRadius:function(){return this._mRadius},getBounds:function(){var t=[this._radius,this._radiusY||this._radius];return new I(this._map.layerPointToLatLng(this._point.subtract(t)),this._map.layerPointToLatLng(this._point.add(t)))},setStyle:e$.prototype.setStyle,_project:function(){var t,e,i,n,o,s=this._latlng.lng,r=this._latlng.lat,a=this._map,h=a.options.crs;h.distance===H.distance?(n=Math.PI/180,o=this._mRadius/H.R/n,t=a.project([r+o,s]),e=a.project([r-o,s]),(isNaN(n=Math.acos((Math.cos(o*n)-Math.sin(r*n)*Math.sin((i=a.unproject(e=t.add(e).divideBy(2)).lat)*n))/(Math.cos(r*n)*Math.cos(i*n)))/n)||0===n)&&(n=o/Math.cos(Math.PI/180*r)),this._point=e.subtract(a.getPixelOrigin()),this._radius=isNaN(n)?0:e.x-a.project([i,s-n]).x,this._radiusY=e.y-t.y):(o=h.unproject(h.project(this._latlng).subtract([this._mRadius,0])),this._point=a.latLngToLayerPoint(this._latlng),this._radius=this._point.x-a.latLngToLayerPoint(o).x),this._updateBounds()}}),e1=e$.extend({options:{smoothFactor:1,noClip:!1},initialize:function(t,e){_(this,e),this._setLatLngs(t)},getLatLngs:function(){return this._latlngs},setLatLngs:function(t){return this._setLatLngs(t),this.redraw()},isEmpty:function(){return!this._latlngs.length},closestLayerPoint:function(t){for(var e=1/0,i=null,n=eH,o=0,s=this._parts.length;o<s;o++)for(var r=this._parts[o],a=1,h=r.length;a<h;a++){var l,u,c=n(t,l=r[a-1],u=r[a],!0);c<e&&(e=c,i=n(t,l,u))}return i&&(i.distance=Math.sqrt(e)),i},getCenter:function(){if(!this._map)throw Error("Must add layer to map before using getCenter()");var t,e,i,n,o,s,r=this._rings[0],a=r.length;if(!a)return null;for(e=t=0;t<a-1;t++)e+=r[t].distanceTo(r[t+1])/2;if(0===e)return this._map.layerPointToLatLng(r[0]);for(i=t=0;t<a-1;t++)if(e<(i+=s=(n=r[t]).distanceTo(o=r[t+1])))return this._map.layerPointToLatLng([o.x-(s=(i-e)/s)*(o.x-n.x),o.y-s*(o.y-n.y)])},getBounds:function(){return this._bounds},addLatLng:function(t,e){return e=e||this._defaultShape(),t=D(t),e.push(t),this._bounds.extend(t),this.redraw()},_setLatLngs:function(t){this._bounds=new I,this._latlngs=this._convertLatLngs(t)},_defaultShape:function(){return eF(this._latlngs)?this._latlngs:this._latlngs[0]},_convertLatLngs:function(t){for(var e=[],i=eF(t),n=0,o=t.length;n<o;n++)i?(e[n]=D(t[n]),this._bounds.extend(e[n])):e[n]=this._convertLatLngs(t[n]);return e},_project:function(){var t=new A;this._rings=[],this._projectLatlngs(this._latlngs,this._rings,t),this._bounds.isValid()&&t.isValid()&&(this._rawPxBounds=t,this._updateBounds())},_updateBounds:function(){var t=this._clickTolerance(),t=new Z(t,t);this._rawPxBounds&&(this._pxBounds=new A([this._rawPxBounds.min.subtract(t),this._rawPxBounds.max.add(t)]))},_projectLatlngs:function(t,e,i){var n,o,s=t[0]instanceof R,r=t.length;if(s){for(o=[],n=0;n<r;n++)o[n]=this._map.latLngToLayerPoint(t[n]),i.extend(o[n]);e.push(o)}else for(n=0;n<r;n++)this._projectLatlngs(t[n],e,i)},_clipPoints:function(){var t=this._renderer._bounds;if(this._parts=[],this._pxBounds&&this._pxBounds.intersects(t)){if(this.options.noClip)this._parts=this._rings;else for(var e,i,n,o,s=this._parts,r=0,a=0,h=this._rings.length;r<h;r++)for(e=0,i=(o=this._rings[r]).length;e<i-1;e++)(n=eR(o[e],o[e+1],t,e,!0))&&(s[a]=s[a]||[],s[a].push(n[0]),n[1]===o[e+1]&&e!==i-2||(s[a].push(n[1]),a++))}},_simplifyPoints:function(){for(var t=this._parts,e=this.options.smoothFactor,i=0,n=t.length;i<n;i++)t[i]=eI(t[i],e)},_update:function(){this._map&&(this._clipPoints(),this._simplifyPoints(),this._updatePath())},_updatePath:function(){this._renderer._updatePoly(this)},_containsPoint:function(t,e){var i,n,o,s,r,a,h=this._clickTolerance();if(!this._pxBounds||!this._pxBounds.contains(t))return!1;for(i=0,s=this._parts.length;i<s;i++)for(n=0,o=(r=(a=this._parts[i]).length)-1;n<r;o=n++)if((e||0!==n)&&eN(t,a[o],a[n])<=h)return!0;return!1}});e1._flat=eW;var e2=e1.extend({options:{fill:!0},isEmpty:function(){return!this._latlngs.length||!this._latlngs[0].length},getCenter:function(){if(!this._map)throw Error("Must add layer to map before using getCenter()");var t,e,i,n,o,s,r,a,h,l=this._rings[0],u=l.length;if(!u)return null;for(t=s=r=a=0,e=u-1;t<u;e=t++)i=l[t],n=l[e],o=i.y*n.x-n.y*i.x,r+=(i.x+n.x)*o,a+=(i.y+n.y)*o,s+=3*o;return h=0===s?l[0]:[r/s,a/s],this._map.layerPointToLatLng(h)},_convertLatLngs:function(t){var t=e1.prototype._convertLatLngs.call(this,t),e=t.length;return 2<=e&&t[0]instanceof R&&t[0].equals(t[e-1])&&t.pop(),t},_setLatLngs:function(t){e1.prototype._setLatLngs.call(this,t),eF(this._latlngs)&&(this._latlngs=[this._latlngs])},_defaultShape:function(){return(eF(this._latlngs[0])?this._latlngs:this._latlngs[0])[0]},_clipPoints:function(){var t=this._renderer._bounds,e=this.options.weight,e=new Z(e,e),t=new A(t.min.subtract(e),t.max.add(e));if(this._parts=[],this._pxBounds&&this._pxBounds.intersects(t)){if(this.options.noClip)this._parts=this._rings;else for(var i,n=0,o=this._rings.length;n<o;n++)(i=eq(this._rings[n],t,!0)).length&&this._parts.push(i)}},_updatePath:function(){this._renderer._updatePoly(this,!0)},_containsPoint:function(t){var e,i,n,o,s,r,a,h,l=!1;if(!this._pxBounds||!this._pxBounds.contains(t))return!1;for(o=0,a=this._parts.length;o<a;o++)for(s=0,r=(h=(e=this._parts[o]).length)-1;s<h;r=s++)i=e[s],n=e[r],i.y>t.y!=n.y>t.y&&t.x<(n.x-i.x)*(t.y-i.y)/(n.y-i.y)+i.x&&(l=!l);return l||e1.prototype._containsPoint.call(this,t,!0)}}),e3=eG.extend({initialize:function(t,e){_(this,e),this._layers={},t&&this.addData(t)},addData:function(t){var e,i,n,o=g(t)?t:t.features;if(o){for(e=0,i=o.length;e<i;e++)((n=o[e]).geometries||n.geometry||n.features||n.coordinates)&&this.addData(n);return this}var s=this.options;if(s.filter&&!s.filter(t))return this;var r=e8(t,s);return r?(r.feature=ie(t),r.defaultOptions=r.options,this.resetStyle(r),s.onEachFeature&&s.onEachFeature(t,r),this.addLayer(r)):this},resetStyle:function(t){return void 0===t?this.eachLayer(this.resetStyle,this):(t.options=e({},t.defaultOptions),this._setLayerStyle(t,this.options.style),this)},setStyle:function(t){return this.eachLayer(function(e){this._setLayerStyle(e,t)},this)},_setLayerStyle:function(t,e){t.setStyle&&("function"==typeof e&&(e=e(t.feature)),t.setStyle(e))}});function e8(t,e){var i,n,o,s="Feature"===t.type?t.geometry:t,r=s?s.coordinates:null,a=[],h=e&&e.pointToLayer,l=e&&e.coordsToLatLng||e4;if(!r&&!s)return null;switch(s.type){case"Point":return e5(h,t,i=l(r),e);case"MultiPoint":for(n=0,o=r.length;n<o;n++)i=l(r[n]),a.push(e5(h,t,i,e));return new eG(a);case"LineString":case"MultiLineString":return new e1(e7(r,"LineString"===s.type?0:1,l),e);case"Polygon":case"MultiPolygon":return new e2(e7(r,"Polygon"===s.type?1:2,l),e);case"GeometryCollection":for(n=0,o=s.geometries.length;n<o;n++){var u=e8({geometry:s.geometries[n],type:"Feature",properties:t.properties},e);u&&a.push(u)}return new eG(a);default:throw Error("Invalid GeoJSON object.")}}function e5(t,e,i,n){return t?t(e,i):new eX(i,n&&n.markersInheritOptions&&n)}function e4(t){return new R(t[1],t[0],t[2])}function e7(t,e,i){for(var n=[],o=0,s=t.length;o<s;o++)n.push(e?e7(t[o],e-1,i):(i||e4)(t[o]));return n}function e6(t,e){return void 0!==(t=D(t)).alt?[u(t.lng,e),u(t.lat,e),u(t.alt,e)]:[u(t.lng,e),u(t.lat,e)]}function e9(t,e,i,n){for(var o=[],s=0,r=t.length;s<r;s++)o.push(e?e9(t[s],e-1,i,n):e6(t[s],n));return!e&&i&&o.push(o[0]),o}function it(t,i){return t.feature?e({},t.feature,{geometry:i}):ie(i)}function ie(t){return"Feature"===t.type||"FeatureCollection"===t.type?t:{type:"Feature",properties:{},geometry:t}}function ii(t,e){return new e3(t,e)}tl={toGeoJSON:function(t){return it(this,{type:"Point",coordinates:e6(this.getLatLng(),t)})}},eX.include(tl),e0.include(tl),eQ.include(tl),e1.include({toGeoJSON:function(t){var e=!eF(this._latlngs);return it(this,{type:(e?"Multi":"")+"LineString",coordinates:e9(this._latlngs,e?1:0,!1,t)})}}),e2.include({toGeoJSON:function(t){var e=!eF(this._latlngs),i=e&&!eF(this._latlngs[0]),t=e9(this._latlngs,i?2:e?1:0,!0,t);return it(this,{type:(i?"Multi":"")+"Polygon",coordinates:t=e?t:[t]})}}),eV.include({toMultiPoint:function(t){var e=[];return this.eachLayer(function(i){e.push(i.toGeoJSON(t).geometry.coordinates)}),it(this,{type:"MultiPoint",coordinates:e})},toGeoJSON:function(t){var e=this.feature&&this.feature.geometry&&this.feature.geometry.type;if("MultiPoint"===e)return this.toMultiPoint(t);var i="GeometryCollection"===e,n=[];return this.eachLayer(function(e){e.toGeoJSON&&(e=e.toGeoJSON(t),i?n.push(e.geometry):"FeatureCollection"===(e=ie(e)).type?n.push.apply(n,e.features):n.push(e))}),i?it(this,{geometries:n,type:"GeometryCollection"}):{type:"FeatureCollection",features:n}}});var tu=ii,io=th.extend({options:{opacity:1,alt:"",interactive:!1,crossOrigin:!1,errorOverlayUrl:"",zIndex:1,className:""},initialize:function(t,e,i){this._url=t,this._bounds=N(e),_(this,i)},onAdd:function(){this._image||(this._initImage(),this.options.opacity<1&&this._updateOpacity()),this.options.interactive&&(t8(this._image,"leaflet-interactive"),this.addInteractiveTarget(this._image)),this.getPane().appendChild(this._image),this._reset()},onRemove:function(){tQ(this._image),this.options.interactive&&this.removeInteractiveTarget(this._image)},setOpacity:function(t){return this.options.opacity=t,this._image&&this._updateOpacity(),this},setStyle:function(t){return t.opacity&&this.setOpacity(t.opacity),this},bringToFront:function(){return this._map&&t1(this._image),this},bringToBack:function(){return this._map&&t2(this._image),this},setUrl:function(t){return this._url=t,this._image&&(this._image.src=t),this},setBounds:function(t){return this._bounds=N(t),this._map&&this._reset(),this},getEvents:function(){var t={zoom:this._reset,viewreset:this._reset};return this._zoomAnimated&&(t.zoomanim=this._animateZoom),t},setZIndex:function(t){return this.options.zIndex=t,this._updateZIndex(),this},getBounds:function(){return this._bounds},getElement:function(){return this._image},_initImage:function(){var t="IMG"===this._url.tagName,e=this._image=t?this._url:t$("img");t8(e,"leaflet-image-layer"),this._zoomAnimated&&t8(e,"leaflet-zoom-animated"),this.options.className&&t8(e,this.options.className),e.onselectstart=l,e.onmousemove=l,e.onload=o(this.fire,this,"load"),e.onerror=o(this._overlayOnError,this,"error"),(this.options.crossOrigin||""===this.options.crossOrigin)&&(e.crossOrigin=!0===this.options.crossOrigin?"":this.options.crossOrigin),this.options.zIndex&&this._updateZIndex(),t?this._url=e.src:(e.src=this._url,e.alt=this.options.alt)},_animateZoom:function(t){var e=this._map.getZoomScale(t.zoom),t=this._map._latLngBoundsToNewLayerBounds(this._bounds,t.zoom,t.center).min;et(this._image,t,e)},_reset:function(){var t=this._image,e=new A(this._map.latLngToLayerPoint(this._bounds.getNorthWest()),this._map.latLngToLayerPoint(this._bounds.getSouthEast())),i=e.getSize();ee(t,e.min),t.style.width=i.x+"px",t.style.height=i.y+"px"},_updateOpacity:function(){t6(this._image,this.options.opacity)},_updateZIndex:function(){this._image&&void 0!==this.options.zIndex&&null!==this.options.zIndex&&(this._image.style.zIndex=this.options.zIndex)},_overlayOnError:function(){this.fire("error");var t=this.options.errorOverlayUrl;t&&this._url!==t&&(this._url=t,this._image.src=t)},getCenter:function(){return this._bounds.getCenter()}}),is=io.extend({options:{autoplay:!0,loop:!0,keepAspectRatio:!0,muted:!1,playsInline:!0},_initImage:function(){var t="VIDEO"===this._url.tagName,e=this._image=t?this._url:t$("video");if(t8(e,"leaflet-image-layer"),this._zoomAnimated&&t8(e,"leaflet-zoom-animated"),this.options.className&&t8(e,this.options.className),e.onselectstart=l,e.onmousemove=l,e.onloadeddata=o(this.fire,this,"load"),t){for(var i=e.getElementsByTagName("source"),n=[],s=0;s<i.length;s++)n.push(i[s].src);this._url=0<i.length?n:[e.src]}else{g(this._url)||(this._url=[this._url]),!this.options.keepAspectRatio&&Object.prototype.hasOwnProperty.call(e.style,"objectFit")&&(e.style.objectFit="fill"),e.autoplay=!!this.options.autoplay,e.loop=!!this.options.loop,e.muted=!!this.options.muted,e.playsInline=!!this.options.playsInline;for(var r=0;r<this._url.length;r++){var a=t$("source");a.src=this._url[r],e.appendChild(a)}}}}),ir=io.extend({_initImage:function(){var t=this._image=this._url;t8(t,"leaflet-image-layer"),this._zoomAnimated&&t8(t,"leaflet-zoom-animated"),this.options.className&&t8(t,this.options.className),t.onselectstart=l,t.onmousemove=l}}),ia=th.extend({options:{interactive:!1,offset:[0,0],className:"",pane:void 0},initialize:function(t,e){_(this,t),this._source=e},openOn:function(t){return(t=arguments.length?t:this._source._map).hasLayer(this)||t.addLayer(this),this},close:function(){return this._map&&this._map.removeLayer(this),this},toggle:function(t){return this._map?this.close():(arguments.length?this._source=t:t=this._source,this._prepareOpen(),this.openOn(t._map)),this},onAdd:function(t){this._zoomAnimated=t._zoomAnimated,this._container||this._initLayout(),t._fadeAnimated&&t6(this._container,0),clearTimeout(this._removeTimeout),this.getPane().appendChild(this._container),this.update(),t._fadeAnimated&&t6(this._container,1),this.bringToFront(),this.options.interactive&&(t8(this._container,"leaflet-interactive"),this.addInteractiveTarget(this._container))},onRemove:function(t){t._fadeAnimated?(t6(this._container,0),this._removeTimeout=setTimeout(o(tQ,void 0,this._container),200)):tQ(this._container),this.options.interactive&&(t5(this._container,"leaflet-interactive"),this.removeInteractiveTarget(this._container))},getLatLng:function(){return this._latlng},setLatLng:function(t){return this._latlng=D(t),this._map&&(this._updatePosition(),this._adjustPan()),this},getContent:function(){return this._content},setContent:function(t){return this._content=t,this.update(),this},getElement:function(){return this._container},update:function(){this._map&&(this._container.style.visibility="hidden",this._updateContent(),this._updateLayout(),this._updatePosition(),this._container.style.visibility="",this._adjustPan())},getEvents:function(){var t={zoom:this._updatePosition,viewreset:this._updatePosition};return this._zoomAnimated&&(t.zoomanim=this._animateZoom),t},isOpen:function(){return!!this._map&&this._map.hasLayer(this)},bringToFront:function(){return this._map&&t1(this._container),this},bringToBack:function(){return this._map&&t2(this._container),this},_prepareOpen:function(t){if(!(i=this._source)._map)return!1;if(i instanceof eG){var e,i=null,n=this._source._layers;for(e in n)if(n[e]._map){i=n[e];break}if(!i)return!1;this._source=i}if(!t){if(i.getCenter)t=i.getCenter();else if(i.getLatLng)t=i.getLatLng();else{if(!i.getBounds)throw Error("Unable to get source layer LatLng.");t=i.getBounds().getCenter()}}return this.setLatLng(t),this._map&&this.update(),!0},_updateContent:function(){if(this._content){var t=this._contentNode,e="function"==typeof this._content?this._content(this._source||this):this._content;if("string"==typeof e)t.innerHTML=e;else{for(;t.hasChildNodes();)t.removeChild(t.firstChild);t.appendChild(e)}this.fire("contentupdate")}},_updatePosition:function(){var t,e,i;this._map&&(e=this._map.latLngToLayerPoint(this._latlng),t=B(this.options.offset),i=this._getAnchor(),this._zoomAnimated?ee(this._container,e.add(i)):t=t.add(e).add(i),e=this._containerBottom=-t.y,i=this._containerLeft=-Math.round(this._containerWidth/2)+t.x,this._container.style.bottom=e+"px",this._container.style.left=i+"px")},_getAnchor:function(){return[0,0]}}),ih=(eM.include({_initOverlay:function(t,e,i,n){var o=e;return o instanceof t||(o=new t(n).setContent(e)),i&&o.setLatLng(i),o}}),th.include({_initOverlay:function(t,e,i,n){var o=i;return o instanceof t?(_(o,n),o._source=this):(o=e&&!n?e:new t(n,this)).setContent(i),o}}),ia.extend({options:{pane:"popupPane",offset:[0,7],maxWidth:300,minWidth:50,maxHeight:null,autoPan:!0,autoPanPaddingTopLeft:null,autoPanPaddingBottomRight:null,autoPanPadding:[5,5],keepInView:!1,closeButton:!0,autoClose:!0,closeOnEscapeKey:!0,className:""},openOn:function(t){return!(t=arguments.length?t:this._source._map).hasLayer(this)&&t._popup&&t._popup.options.autoClose&&t.removeLayer(t._popup),t._popup=this,ia.prototype.openOn.call(this,t)},onAdd:function(t){ia.prototype.onAdd.call(this,t),t.fire("popupopen",{popup:this}),this._source&&(this._source.fire("popupopen",{popup:this},!0),this._source instanceof e$||this._source.on("preclick",ef))},onRemove:function(t){ia.prototype.onRemove.call(this,t),t.fire("popupclose",{popup:this}),this._source&&(this._source.fire("popupclose",{popup:this},!0),this._source instanceof e$||this._source.off("preclick",ef))},getEvents:function(){var t=ia.prototype.getEvents.call(this);return(void 0!==this.options.closeOnClick?this.options.closeOnClick:this._map.options.closePopupOnClick)&&(t.preclick=this.close),this.options.keepInView&&(t.moveend=this._adjustPan),t},_initLayout:function(){var t="leaflet-popup",e=this._container=t$("div",t+" "+(this.options.className||"")+" leaflet-zoom-animated"),i=this._wrapper=t$("div",t+"-content-wrapper",e);this._contentNode=t$("div",t+"-content",i),ev(e),eg(this._contentNode),el(e,"contextmenu",ef),this._tipContainer=t$("div",t+"-tip-container",e),this._tip=t$("div",t+"-tip",this._tipContainer),this.options.closeButton&&((i=this._closeButton=t$("a",t+"-close-button",e)).setAttribute("role","button"),i.setAttribute("aria-label","Close popup"),i.href="#close",i.innerHTML='<span aria-hidden="true">&#215;</span>',el(i,"click",this.close,this))},_updateLayout:function(){var t=this._contentNode,e=t.style,i=(e.width="",e.whiteSpace="nowrap",t.offsetWidth),i=Math.min(i,this.options.maxWidth),i=(i=Math.max(i,this.options.minWidth),e.width=i+1+"px",e.whiteSpace="",e.height="",t.offsetHeight),n=this.options.maxHeight,o="leaflet-popup-scrolled";n&&n<i?(e.height=n+"px",t8(t,o)):t5(t,o),this._containerWidth=this._container.offsetWidth},_animateZoom:function(t){var t=this._map._latLngToNewLayerPoint(this._latlng,t.zoom,t.center),e=this._getAnchor();ee(this._container,t.add(e))},_adjustPan:function(t){var e,i,n,o,s,r,a,h;this.options.autoPan&&(this._map._panAnim&&this._map._panAnim.stop(),e=this._map,i=parseInt(tX(this._container,"marginBottom"),10)||0,i=this._container.offsetHeight+i,h=this._containerWidth,(n=new Z(this._containerLeft,-i-this._containerBottom))._add(ei(this._container)),n=e.layerPointToContainerPoint(n),s=B(this.options.autoPanPadding),o=B(this.options.autoPanPaddingTopLeft||s),s=B(this.options.autoPanPaddingBottomRight||s),r=e.getSize(),a=0,n.x+h+s.x>r.x&&(a=n.x+h-r.x+s.x),n.x-a-o.x<(h=0)&&(a=n.x-o.x),n.y+i+s.y>r.y&&(h=n.y+i-r.y+s.y),n.y-h-o.y<0&&(h=n.y-o.y),(a||h)&&e.fire("autopanstart").panBy([a,h],{animate:t&&"moveend"===t.type}))},_getAnchor:function(){return B(this._source&&this._source._getPopupAnchor?this._source._getPopupAnchor():[0,0])}})),il=(eM.mergeOptions({closePopupOnClick:!0}),eM.include({openPopup:function(t,e,i){return this._initOverlay(ih,t,e,i).openOn(this),this},closePopup:function(t){return(t=arguments.length?t:this._popup)&&t.close(),this}}),th.include({bindPopup:function(t,e){return this._popup=this._initOverlay(ih,this._popup,t,e),this._popupHandlersAdded||(this.on({click:this._openPopup,keypress:this._onKeyPress,remove:this.closePopup,move:this._movePopup}),this._popupHandlersAdded=!0),this},unbindPopup:function(){return this._popup&&(this.off({click:this._openPopup,keypress:this._onKeyPress,remove:this.closePopup,move:this._movePopup}),this._popupHandlersAdded=!1,this._popup=null),this},openPopup:function(t){return this._popup&&this._popup._prepareOpen(t)&&this._popup.openOn(this._map),this},closePopup:function(){return this._popup&&this._popup.close(),this},togglePopup:function(){return this._popup&&this._popup.toggle(this),this},isPopupOpen:function(){return!!this._popup&&this._popup.isOpen()},setPopupContent:function(t){return this._popup&&this._popup.setContent(t),this},getPopup:function(){return this._popup},_openPopup:function(t){var e;this._popup&&this._map&&(ex(t),e=t.layer||t.target,this._popup._source!==e||e instanceof e$?(this._popup._source=e,this.openPopup(t.latlng)):this._map.hasLayer(this._popup)?this.closePopup():this.openPopup(t.latlng))},_movePopup:function(t){this._popup.setLatLng(t.latlng)},_onKeyPress:function(t){13===t.originalEvent.keyCode&&this._openPopup(t)}}),ia.extend({options:{pane:"tooltipPane",offset:[0,0],direction:"auto",permanent:!1,sticky:!1,opacity:.9},onAdd:function(t){ia.prototype.onAdd.call(this,t),this.setOpacity(this.options.opacity),t.fire("tooltipopen",{tooltip:this}),this._source&&(this.addEventParent(this._source),this._source.fire("tooltipopen",{tooltip:this},!0))},onRemove:function(t){ia.prototype.onRemove.call(this,t),t.fire("tooltipclose",{tooltip:this}),this._source&&(this.removeEventParent(this._source),this._source.fire("tooltipclose",{tooltip:this},!0))},getEvents:function(){var t=ia.prototype.getEvents.call(this);return this.options.permanent||(t.preclick=this.close),t},_initLayout:function(){var t="leaflet-tooltip "+(this.options.className||"")+" leaflet-zoom-"+(this._zoomAnimated?"animated":"hide");this._contentNode=this._container=t$("div",t)},_updateLayout:function(){},_adjustPan:function(){},_setPosition:function(t){var e,i=this._map,n=this._container,o=i.latLngToContainerPoint(i.getCenter()),i=i.layerPointToContainerPoint(t),s=this.options.direction,r=n.offsetWidth,a=n.offsetHeight,h=B(this.options.offset),l=this._getAnchor(),i="top"===s?(e=r/2,a):"bottom"===s?(e=r/2,0):(e="center"===s?r/2:"right"===s?0:"left"===s?r:i.x<o.x?(s="right",0):(s="left",r+2*(h.x+l.x)),a/2);t=t.subtract(B(e,i,!0)).add(h).add(l),t5(n,"leaflet-tooltip-right"),t5(n,"leaflet-tooltip-left"),t5(n,"leaflet-tooltip-top"),t5(n,"leaflet-tooltip-bottom"),t8(n,"leaflet-tooltip-"+s),ee(n,t)},_updatePosition:function(){var t=this._map.latLngToLayerPoint(this._latlng);this._setPosition(t)},setOpacity:function(t){this.options.opacity=t,this._container&&t6(this._container,t)},_animateZoom:function(t){t=this._map._latLngToNewLayerPoint(this._latlng,t.zoom,t.center),this._setPosition(t)},_getAnchor:function(){return B(this._source&&this._source._getTooltipAnchor&&!this.options.sticky?this._source._getTooltipAnchor():[0,0])}})),iu=(eM.include({openTooltip:function(t,e,i){return this._initOverlay(il,t,e,i).openOn(this),this},closeTooltip:function(t){return t.close(),this}}),th.include({bindTooltip:function(t,e){return this._tooltip&&this.isTooltipOpen()&&this.unbindTooltip(),this._tooltip=this._initOverlay(il,this._tooltip,t,e),this._initTooltipInteractions(),this._tooltip.options.permanent&&this._map&&this._map.hasLayer(this)&&this.openTooltip(),this},unbindTooltip:function(){return this._tooltip&&(this._initTooltipInteractions(!0),this.closeTooltip(),this._tooltip=null),this},_initTooltipInteractions:function(t){var e;!t&&this._tooltipHandlersAdded||(e={remove:this.closeTooltip,move:this._moveTooltip},this._tooltip.options.permanent?e.add=this._openTooltip:(e.mouseover=this._openTooltip,e.mouseout=this.closeTooltip,e.click=this._openTooltip),this._tooltip.options.sticky&&(e.mousemove=this._moveTooltip),this[t?"off":"on"](e),this._tooltipHandlersAdded=!t)},openTooltip:function(t){return this._tooltip&&this._tooltip._prepareOpen(t)&&this._tooltip.openOn(this._map),this},closeTooltip:function(){if(this._tooltip)return this._tooltip.close()},toggleTooltip:function(){return this._tooltip&&this._tooltip.toggle(this),this},isTooltipOpen:function(){return this._tooltip.isOpen()},setTooltipContent:function(t){return this._tooltip&&this._tooltip.setContent(t),this},getTooltip:function(){return this._tooltip},_openTooltip:function(t){!this._tooltip||!this._map||this._map.dragging&&this._map.dragging.moving()||(this._tooltip._source=t.layer||t.target,this.openTooltip(this._tooltip.options.sticky?t.latlng:void 0))},_moveTooltip:function(t){var e=t.latlng;this._tooltip.options.sticky&&t.originalEvent&&(t=this._map.mouseEventToContainerPoint(t.originalEvent),t=this._map.containerPointToLayerPoint(t),e=this._map.layerPointToLatLng(t)),this._tooltip.setLatLng(e)}}),eK.extend({options:{iconSize:[12,12],html:!1,bgPos:null,className:"leaflet-div-icon"},createIcon:function(t){var t=t&&"DIV"===t.tagName?t:document.createElement("div"),e=this.options;return e.html instanceof Element?(t0(t),t.appendChild(e.html)):t.innerHTML=!1!==e.html?e.html:"",e.bgPos&&(e=B(e.bgPos),t.style.backgroundPosition=-e.x+"px "+-e.y+"px"),this._setIconStyles(t,"icon"),t},createShadow:function(){return null}}));eK.Default=eY;var ic=th.extend({options:{tileSize:256,opacity:1,updateWhenIdle:tC.mobile,updateWhenZooming:!0,updateInterval:200,zIndex:1,bounds:null,minZoom:0,maxZoom:void 0,maxNativeZoom:void 0,minNativeZoom:void 0,noWrap:!1,pane:"tilePane",className:"",keepBuffer:2},initialize:function(t){_(this,t)},onAdd:function(){this._initContainer(),this._levels={},this._tiles={},this._resetView()},beforeAdd:function(t){t._addZoomLimit(this)},onRemove:function(t){this._removeAllTiles(),tQ(this._container),t._removeZoomLimit(this),this._container=null,this._tileZoom=void 0},bringToFront:function(){return this._map&&(t1(this._container),this._setAutoZIndex(Math.max)),this},bringToBack:function(){return this._map&&(t2(this._container),this._setAutoZIndex(Math.min)),this},getContainer:function(){return this._container},setOpacity:function(t){return this.options.opacity=t,this._updateOpacity(),this},setZIndex:function(t){return this.options.zIndex=t,this._updateZIndex(),this},isLoading:function(){return this._loading},redraw:function(){var t;return this._map&&(this._removeAllTiles(),(t=this._clampZoom(this._map.getZoom()))!==this._tileZoom&&(this._tileZoom=t,this._updateLevels()),this._update()),this},getEvents:function(){var t={viewprereset:this._invalidateAll,viewreset:this._resetView,zoom:this._resetView,moveend:this._onMoveEnd};return this.options.updateWhenIdle||(this._onMove||(this._onMove=a(this._onMoveEnd,this.options.updateInterval,this)),t.move=this._onMove),this._zoomAnimated&&(t.zoomanim=this._animateZoom),t},createTile:function(){return document.createElement("div")},getTileSize:function(){var t=this.options.tileSize;return t instanceof Z?t:new Z(t,t)},_updateZIndex:function(){this._container&&void 0!==this.options.zIndex&&null!==this.options.zIndex&&(this._container.style.zIndex=this.options.zIndex)},_setAutoZIndex:function(t){for(var e,i=this.getPane().children,n=-t(-1/0,1/0),o=0,s=i.length;o<s;o++)e=i[o].style.zIndex,i[o]!==this._container&&e&&(n=t(n,+e));isFinite(n)&&(this.options.zIndex=n+t(-1,1),this._updateZIndex())},_updateOpacity:function(){if(this._map&&!tC.ielt9){t6(this._container,this.options.opacity);var t,e=+new Date,i=!1,n=!1;for(t in this._tiles){var o,s=this._tiles[t];s.current&&s.loaded&&(o=Math.min(1,(e-s.loaded)/200),t6(s.el,o),o<1?i=!0:(s.active?n=!0:this._onOpaqueTile(s),s.active=!0))}n&&!this._noPrune&&this._pruneTiles(),i&&(z(this._fadeFrame),this._fadeFrame=M(this._updateOpacity,this))}},_onOpaqueTile:l,_initContainer:function(){this._container||(this._container=t$("div","leaflet-layer "+(this.options.className||"")),this._updateZIndex(),this.options.opacity<1&&this._updateOpacity(),this.getPane().appendChild(this._container))},_updateLevels:function(){var t=this._tileZoom,e=this.options.maxZoom;if(void 0!==t){for(var i in this._levels)i=Number(i),this._levels[i].el.children.length||i===t?(this._levels[i].el.style.zIndex=e-Math.abs(t-i),this._onUpdateLevel(i)):(tQ(this._levels[i].el),this._removeTilesAtZoom(i),this._onRemoveLevel(i),delete this._levels[i]);var n=this._levels[t],o=this._map;return n||((n=this._levels[t]={}).el=t$("div","leaflet-tile-container leaflet-zoom-animated",this._container),n.el.style.zIndex=e,n.origin=o.project(o.unproject(o.getPixelOrigin()),t).round(),n.zoom=t,this._setZoomTransform(n,o.getCenter(),o.getZoom()),l(n.el.offsetWidth),this._onCreateLevel(n)),this._level=n}},_onUpdateLevel:l,_onRemoveLevel:l,_onCreateLevel:l,_pruneTiles:function(){if(this._map){var t,e,i,n=this._map.getZoom();if(n>this.options.maxZoom||n<this.options.minZoom)this._removeAllTiles();else{for(t in this._tiles)(i=this._tiles[t]).retain=i.current;for(t in this._tiles)(i=this._tiles[t]).current&&!i.active&&(e=i.coords,this._retainParent(e.x,e.y,e.z,e.z-5)||this._retainChildren(e.x,e.y,e.z,e.z+2));for(t in this._tiles)this._tiles[t].retain||this._removeTile(t)}}},_removeTilesAtZoom:function(t){for(var e in this._tiles)this._tiles[e].coords.z===t&&this._removeTile(e)},_removeAllTiles:function(){for(var t in this._tiles)this._removeTile(t)},_invalidateAll:function(){for(var t in this._levels)tQ(this._levels[t].el),this._onRemoveLevel(Number(t)),delete this._levels[t];this._removeAllTiles(),this._tileZoom=void 0},_retainParent:function(t,e,i,n){var t=Math.floor(t/2),e=Math.floor(e/2),i=i-1,o=new Z(+t,+e),o=(o.z=i,this._tileCoordsToKey(o)),o=this._tiles[o];return o&&o.active?o.retain=!0:(o&&o.loaded&&(o.retain=!0),n<i&&this._retainParent(t,e,i,n))},_retainChildren:function(t,e,i,n){for(var o=2*t;o<2*t+2;o++)for(var s=2*e;s<2*e+2;s++){var r=new Z(o,s),r=(r.z=i+1,this._tileCoordsToKey(r)),r=this._tiles[r];r&&r.active?r.retain=!0:(r&&r.loaded&&(r.retain=!0),i+1<n&&this._retainChildren(o,s,i+1,n))}},_resetView:function(t){t=t&&(t.pinch||t.flyTo),this._setView(this._map.getCenter(),this._map.getZoom(),t,t)},_animateZoom:function(t){this._setView(t.center,t.zoom,!0,t.noUpdate)},_clampZoom:function(t){var e=this.options;return void 0!==e.minNativeZoom&&t<e.minNativeZoom?e.minNativeZoom:void 0!==e.maxNativeZoom&&e.maxNativeZoom<t?e.maxNativeZoom:t},_setView:function(t,e,i,n){var o=Math.round(e),o=void 0!==this.options.maxZoom&&o>this.options.maxZoom||void 0!==this.options.minZoom&&o<this.options.minZoom?void 0:this._clampZoom(o),s=this.options.updateWhenZooming&&o!==this._tileZoom;n&&!s||(this._tileZoom=o,this._abortLoading&&this._abortLoading(),this._updateLevels(),this._resetGrid(),void 0!==o&&this._update(t),i||this._pruneTiles(),this._noPrune=!!i),this._setZoomTransforms(t,e)},_setZoomTransforms:function(t,e){for(var i in this._levels)this._setZoomTransform(this._levels[i],t,e)},_setZoomTransform:function(t,e,i){var n=this._map.getZoomScale(i,t.zoom),e=t.origin.multiplyBy(n).subtract(this._map._getNewPixelOrigin(e,i)).round();tC.any3d?et(t.el,e,n):ee(t.el,e)},_resetGrid:function(){var t=this._map,e=t.options.crs,i=this._tileSize=this.getTileSize(),n=this._tileZoom,o=this._map.getPixelWorldBounds(this._tileZoom);o&&(this._globalTileRange=this._pxBoundsToTileRange(o)),this._wrapX=e.wrapLng&&!this.options.noWrap&&[Math.floor(t.project([0,e.wrapLng[0]],n).x/i.x),Math.ceil(t.project([0,e.wrapLng[1]],n).x/i.y)],this._wrapY=e.wrapLat&&!this.options.noWrap&&[Math.floor(t.project([e.wrapLat[0],0],n).y/i.x),Math.ceil(t.project([e.wrapLat[1],0],n).y/i.y)]},_onMoveEnd:function(){this._map&&!this._map._animatingZoom&&this._update()},_getTiledPixelBounds:function(t){var e=this._map,i=e._animatingZoom?Math.max(e._animateToZoom,e.getZoom()):e.getZoom(),i=e.getZoomScale(i,this._tileZoom),t=e.project(t,this._tileZoom).floor(),e=e.getSize().divideBy(2*i);return new A(t.subtract(e),t.add(e))},_update:function(t){var e=this._map;if(e){var i=this._clampZoom(e.getZoom());if(void 0===t&&(t=e.getCenter()),void 0!==this._tileZoom){var n,e=this._getTiledPixelBounds(t),o=this._pxBoundsToTileRange(e),s=o.getCenter(),r=[],e=this.options.keepBuffer,a=new A(o.getBottomLeft().subtract([e,-e]),o.getTopRight().add([e,-e]));if(!(isFinite(o.min.x)&&isFinite(o.min.y)&&isFinite(o.max.x)&&isFinite(o.max.y)))throw Error("Attempted to load an infinite number of tiles");for(n in this._tiles){var h=this._tiles[n].coords;h.z===this._tileZoom&&a.contains(new Z(h.x,h.y))||(this._tiles[n].current=!1)}if(1<Math.abs(i-this._tileZoom))this._setView(t,i);else{for(var l=o.min.y;l<=o.max.y;l++)for(var u=o.min.x;u<=o.max.x;u++){var c,d=new Z(u,l);d.z=this._tileZoom,this._isValidTile(d)&&((c=this._tiles[this._tileCoordsToKey(d)])?c.current=!0:r.push(d))}if(r.sort(function(t,e){return t.distanceTo(s)-e.distanceTo(s)}),0!==r.length){this._loading||(this._loading=!0,this.fire("loading"));for(var _=document.createDocumentFragment(),u=0;u<r.length;u++)this._addTile(r[u],_);this._level.el.appendChild(_)}}}}},_isValidTile:function(t){var e=this._map.options.crs;if(!e.infinite){var i=this._globalTileRange;if(!e.wrapLng&&(t.x<i.min.x||t.x>i.max.x)||!e.wrapLat&&(t.y<i.min.y||t.y>i.max.y))return!1}return!this.options.bounds||(e=this._tileCoordsToBounds(t),N(this.options.bounds).overlaps(e))},_keyToBounds:function(t){return this._tileCoordsToBounds(this._keyToTileCoords(t))},_tileCoordsToNwSe:function(t){var e=this._map,i=this.getTileSize(),n=t.scaleBy(i),i=n.add(i);return[e.unproject(n,t.z),e.unproject(i,t.z)]},_tileCoordsToBounds:function(t){return t=new I((t=this._tileCoordsToNwSe(t))[0],t[1]),t=this.options.noWrap?t:this._map.wrapLatLngBounds(t)},_tileCoordsToKey:function(t){return t.x+":"+t.y+":"+t.z},_keyToTileCoords:function(t){var t=t.split(":"),e=new Z(+t[0],+t[1]);return e.z=+t[2],e},_removeTile:function(t){var e=this._tiles[t];e&&(tQ(e.el),delete this._tiles[t],this.fire("tileunload",{tile:e.el,coords:this._keyToTileCoords(t)}))},_initTile:function(t){t8(t,"leaflet-tile");var e=this.getTileSize();t.style.width=e.x+"px",t.style.height=e.y+"px",t.onselectstart=l,t.onmousemove=l,tC.ielt9&&this.options.opacity<1&&t6(t,this.options.opacity)},_addTile:function(t,e){var i=this._getTilePos(t),n=this._tileCoordsToKey(t),s=this.createTile(this._wrapCoords(t),o(this._tileReady,this,t));this._initTile(s),this.createTile.length<2&&M(o(this._tileReady,this,t,null,s)),ee(s,i),this._tiles[n]={el:s,coords:t,current:!0},e.appendChild(s),this.fire("tileloadstart",{tile:s,coords:t})},_tileReady:function(t,e,i){e&&this.fire("tileerror",{error:e,tile:i,coords:t}),(i=this._tiles[this._tileCoordsToKey(t)])&&(i.loaded=+new Date,this._map._fadeAnimated?(t6(i.el,0),z(this._fadeFrame),this._fadeFrame=M(this._updateOpacity,this)):(i.active=!0,this._pruneTiles()),e||(t8(i.el,"leaflet-tile-loaded"),this.fire("tileload",{tile:i.el,coords:t})),this._noTilesToLoad()&&(this._loading=!1,this.fire("load"),tC.ielt9||!this._map._fadeAnimated?M(this._pruneTiles,this):setTimeout(o(this._pruneTiles,this),250)))},_getTilePos:function(t){return t.scaleBy(this.getTileSize()).subtract(this._level.origin)},_wrapCoords:function(t){var e=new Z(this._wrapX?h(t.x,this._wrapX):t.x,this._wrapY?h(t.y,this._wrapY):t.y);return e.z=t.z,e},_pxBoundsToTileRange:function(t){var e=this.getTileSize();return new A(t.min.unscaleBy(e).floor(),t.max.unscaleBy(e).ceil().subtract([1,1]))},_noTilesToLoad:function(){for(var t in this._tiles)if(!this._tiles[t].loaded)return!1;return!0}}),id=ic.extend({options:{minZoom:0,maxZoom:18,subdomains:"abc",errorTileUrl:"",zoomOffset:0,tms:!1,zoomReverse:!1,detectRetina:!1,crossOrigin:!1,referrerPolicy:!1},initialize:function(t,e){this._url=t,(e=_(this,e)).detectRetina&&tC.retina&&0<e.maxZoom&&(e.tileSize=Math.floor(e.tileSize/2),e.zoomReverse?(e.zoomOffset--,e.minZoom++):(e.zoomOffset++,e.maxZoom--),e.minZoom=Math.max(0,e.minZoom)),"string"==typeof e.subdomains&&(e.subdomains=e.subdomains.split("")),this.on("tileunload",this._onTileRemove)},setUrl:function(t,e){return this._url===t&&void 0===e&&(e=!0),this._url=t,e||this.redraw(),this},createTile:function(t,e){var i=document.createElement("img");return el(i,"load",o(this._tileOnLoad,this,e,i)),el(i,"error",o(this._tileOnError,this,e,i)),(this.options.crossOrigin||""===this.options.crossOrigin)&&(i.crossOrigin=!0===this.options.crossOrigin?"":this.options.crossOrigin),"string"==typeof this.options.referrerPolicy&&(i.referrerPolicy=this.options.referrerPolicy),i.alt="",i.setAttribute("role","presentation"),i.src=this.getTileUrl(t),i},getTileUrl:function(t){var i={r:tC.retina?"@2x":"",s:this._getSubdomain(t),x:t.x,y:t.y,z:this._getZoomForUrl()};return this._map&&!this._map.options.crs.infinite&&(t=this._globalTileRange.max.y-t.y,this.options.tms&&(i.y=t),i["-y"]=t),f(this._url,e(i,this.options))},_tileOnLoad:function(t,e){tC.ielt9?setTimeout(o(t,this,null,e),0):t(null,e)},_tileOnError:function(t,e,i){var n=this.options.errorTileUrl;n&&e.getAttribute("src")!==n&&(e.src=n),t(i,e)},_onTileRemove:function(t){t.tile.onload=null},_getZoomForUrl:function(){var t=this._tileZoom;return(t=this.options.zoomReverse?this.options.maxZoom-t:t)+this.options.zoomOffset},_getSubdomain:function(t){return t=Math.abs(t.x+t.y)%this.options.subdomains.length,this.options.subdomains[t]},_abortLoading:function(){var t,e,i;for(t in this._tiles)this._tiles[t].coords.z!==this._tileZoom&&((i=this._tiles[t].el).onload=l,i.onerror=l,i.complete||(i.src=y,e=this._tiles[t].coords,tQ(i),delete this._tiles[t],this.fire("tileabort",{tile:i,coords:e})))},_removeTile:function(t){var e=this._tiles[t];if(e)return e.el.setAttribute("src",y),ic.prototype._removeTile.call(this,t)},_tileReady:function(t,e,i){if(this._map&&(!i||i.getAttribute("src")!==y))return ic.prototype._tileReady.call(this,t,e,i)}});function i_(t,e){return new id(t,e)}var ip=id.extend({defaultWmsParams:{service:"WMS",request:"GetMap",layers:"",styles:"",format:"image/jpeg",transparent:!1,version:"1.1.1"},options:{crs:null,uppercase:!1},initialize:function(t,i){this._url=t;var n,o=e({},this.defaultWmsParams);for(n in i)n in this.options||(o[n]=i[n]);var t=(i=_(this,i)).detectRetina&&tC.retina?2:1,s=this.getTileSize();o.width=s.x*t,o.height=s.y*t,this.wmsParams=o},onAdd:function(t){this._crs=this.options.crs||t.options.crs,this._wmsVersion=parseFloat(this.wmsParams.version);var e=1.3<=this._wmsVersion?"crs":"srs";this.wmsParams[e]=this._crs.code,id.prototype.onAdd.call(this,t)},getTileUrl:function(t){var e=this._tileCoordsToNwSe(t),i=this._crs,i=O(i.project(e[0]),i.project(e[1])),e=i.min,i=i.max,e=(1.3<=this._wmsVersion&&this._crs===eU?[e.y,e.x,i.y,i.x]:[e.x,e.y,i.x,i.y]).join(","),i=id.prototype.getTileUrl.call(this,t);return i+p(this.wmsParams,i,this.options.uppercase)+(this.options.uppercase?"&BBOX=":"&bbox=")+e},setParams:function(t,i){return e(this.wmsParams,t),i||this.redraw(),this}});id.WMS=ip,i_.wms=function(t,e){return new ip(t,e)};var im=th.extend({options:{padding:.1},initialize:function(t){_(this,t),r(this),this._layers=this._layers||{}},onAdd:function(){this._container||(this._initContainer(),this._zoomAnimated&&t8(this._container,"leaflet-zoom-animated")),this.getPane().appendChild(this._container),this._update(),this.on("update",this._updatePaths,this)},onRemove:function(){this.off("update",this._updatePaths,this),this._destroyContainer()},getEvents:function(){var t={viewreset:this._reset,zoom:this._onZoom,moveend:this._update,zoomend:this._onZoomEnd};return this._zoomAnimated&&(t.zoomanim=this._onAnimZoom),t},_onAnimZoom:function(t){this._updateTransform(t.center,t.zoom)},_onZoom:function(){this._updateTransform(this._map.getCenter(),this._map.getZoom())},_updateTransform:function(t,e){var i=this._map.getZoomScale(e,this._zoom),n=this._map.getSize().multiplyBy(.5+this.options.padding),o=this._map.project(this._center,e),n=n.multiplyBy(-i).add(o).subtract(this._map._getNewPixelOrigin(t,e));tC.any3d?et(this._container,n,i):ee(this._container,n)},_reset:function(){for(var t in this._update(),this._updateTransform(this._center,this._zoom),this._layers)this._layers[t]._reset()},_onZoomEnd:function(){for(var t in this._layers)this._layers[t]._project()},_updatePaths:function(){for(var t in this._layers)this._layers[t]._update()},_update:function(){var t=this.options.padding,e=this._map.getSize(),i=this._map.containerPointToLayerPoint(e.multiplyBy(-t)).round();this._bounds=new A(i,i.add(e.multiplyBy(1+2*t)).round()),this._center=this._map.getCenter(),this._zoom=this._map.getZoom()}}),ig=im.extend({options:{tolerance:0},getEvents:function(){var t=im.prototype.getEvents.call(this);return t.viewprereset=this._onViewPreReset,t},_onViewPreReset:function(){this._postponeUpdatePaths=!0},onAdd:function(){im.prototype.onAdd.call(this),this._draw()},_initContainer:function(){var t=this._container=document.createElement("canvas");el(t,"mousemove",this._onMouseMove,this),el(t,"click dblclick mousedown mouseup contextmenu",this._onClick,this),el(t,"mouseout",this._handleMouseOut,this),t._leaflet_disable_events=!0,this._ctx=t.getContext("2d")},_destroyContainer:function(){z(this._redrawRequest),delete this._ctx,tQ(this._container),ec(this._container),delete this._container},_updatePaths:function(){if(!this._postponeUpdatePaths){for(var t in this._redrawBounds=null,this._layers)this._layers[t]._update();this._redraw()}},_update:function(){var t,e,i,n;this._map._animatingZoom&&this._bounds||(im.prototype._update.call(this),t=this._bounds,e=this._container,i=t.getSize(),n=tC.retina?2:1,ee(e,t.min),e.width=n*i.x,e.height=n*i.y,e.style.width=i.x+"px",e.style.height=i.y+"px",tC.retina&&this._ctx.scale(2,2),this._ctx.translate(-t.min.x,-t.min.y),this.fire("update"))},_reset:function(){im.prototype._reset.call(this),this._postponeUpdatePaths&&(this._postponeUpdatePaths=!1,this._updatePaths())},_initPath:function(t){this._updateDashArray(t),t=(this._layers[r(t)]=t)._order={layer:t,prev:this._drawLast,next:null},this._drawLast&&(this._drawLast.next=t),this._drawLast=t,this._drawFirst=this._drawFirst||this._drawLast},_addPath:function(t){this._requestRedraw(t)},_removePath:function(t){var e=t._order,i=e.next,e=e.prev;i?i.prev=e:this._drawLast=e,e?e.next=i:this._drawFirst=i,delete t._order,delete this._layers[r(t)],this._requestRedraw(t)},_updatePath:function(t){this._extendRedrawBounds(t),t._project(),t._update(),this._requestRedraw(t)},_updateStyle:function(t){this._updateDashArray(t),this._requestRedraw(t)},_updateDashArray:function(t){if("string"==typeof t.options.dashArray){for(var e,i=t.options.dashArray.split(/[, ]+/),n=[],o=0;o<i.length;o++){if(isNaN(e=Number(i[o])))return;n.push(e)}t.options._dashArray=n}else t.options._dashArray=t.options.dashArray},_requestRedraw:function(t){this._map&&(this._extendRedrawBounds(t),this._redrawRequest=this._redrawRequest||M(this._redraw,this))},_extendRedrawBounds:function(t){var e;t._pxBounds&&(e=(t.options.weight||0)+1,this._redrawBounds=this._redrawBounds||new A,this._redrawBounds.extend(t._pxBounds.min.subtract([e,e])),this._redrawBounds.extend(t._pxBounds.max.add([e,e])))},_redraw:function(){this._redrawRequest=null,this._redrawBounds&&(this._redrawBounds.min._floor(),this._redrawBounds.max._ceil()),this._clear(),this._draw(),this._redrawBounds=null},_clear:function(){var t,e=this._redrawBounds;e?(t=e.getSize(),this._ctx.clearRect(e.min.x,e.min.y,t.x,t.y)):(this._ctx.save(),this._ctx.setTransform(1,0,0,1,0,0),this._ctx.clearRect(0,0,this._container.width,this._container.height),this._ctx.restore())},_draw:function(){var t,e,i=this._redrawBounds;this._ctx.save(),i&&(e=i.getSize(),this._ctx.beginPath(),this._ctx.rect(i.min.x,i.min.y,e.x,e.y),this._ctx.clip()),this._drawing=!0;for(var n=this._drawFirst;n;n=n.next)t=n.layer,(!i||t._pxBounds&&t._pxBounds.intersects(i))&&t._updatePath();this._drawing=!1,this._ctx.restore()},_updatePoly:function(t,e){if(this._drawing){var i,n,o,s,r=t._parts,a=r.length,h=this._ctx;if(a){for(h.beginPath(),i=0;i<a;i++){for(n=0,o=r[i].length;n<o;n++)s=r[i][n],h[n?"lineTo":"moveTo"](s.x,s.y);e&&h.closePath()}this._fillStroke(h,t)}}},_updateCircle:function(t){var e,i,n,o;this._drawing&&!t._empty()&&(e=t._point,i=this._ctx,n=Math.max(Math.round(t._radius),1),1!=(o=(Math.max(Math.round(t._radiusY),1)||n)/n)&&(i.save(),i.scale(1,o)),i.beginPath(),i.arc(e.x,e.y/o,n,0,2*Math.PI,!1),1!=o&&i.restore(),this._fillStroke(i,t))},_fillStroke:function(t,e){var i=e.options;i.fill&&(t.globalAlpha=i.fillOpacity,t.fillStyle=i.fillColor||i.color,t.fill(i.fillRule||"evenodd")),i.stroke&&0!==i.weight&&(t.setLineDash&&t.setLineDash(e.options&&e.options._dashArray||[]),t.globalAlpha=i.opacity,t.lineWidth=i.weight,t.strokeStyle=i.color,t.lineCap=i.lineCap,t.lineJoin=i.lineJoin,t.stroke())},_onClick:function(t){for(var e,i,n=this._map.mouseEventToLayerPoint(t),o=this._drawFirst;o;o=o.next)(e=o.layer).options.interactive&&e._containsPoint(n)&&(("click"===t.type||"preclick"===t.type)&&this._map._draggableMoved(e)||(i=e));this._fireEvent(!!i&&[i],t)},_onMouseMove:function(t){var e;!this._map||this._map.dragging.moving()||this._map._animatingZoom||(e=this._map.mouseEventToLayerPoint(t),this._handleMouseHover(t,e))},_handleMouseOut:function(t){var e=this._hoveredLayer;e&&(t5(this._container,"leaflet-interactive"),this._fireEvent([e],t,"mouseout"),this._hoveredLayer=null,this._mouseHoverThrottled=!1)},_handleMouseHover:function(t,e){if(!this._mouseHoverThrottled){for(var i,n,s=this._drawFirst;s;s=s.next)(i=s.layer).options.interactive&&i._containsPoint(e)&&(n=i);n!==this._hoveredLayer&&(this._handleMouseOut(t),n&&(t8(this._container,"leaflet-interactive"),this._fireEvent([n],t,"mouseover"),this._hoveredLayer=n)),this._fireEvent(!!this._hoveredLayer&&[this._hoveredLayer],t),this._mouseHoverThrottled=!0,setTimeout(o(function(){this._mouseHoverThrottled=!1},this),32)}},_fireEvent:function(t,e,i){this._map._fireDOMEvent(e,i||e.type,t)},_bringToFront:function(t){var e,i,n=t._order;n&&(e=n.next,i=n.prev,e&&((e.prev=i)?i.next=e:e&&(this._drawFirst=e),n.prev=this._drawLast,(this._drawLast.next=n).next=null,this._drawLast=n,this._requestRedraw(t)))},_bringToBack:function(t){var e,i,n=t._order;n&&(e=n.next,(i=n.prev)&&((i.next=e)?e.prev=i:i&&(this._drawLast=i),n.prev=null,n.next=this._drawFirst,this._drawFirst.prev=n,this._drawFirst=n,this._requestRedraw(t)))}});function iv(t){return tC.canvas?new ig(t):null}var iy=function(){try{return document.namespaces.add("lvml","urn:schemas-microsoft-com:vml"),function(t){return document.createElement("<lvml:"+t+' class="lvml">')}}catch(t){}return function(t){return document.createElement("<"+t+' xmlns="urn:schemas-microsoft.com:vml" class="lvml">')}}(),tc={_initContainer:function(){this._container=t$("div","leaflet-vml-container")},_update:function(){this._map._animatingZoom||(im.prototype._update.call(this),this.fire("update"))},_initPath:function(t){var e=t._container=iy("shape");t8(e,"leaflet-vml-shape "+(this.options.className||"")),e.coordsize="1 1",t._path=iy("path"),e.appendChild(t._path),this._updateStyle(t),this._layers[r(t)]=t},_addPath:function(t){var e=t._container;this._container.appendChild(e),t.options.interactive&&t.addInteractiveTarget(e)},_removePath:function(t){var e=t._container;tQ(e),t.removeInteractiveTarget(e),delete this._layers[r(t)]},_updateStyle:function(t){var e=t._stroke,i=t._fill,n=t.options,o=t._container;o.stroked=!!n.stroke,o.filled=!!n.fill,n.stroke?(e=e||(t._stroke=iy("stroke")),o.appendChild(e),e.weight=n.weight+"px",e.color=n.color,e.opacity=n.opacity,n.dashArray?e.dashStyle=g(n.dashArray)?n.dashArray.join(" "):n.dashArray.replace(/( *, *)/g," "):e.dashStyle="",e.endcap=n.lineCap.replace("butt","flat"),e.joinstyle=n.lineJoin):e&&(o.removeChild(e),t._stroke=null),n.fill?(i=i||(t._fill=iy("fill")),o.appendChild(i),i.color=n.fillColor||n.color,i.opacity=n.fillOpacity):i&&(o.removeChild(i),t._fill=null)},_updateCircle:function(t){var e=t._point.round(),i=Math.round(t._radius),n=Math.round(t._radiusY||i);this._setPath(t,t._empty()?"M0 0":"AL "+e.x+","+e.y+" "+i+","+n+" 0,23592600")},_setPath:function(t,e){t._path.v=e},_bringToFront:function(t){t1(t._container)},_bringToBack:function(t){t2(t._container)}},ix=tC.vml?iy:G,ib=im.extend({_initContainer:function(){this._container=ix("svg"),this._container.setAttribute("pointer-events","none"),this._rootGroup=ix("g"),this._container.appendChild(this._rootGroup)},_destroyContainer:function(){tQ(this._container),ec(this._container),delete this._container,delete this._rootGroup,delete this._svgSize},_update:function(){var t,e,i;this._map._animatingZoom&&this._bounds||(im.prototype._update.call(this),e=(t=this._bounds).getSize(),i=this._container,this._svgSize&&this._svgSize.equals(e)||(this._svgSize=e,i.setAttribute("width",e.x),i.setAttribute("height",e.y)),ee(i,t.min),i.setAttribute("viewBox",[t.min.x,t.min.y,e.x,e.y].join(" ")),this.fire("update"))},_initPath:function(t){var e=t._path=ix("path");t.options.className&&t8(e,t.options.className),t.options.interactive&&t8(e,"leaflet-interactive"),this._updateStyle(t),this._layers[r(t)]=t},_addPath:function(t){this._rootGroup||this._initContainer(),this._rootGroup.appendChild(t._path),t.addInteractiveTarget(t._path)},_removePath:function(t){tQ(t._path),t.removeInteractiveTarget(t._path),delete this._layers[r(t)]},_updatePath:function(t){t._project(),t._update()},_updateStyle:function(t){var e=t._path,t=t.options;e&&(t.stroke?(e.setAttribute("stroke",t.color),e.setAttribute("stroke-opacity",t.opacity),e.setAttribute("stroke-width",t.weight),e.setAttribute("stroke-linecap",t.lineCap),e.setAttribute("stroke-linejoin",t.lineJoin),t.dashArray?e.setAttribute("stroke-dasharray",t.dashArray):e.removeAttribute("stroke-dasharray"),t.dashOffset?e.setAttribute("stroke-dashoffset",t.dashOffset):e.removeAttribute("stroke-dashoffset")):e.setAttribute("stroke","none"),t.fill?(e.setAttribute("fill",t.fillColor||t.color),e.setAttribute("fill-opacity",t.fillOpacity),e.setAttribute("fill-rule",t.fillRule||"evenodd")):e.setAttribute("fill","none"))},_updatePoly:function(t,e){this._setPath(t,K(t._parts,e))},_updateCircle:function(t){var e=t._point,i=Math.max(Math.round(t._radius),1),n="a"+i+","+(Math.max(Math.round(t._radiusY),1)||i)+" 0 1,0 ",e=t._empty()?"M0 0":"M"+(e.x-i)+","+e.y+n+2*i+",0 "+n+-(2*i)+",0 ";this._setPath(t,e)},_setPath:function(t,e){t._path.setAttribute("d",e)},_bringToFront:function(t){t1(t._path)},_bringToBack:function(t){t2(t._path)}});function iw(t){return tC.svg||tC.vml?new ib(t):null}tC.vml&&ib.include(tc),eM.include({getRenderer:function(t){return t=(t=t.options.renderer||this._getPaneRenderer(t.options.pane)||this.options.renderer||this._renderer)||(this._renderer=this._createRenderer()),this.hasLayer(t)||this.addLayer(t),t},_getPaneRenderer:function(t){if("overlayPane"===t||void 0===t)return!1;var e=this._paneRenderers[t];return void 0===e&&(e=this._createRenderer({pane:t}),this._paneRenderers[t]=e),e},_createRenderer:function(t){return this.options.preferCanvas&&iv(t)||iw(t)}});var iP=e2.extend({initialize:function(t,e){e2.prototype.initialize.call(this,this._boundsToLatLngs(t),e)},setBounds:function(t){return this.setLatLngs(this._boundsToLatLngs(t))},_boundsToLatLngs:function(t){return[(t=N(t)).getSouthWest(),t.getNorthWest(),t.getNorthEast(),t.getSouthEast()]}});ib.create=ix,ib.pointsToPath=K,e3.geometryToLayer=e8,e3.coordsToLatLng=e4,e3.coordsToLatLngs=e7,e3.latLngToCoords=e6,e3.latLngsToCoords=e9,e3.getFeature=it,e3.asFeature=ie,eM.mergeOptions({boxZoom:!0});var Y=$.extend({initialize:function(t){this._map=t,this._container=t._container,this._pane=t._panes.overlayPane,this._resetStateTimeout=0,t.on("unload",this._destroy,this)},addHooks:function(){el(this._container,"mousedown",this._onMouseDown,this)},removeHooks:function(){ec(this._container,"mousedown",this._onMouseDown,this)},moved:function(){return this._moved},_destroy:function(){tQ(this._pane),delete this._pane},_resetState:function(){this._resetStateTimeout=0,this._moved=!1},_clearDeferredResetState:function(){0!==this._resetStateTimeout&&(clearTimeout(this._resetStateTimeout),this._resetStateTimeout=0)},_onMouseDown:function(t){if(!t.shiftKey||1!==t.which&&1!==t.button)return!1;this._clearDeferredResetState(),this._resetState(),tW(),en(),this._startPoint=this._map.mouseEventToContainerPoint(t),el(document,{contextmenu:ex,mousemove:this._onMouseMove,mouseup:this._onMouseUp,keydown:this._onKeyDown},this)},_onMouseMove:function(t){this._moved||(this._moved=!0,this._box=t$("div","leaflet-zoom-box",this._container),t8(this._container,"leaflet-crosshair"),this._map.fire("boxzoomstart")),this._point=this._map.mouseEventToContainerPoint(t);var t=new A(this._point,this._startPoint),e=t.getSize();ee(this._box,t.min),this._box.style.width=e.x+"px",this._box.style.height=e.y+"px"},_finish:function(){this._moved&&(tQ(this._box),t5(this._container,"leaflet-crosshair")),tq(),eo(),ec(document,{contextmenu:ex,mousemove:this._onMouseMove,mouseup:this._onMouseUp,keydown:this._onKeyDown},this)},_onMouseUp:function(t){1!==t.which&&1!==t.button||(this._finish(),this._moved&&(this._clearDeferredResetState(),this._resetStateTimeout=setTimeout(o(this._resetState,this),0),t=new I(this._map.containerPointToLatLng(this._startPoint),this._map.containerPointToLatLng(this._point)),this._map.fitBounds(t).fire("boxzoomend",{boxZoomBounds:t})))},_onKeyDown:function(t){27===t.keyCode&&(this._finish(),this._clearDeferredResetState(),this._resetState())}}),td=(eM.addInitHook("addHandler","boxZoom",Y),eM.mergeOptions({doubleClickZoom:!0}),$.extend({addHooks:function(){this._map.on("dblclick",this._onDoubleClick,this)},removeHooks:function(){this._map.off("dblclick",this._onDoubleClick,this)},_onDoubleClick:function(t){var e=this._map,i=e.getZoom(),n=e.options.zoomDelta,i=t.originalEvent.shiftKey?i-n:i+n;"center"===e.options.doubleClickZoom?e.setZoom(i):e.setZoomAround(t.containerPoint,i)}})),t_=(eM.addInitHook("addHandler","doubleClickZoom",td),eM.mergeOptions({dragging:!0,inertia:!0,inertiaDeceleration:3400,inertiaMaxSpeed:1/0,easeLinearity:.2,worldCopyJump:!1,maxBoundsViscosity:0}),$.extend({addHooks:function(){var t;this._draggable||(t=this._map,this._draggable=new eO(t._mapPane,t._container),this._draggable.on({dragstart:this._onDragStart,drag:this._onDrag,dragend:this._onDragEnd},this),this._draggable.on("predrag",this._onPreDragLimit,this),t.options.worldCopyJump&&(this._draggable.on("predrag",this._onPreDragWrap,this),t.on("zoomend",this._onZoomEnd,this),t.whenReady(this._onZoomEnd,this))),t8(this._map._container,"leaflet-grab leaflet-touch-drag"),this._draggable.enable(),this._positions=[],this._times=[]},removeHooks:function(){t5(this._map._container,"leaflet-grab"),t5(this._map._container,"leaflet-touch-drag"),this._draggable.disable()},moved:function(){return this._draggable&&this._draggable._moved},moving:function(){return this._draggable&&this._draggable._moving},_onDragStart:function(){var t,e=this._map;e._stop(),this._map.options.maxBounds&&this._map.options.maxBoundsViscosity?(t=N(this._map.options.maxBounds),this._offsetLimit=O(this._map.latLngToContainerPoint(t.getNorthWest()).multiplyBy(-1),this._map.latLngToContainerPoint(t.getSouthEast()).multiplyBy(-1).add(this._map.getSize())),this._viscosity=Math.min(1,Math.max(0,this._map.options.maxBoundsViscosity))):this._offsetLimit=null,e.fire("movestart").fire("dragstart"),e.options.inertia&&(this._positions=[],this._times=[])},_onDrag:function(t){var e,i;this._map.options.inertia&&(e=this._lastTime=+new Date,i=this._lastPos=this._draggable._absPos||this._draggable._newPos,this._positions.push(i),this._times.push(e),this._prunePositions(e)),this._map.fire("move",t).fire("drag",t)},_prunePositions:function(t){for(;1<this._positions.length&&50<t-this._times[0];)this._positions.shift(),this._times.shift()},_onZoomEnd:function(){var t=this._map.getSize().divideBy(2),e=this._map.latLngToLayerPoint([0,0]);this._initialWorldOffset=e.subtract(t).x,this._worldWidth=this._map.getPixelWorldBounds().getSize().x},_viscousLimit:function(t,e){return t-(t-e)*this._viscosity},_onPreDragLimit:function(){var t,e;this._viscosity&&this._offsetLimit&&(t=this._draggable._newPos.subtract(this._draggable._startPos),e=this._offsetLimit,t.x<e.min.x&&(t.x=this._viscousLimit(t.x,e.min.x)),t.y<e.min.y&&(t.y=this._viscousLimit(t.y,e.min.y)),t.x>e.max.x&&(t.x=this._viscousLimit(t.x,e.max.x)),t.y>e.max.y&&(t.y=this._viscousLimit(t.y,e.max.y)),this._draggable._newPos=this._draggable._startPos.add(t))},_onPreDragWrap:function(){var t=this._worldWidth,e=Math.round(t/2),i=this._initialWorldOffset,n=this._draggable._newPos.x,o=(n-e+i)%t+e-i,n=(n+e+i)%t-e-i,t=Math.abs(o+i)<Math.abs(n+i)?o:n;this._draggable._absPos=this._draggable._newPos.clone(),this._draggable._newPos.x=t},_onDragEnd:function(t){var e,i,n,o,s=this._map,r=s.options,a=!r.inertia||t.noInertia||this._times.length<2;s.fire("dragend",t),a?s.fire("moveend"):(this._prunePositions(+new Date),t=this._lastPos.subtract(this._positions[0]),a=(t=t.multiplyBy((e=r.easeLinearity)/(a=(this._lastTime-this._times[0])/1e3))).distanceTo([0,0]),i=Math.min(r.inertiaMaxSpeed,a),(o=(t=t.multiplyBy(i/a)).multiplyBy(-(n=i/(r.inertiaDeceleration*e))/2).round()).x||o.y?(o=s._limitOffset(o,s.options.maxBounds),M(function(){s.panBy(o,{duration:n,easeLinearity:e,noMoveStart:!0,animate:!0})})):s.fire("moveend"))}})),tp=(eM.addInitHook("addHandler","dragging",t_),eM.mergeOptions({keyboard:!0,keyboardPanDelta:80}),$.extend({keyCodes:{left:[37],right:[39],down:[40],up:[38],zoomIn:[187,107,61,171],zoomOut:[189,109,54,173]},initialize:function(t){this._map=t,this._setPanDelta(t.options.keyboardPanDelta),this._setZoomDelta(t.options.zoomDelta)},addHooks:function(){var t=this._map._container;t.tabIndex<=0&&(t.tabIndex="0"),el(t,{focus:this._onFocus,blur:this._onBlur,mousedown:this._onMouseDown},this),this._map.on({focus:this._addHooks,blur:this._removeHooks},this)},removeHooks:function(){this._removeHooks(),ec(this._map._container,{focus:this._onFocus,blur:this._onBlur,mousedown:this._onMouseDown},this),this._map.off({focus:this._addHooks,blur:this._removeHooks},this)},_onMouseDown:function(){var t,e,i;this._focused||(i=document.body,t=document.documentElement,e=i.scrollTop||t.scrollTop,i=i.scrollLeft||t.scrollLeft,this._map._container.focus(),window.scrollTo(i,e))},_onFocus:function(){this._focused=!0,this._map.fire("focus")},_onBlur:function(){this._focused=!1,this._map.fire("blur")},_setPanDelta:function(t){for(var e=this._panKeys={},i=this.keyCodes,n=0,o=i.left.length;n<o;n++)e[i.left[n]]=[-1*t,0];for(n=0,o=i.right.length;n<o;n++)e[i.right[n]]=[t,0];for(n=0,o=i.down.length;n<o;n++)e[i.down[n]]=[0,t];for(n=0,o=i.up.length;n<o;n++)e[i.up[n]]=[0,-1*t]},_setZoomDelta:function(t){for(var e=this._zoomKeys={},i=this.keyCodes,n=0,o=i.zoomIn.length;n<o;n++)e[i.zoomIn[n]]=t;for(n=0,o=i.zoomOut.length;n<o;n++)e[i.zoomOut[n]]=-t},_addHooks:function(){el(document,"keydown",this._onKeyDown,this)},_removeHooks:function(){ec(document,"keydown",this._onKeyDown,this)},_onKeyDown:function(t){if(!(t.altKey||t.ctrlKey||t.metaKey)){var e,i=t.keyCode,n=this._map;if(i in this._panKeys)n._panAnim&&n._panAnim._inProgress||(e=this._panKeys[i],t.shiftKey&&(e=B(e).multiplyBy(3)),n.panBy(e),n.options.maxBounds&&n.panInsideBounds(n.options.maxBounds));else if(i in this._zoomKeys)n.setZoom(n.getZoom()+(t.shiftKey?3:1)*this._zoomKeys[i]);else{if(27!==i||!n._popup||!n._popup.options.closeOnEscapeKey)return;n.closePopup()}ex(t)}}})),tm=(eM.addInitHook("addHandler","keyboard",tp),eM.mergeOptions({scrollWheelZoom:!0,wheelDebounceTime:40,wheelPxPerZoomLevel:60}),$.extend({addHooks:function(){el(this._map._container,"wheel",this._onWheelScroll,this),this._delta=0},removeHooks:function(){ec(this._map._container,"wheel",this._onWheelScroll,this)},_onWheelScroll:function(t){var e=eP(t),e=(this._delta+=e,this._lastMousePos=this._map.mouseEventToContainerPoint(t),this._startTime||(this._startTime=+new Date),Math.max(this._map.options.wheelDebounceTime-(+new Date-this._startTime),0));clearTimeout(this._timer),this._timer=setTimeout(o(this._performZoom,this),e),ex(t)},_performZoom:function(){var t=this._map,e=t.getZoom(),i=this._map.options.zoomSnap||0,n=(t._stop(),this._delta/(4*this._map.options.wheelPxPerZoomLevel)),n=4*Math.log(2/(1+Math.exp(-Math.abs(n))))/Math.LN2,i=i?Math.ceil(n/i)*i:n,n=t._limitZoom(e+(0<this._delta?i:-i))-e;this._delta=0,this._startTime=null,n&&("center"===t.options.scrollWheelZoom?t.setZoom(e+n):t.setZoomAround(this._lastMousePos,e+n))}})),tf=(eM.addInitHook("addHandler","scrollWheelZoom",tm),eM.mergeOptions({tapHold:tC.touchNative&&tC.safari&&tC.mobile,tapTolerance:15}),$.extend({addHooks:function(){el(this._map._container,"touchstart",this._onDown,this)},removeHooks:function(){ec(this._map._container,"touchstart",this._onDown,this)},_onDown:function(t){var e;clearTimeout(this._holdTimeout),1===t.touches.length&&(e=t.touches[0],this._startPos=this._newPos=new Z(e.clientX,e.clientY),this._holdTimeout=setTimeout(o(function(){this._cancel(),this._isTapValid()&&(el(document,"touchend",ey),el(document,"touchend touchcancel",this._cancelClickPrevent),this._simulateEvent("contextmenu",e))},this),600),el(document,"touchend touchcancel contextmenu",this._cancel,this),el(document,"touchmove",this._onMove,this))},_cancelClickPrevent:function t(){ec(document,"touchend",ey),ec(document,"touchend touchcancel",t)},_cancel:function(){clearTimeout(this._holdTimeout),ec(document,"touchend touchcancel contextmenu",this._cancel,this),ec(document,"touchmove",this._onMove,this)},_onMove:function(t){t=t.touches[0],this._newPos=new Z(t.clientX,t.clientY)},_isTapValid:function(){return this._newPos.distanceTo(this._startPos)<=this._map.options.tapTolerance},_simulateEvent:function(t,e){(t=new MouseEvent(t,{bubbles:!0,cancelable:!0,view:window,screenX:e.screenX,screenY:e.screenY,clientX:e.clientX,clientY:e.clientY}))._simulated=!0,e.target.dispatchEvent(t)}})),tg=(eM.addInitHook("addHandler","tapHold",tf),eM.mergeOptions({touchZoom:tC.touch,bounceAtZoomLimits:!0}),$.extend({addHooks:function(){t8(this._map._container,"leaflet-touch-zoom"),el(this._map._container,"touchstart",this._onTouchStart,this)},removeHooks:function(){t5(this._map._container,"leaflet-touch-zoom"),ec(this._map._container,"touchstart",this._onTouchStart,this)},_onTouchStart:function(t){var e,i,n=this._map;!t.touches||2!==t.touches.length||n._animatingZoom||this._zooming||(e=n.mouseEventToContainerPoint(t.touches[0]),i=n.mouseEventToContainerPoint(t.touches[1]),this._centerPoint=n.getSize()._divideBy(2),this._startLatLng=n.containerPointToLatLng(this._centerPoint),"center"!==n.options.touchZoom&&(this._pinchStartLatLng=n.containerPointToLatLng(e.add(i)._divideBy(2))),this._startDist=e.distanceTo(i),this._startZoom=n.getZoom(),this._moved=!1,this._zooming=!0,n._stop(),el(document,"touchmove",this._onTouchMove,this),el(document,"touchend touchcancel",this._onTouchEnd,this),ey(t))},_onTouchMove:function(t){if(t.touches&&2===t.touches.length&&this._zooming){var e=this._map,i=e.mouseEventToContainerPoint(t.touches[0]),n=e.mouseEventToContainerPoint(t.touches[1]),s=i.distanceTo(n)/this._startDist;if(this._zoom=e.getScaleZoom(s,this._startZoom),!e.options.bounceAtZoomLimits&&(this._zoom<e.getMinZoom()&&s<1||this._zoom>e.getMaxZoom()&&1<s)&&(this._zoom=e._limitZoom(this._zoom)),"center"===e.options.touchZoom){if(this._center=this._startLatLng,1==s)return}else{if(i=i._add(n)._divideBy(2)._subtract(this._centerPoint),1==s&&0===i.x&&0===i.y)return;this._center=e.unproject(e.project(this._pinchStartLatLng,this._zoom).subtract(i),this._zoom)}this._moved||(e._moveStart(!0,!1),this._moved=!0),z(this._animRequest),n=o(e._move,e,this._center,this._zoom,{pinch:!0,round:!1}),this._animRequest=M(n,this,!0),ey(t)}},_onTouchEnd:function(){this._moved&&this._zooming?(this._zooming=!1,z(this._animRequest),ec(document,"touchmove",this._onTouchMove,this),ec(document,"touchend touchcancel",this._onTouchEnd,this),this._map.options.zoomAnimation?this._map._animateZoom(this._center,this._map._limitZoom(this._zoom),!0,this._map.options.zoomSnap):this._map._resetView(this._center,this._map._limitZoom(this._zoom))):this._zooming=!1}})),iL=(eM.addInitHook("addHandler","touchZoom",tg),eM.BoxZoom=Y,eM.DoubleClickZoom=td,eM.Drag=t_,eM.Keyboard=tp,eM.ScrollWheelZoom=tm,eM.TapHold=tf,eM.TouchZoom=tg,t.Bounds=A,t.Browser=tC,t.CRS=j,t.Canvas=ig,t.Circle=e0,t.CircleMarker=eQ,t.Class=C,t.Control=eS,t.DivIcon=iu,t.DivOverlay=ia,t.DomEvent=X,t.DomUtil=J,t.Draggable=eO,t.Evented=E,t.FeatureGroup=eG,t.GeoJSON=e3,t.GridLayer=ic,t.Handler=$,t.Icon=eK,t.ImageOverlay=io,t.LatLng=R,t.LatLngBounds=I,t.Layer=th,t.LayerGroup=eV,t.LineUtil=tt,t.Map=eM,t.Marker=eX,t.Mixin=Q,t.Path=e$,t.Point=Z,t.PolyUtil=te,t.Polygon=e2,t.Polyline=e1,t.Popup=ih,t.PosAnimation=eT,t.Projection=to,t.Rectangle=iP,t.Renderer=im,t.SVG=ib,t.SVGOverlay=ir,t.TileLayer=id,t.Tooltip=il,t.Transformation=W,t.Util={__proto__:null,extend:e,create:i,bind:o,get lastId(){return s},stamp:r,throttle:a,wrapNum:h,falseFn:l,formatNum:u,trim:c,splitWords:d,setOptions:_,getParamString:p,template:f,isArray:g,indexOf:v,emptyImageUrl:y,requestFn:P,cancelFn:T,requestAnimFrame:M,cancelAnimFrame:z},t.VideoOverlay=is,t.bind=o,t.bounds=O,t.canvas=iv,t.circle=function(t,e,i){return new e0(t,e,i)},t.circleMarker=function(t,e){return new eQ(t,e)},t.control=ez,t.divIcon=function(t){return new iu(t)},t.extend=e,t.featureGroup=function(t,e){return new eG(t,e)},t.geoJSON=ii,t.geoJson=tu,t.gridLayer=function(t){return new ic(t)},t.icon=function(t){return new eK(t)},t.imageOverlay=function(t,e,i){return new io(t,e,i)},t.latLng=D,t.latLngBounds=N,t.layerGroup=function(t,e){return new eV(t,e)},t.map=function(t,e){return new eM(t,e)},t.marker=function(t,e){return new eX(t,e)},t.point=B,t.polygon=function(t,e){return new e2(t,e)},t.polyline=function(t,e){return new e1(t,e)},t.popup=function(t,e){return new ih(t,e)},t.rectangle=function(t,e){return new iP(t,e)},t.setOptions=_,t.stamp=r,t.svg=iw,t.svgOverlay=function(t,e,i){return new ir(t,e,i)},t.tileLayer=i_,t.tooltip=function(t,e){return new il(t,e)},t.transformation=q,t.version="1.8.0",t.videoOverlay=function(t,e,i){return new is(t,e,i)},window.L);t.noConflict=function(){return window.L=iL,this},window.L=t}(e)}},e={};function i(n){var o=e[n];if(void 0!==o)return o.exports;var s=e[n]={exports:{}};return t[n].call(s.exports,s,s.exports,i),s.exports}(()=>{i.n=t=>{var e=t&&t.__esModule?()=>t.default:()=>t;return i.d(e,{a:e}),e}})(),(()=>{i.d=(t,e)=>{for(var n in e)i.o(e,n)&&!i.o(t,n)&&Object.defineProperty(t,n,{enumerable:!0,get:e[n]})}})(),(()=>{i.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e)})(),(()=>{"use strict";var t=i(857),e=i.n(t);class n{static ucFirst(t){return t.charAt(0).toUpperCase()+t.slice(1)}static lcFirst(t){return t.charAt(0).toLowerCase()+t.slice(1)}static toDashCase(t){return t.replace(/([A-Z])/g,"-$1").replace(/^-/,"").toLowerCase()}static toLowerCamelCase(t,e){let i=n.toUpperCamelCase(t,e);return n.lcFirst(i)}static toUpperCamelCase(t,e){return e?t.split(e).map(t=>n.ucFirst(t.toLowerCase())).join(""):n.ucFirst(t.toLowerCase())}static parsePrimitive(t){try{return/^\d+(.|,)\d+$/.test(t)&&(t=t.replace(",",".")),JSON.parse(t)}catch(e){return t.toString()}}}class o{static isNode(t){return"object"==typeof t&&null!==t&&(t===document||t===window||t instanceof Node)}static hasAttribute(t,e){if(!o.isNode(t))throw Error("The element must be a valid HTML Node!");return"function"==typeof t.hasAttribute&&t.hasAttribute(e)}static getAttribute(t,e){let i=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(i&&!1===o.hasAttribute(t,e))throw Error('The required property "'.concat(e,'" does not exist!'));if("function"!=typeof t.getAttribute){if(i)throw Error("This node doesn't support the getAttribute function!");return}return t.getAttribute(e)}static getDataAttribute(t,e){let i=!(arguments.length>2)||void 0===arguments[2]||arguments[2],s=e.replace(/^data(|-)/,""),r=n.toLowerCamelCase(s,"-");if(!o.isNode(t)){if(i)throw Error("The passed node is not a valid HTML Node!");return}if(void 0===t.dataset){if(i)throw Error("This node doesn't support the dataset attribute!");return}let a=t.dataset[r];if(void 0===a){if(i)throw Error('The required data attribute "'.concat(e,'" does not exist on ').concat(t,"!"));return a}return n.parsePrimitive(a)}static querySelector(t,e){let i=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(i&&!o.isNode(t))throw Error("The parent node is not a valid HTML Node!");let n=t.querySelector(e)||!1;if(i&&!1===n)throw Error('The required element "'.concat(e,'" does not exist in parent node!'));return n}static querySelectorAll(t,e){let i=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(i&&!o.isNode(t))throw Error("The parent node is not a valid HTML Node!");let n=t.querySelectorAll(e);if(0===n.length&&(n=!1),i&&!1===n)throw Error('At least one item of "'.concat(e,'" must exist in parent node!'));return n}static getFocusableElements(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:document.body;return t.querySelectorAll('\n            input:not([tabindex^="-"]):not([disabled]):not([type="hidden"]),\n            select:not([tabindex^="-"]):not([disabled]),\n            textarea:not([tabindex^="-"]):not([disabled]),\n            button:not([tabindex^="-"]):not([disabled]),\n            a[href]:not([tabindex^="-"]):not([disabled]),\n            [tabindex]:not([tabindex^="-"]):not([disabled])\n        ')}static getFirstFocusableElement(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:document.body;return this.getFocusableElements(t)[0]}static getLastFocusableElement(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:document,e=this.getFocusableElements(t);return e[e.length-1]}}class s{publish(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=arguments.length>2&&void 0!==arguments[2]&&arguments[2],n=new CustomEvent(t,{detail:e,cancelable:i});return this.el.dispatchEvent(n),n}subscribe(t,e){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},n=this,o=t.split("."),s=i.scope?e.bind(i.scope):e;if(i.once&&!0===i.once){let e=s;s=function(i){n.unsubscribe(t),e(i)}}return this.el.addEventListener(o[0],s),this.listeners.push({splitEventName:o,opts:i,cb:s}),!0}unsubscribe(t){let e=t.split(".");return this.listeners=this.listeners.reduce((t,i)=>([...i.splitEventName].sort().toString()===e.sort().toString()?this.el.removeEventListener(i.splitEventName[0],i.cb):t.push(i),t),[]),!0}reset(){return this.listeners.forEach(t=>{this.el.removeEventListener(t.splitEventName[0],t.cb)}),this.listeners=[],!0}get el(){return this._el}set el(t){this._el=t}get listeners(){return this._listeners}set listeners(t){this._listeners=t}constructor(t=document){this._el=t,t.$emitter=this,this._listeners=[]}}class r{init(){throw Error('The "init" method for the plugin "'.concat(this._pluginName,'" is not defined.'))}update(){}_init(){this._initialized||(this.init(),this._initialized=!0)}_update(){this._initialized&&this.update()}_mergeOptions(t){let i=n.toDashCase(this._pluginName),s=o.getDataAttribute(this.el,"data-".concat(i,"-config"),!1),r=o.getAttribute(this.el,"data-".concat(i,"-options"),!1),a=[this.constructor.options,this.options,t];s&&a.push(window.PluginConfigManager.get(this._pluginName,s));try{r&&a.push(JSON.parse(r))}catch(t){throw console.error(this.el),Error('The data attribute "data-'.concat(i,'-options" could not be parsed to json: ').concat(t.message))}return e().all(a.filter(t=>t instanceof Object&&!(t instanceof Array)).map(t=>t||{}))}_registerInstance(){window.PluginManager.getPluginInstancesFromElement(this.el).set(this._pluginName,this),window.PluginManager.getPlugin(this._pluginName,!1).get("instances").push(this)}_getPluginName(t){return t||(t=this.constructor.name),t}constructor(t,e={},i=!1){if(!o.isNode(t))throw Error("There is no valid element given.");this.el=t,this.$emitter=new s(this.el),this._pluginName=this._getPluginName(i),this.options=this._mergeOptions(e),this._initialized=!1,this._registerInstance(),this._init()}}var a=i(820),h=i.n(a);class l{get(t,e){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"application/json",n=this._createPreparedRequest("GET",t,i);return this._sendRequest(n,null,e)}post(t,e,i){let n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"application/json";n=this._getContentType(e,n);let o=this._createPreparedRequest("POST",t,n);return this._sendRequest(o,e,i)}delete(t,e,i){let n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"application/json";n=this._getContentType(e,n);let o=this._createPreparedRequest("DELETE",t,n);return this._sendRequest(o,e,i)}patch(t,e,i){let n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"application/json";n=this._getContentType(e,n);let o=this._createPreparedRequest("PATCH",t,n);return this._sendRequest(o,e,i)}abort(){if(this._request)return this._request.abort()}setErrorHandlingInternal(t){this._errorHandlingInternal=t}_registerOnLoaded(t,e){e&&(!0===this._errorHandlingInternal?(t.addEventListener("load",()=>{e(t.responseText,t)}),t.addEventListener("abort",()=>{console.warn("the request to ".concat(t.responseURL," was aborted"))}),t.addEventListener("error",()=>{console.warn("the request to ".concat(t.responseURL," failed with status ").concat(t.status))}),t.addEventListener("timeout",()=>{console.warn("the request to ".concat(t.responseURL," timed out"))})):t.addEventListener("loadend",()=>{e(t.responseText,t)}))}_sendRequest(t,e,i){return this._registerOnLoaded(t,i),t.send(e),t}_getContentType(t,e){return t instanceof FormData&&(e=!1),e}_createPreparedRequest(t,e,i){return this._request=new XMLHttpRequest,this._request.open(t,e),this._request.setRequestHeader("X-Requested-With","XMLHttpRequest"),i&&this._request.setRequestHeader("Content-type",i),this._request}constructor(){this._request=null,this._errorHandlingInternal=!1}}class u extends r{init(){this.mainButton=document.getElementById(this.options.searchBranchButton),this.branchKey=this.mainButton?this.mainButton.dataset.branch:null,this.searchButton=document.getElementById(this.options.buttonId),this.searchInput=document.getElementById(this.options.searchInput),this.searchButton.addEventListener("click",this._fetchBranches.bind(this)),this.searchInput.addEventListener("keypress",this._fetchBranches.bind(this)),this.httpClient=new l,this.branchList=document.getElementById(this.options.branchList),this.branchModalContent=document.getElementById(this.options.branchModalContent),this.branchModalContent.style.display="none",this.mapVisibility=document.getElementById(this.options.mapVisibility).value}_fetchBranches(t){if(("click"===t.type||"keypress"===t.type&&"Enter"===t.key)&&(this.branchModalContent.style.display="none",""!==this.searchInput.value)){let t=document.getElementById(this.options.noBranchDataFound);null!==t&&t.remove(),this._showSpinner(!0);let e="/wunschfiliale/search/"+this.searchInput.value;null!==this.branchKey&&(e+="/"+this.branchKey),this.httpClient.get(e,(t,e)=>{let i=JSON.parse(t);200===e.status?!0==!!this.mapVisibility?this._showMapPoints(i.data):this._showOnlyBranches(i.data):(this.branchModalContent.style.display="none",document.querySelector(this.options.modalBodyClass).insertAdjacentHTML("beforeend","<div id='noBranchDataFound' class='row mt-3 text-center'><h4>".concat(i.message,"</h4></div>"))),this._showSpinner(!1)})}}_showOnlyBranches(t){for(let e of(document.getElementById(this.options.branchModalContent).style.display="flex",this.branchList.innerHTML="",t.forEach(t=>{this._createBranchList(t)}),document.querySelectorAll(".selectDeliveryStation")))e.addEventListener("click",this._changeAddress.bind(this))}_createBranchList(t){let e=JSON.stringify(t),i="";null!==t.openingHourFormatted&&(i="Öffnungszeiten: "+t.openingHourFormatted),this.branchList.innerHTML+='<div class="col-12 mb-2 singleBranch">\n               <div class="branchContent p-3">\n                    <div class="branchName"><b>'.concat(t.firstLineOfAddress,'</b></div>\n                    <div class="branchStreet">').concat(t.address.streetName," ").concat(t.address.streetNumber,'</div>\n                    <div class="branchZipAndPlace">').concat(t.address.postalCode," ").concat(t.address.city,'</div>\n                    <div class="branchOpenHours">').concat(i,'</div>\n                    <input type="hidden" name="branchKey').concat(t.branchKey,"\" value='").concat(e,'\'>\n              </div><button type="submit" class="pl-3 selectDeliveryStation checkout-link" data-branch-id="').concat(t.branchKey,'">').concat(window.wunschfiliale.chooseStation,"</button></div>")}_showMapPoints(t){this._clearMapContainer(),this._createMap();let e={};for(let i of(document.getElementById(this.options.branchModalContent).style.display="flex",this.branchList.innerHTML="",t.forEach(t=>{this._createBranchList(t),e={lon:t.address.coordinates.longitudeWGS84,lat:t.address.coordinates.latitudeWGS84};let i=this._generatePopupMarkup(t),n=h().marker(e);n.bindPopup(i).openPopup(),n.addTo(this.map)}),document.querySelectorAll(".selectDeliveryStation")))i.addEventListener("click",this._changeAddress.bind(this));this.map.setView(e,13),this.invalidateSizeInterval&&clearInterval(this.invalidateSizeInterval),this.invalidateSizeInterval=setInterval(this._invalidateMapSize.bind(this),500)}_invalidateMapSize(){this.map.invalidateSize()}_clearMapContainer(){this.map&&this.map.remove()}_generatePopupMarkup(t){let e='\n            <div class="branchName"><b>'.concat(t.firstLineOfAddress,'</b></div>\n            <div class="branchStreet"> ').concat(t.address.streetName," ").concat(t.address.streetNumber,' </div>\n            <div class="branchZipAndPlace"> ').concat(t.address.postalCode," ").concat(t.address.city," </div>");return null!==t.openingHourFormatted&&(e+='<div class="branchOpenHours"> '.concat(window.wunschfiliale.openingHours,": ").concat(t.openingHourFormatted," </div>")),e+='<button type="submit" onclick="triggerBranchButton(this)" class="mt-2 selectDeliveryStationIcon checkout-link" data-branch-button="'.concat(t.branchKey,'">').concat(window.wunschfiliale.chooseStation,"</button>")}_createMap(){this.map=h().map("map"),h().tileLayer("https://{s}.tile.openstreetmap.de/{z}/{x}/{y}.png",{attribution:'Map <a href="https://memomaps.de/">memomaps.de</a> <a href="http://creativecommons.org/licenses/by-sa/2.0/">CC-BY-SA</a>, map data &copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'}).addTo(this.map)}_showSpinner(t){let e=document.querySelector(".spinner-container");t?e.style.display="flex":e.style.display="none"}_changeAddress(t){let e=t.target.dataset.branchId,i=document.querySelector("[name=branchKey"+e+"]"),n=document.querySelector("[name=customerId]").value;if(null!==i){let t=JSON.parse(i.value);this.httpClient.post("/wunschfiliale/pickupStation",JSON.stringify({branchData:t,customerId:n}),t=>{window.location.reload()})}}}u.options={buttonId:"choosePostfiliale",searchInput:"searchBranchInput",map:"map",branchList:"branchList",branchModalContent:"branchModalContent",noBranchDataFound:"noBranchDataFound",modalBodyClass:".modal-body",mapVisibility:"mapVisibility",searchBranchButton:"searchBranchButton"},window.PluginManager.register("Wunschfiliale",u,"[data-post-wunschfiliale]")})()})();