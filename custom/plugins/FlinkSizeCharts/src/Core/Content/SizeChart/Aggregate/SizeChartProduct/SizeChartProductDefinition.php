<?php declare(strict_types=1);

namespace Flink\SizeCharts\Core\Content\SizeChart\Aggregate\SizeChartProduct;

use Flink\SizeCharts\Core\Content\SizeChart\Aggregate\SizeChartProductTranslation\SizeChartProductTranslationDefinition;
use Flink\SizeCharts\Core\Content\SizeChart\SizeChartDefinition;
use Shopware\Core\Content\Product\ProductDefinition;
use Shopware\Core\Framework\DataAbstractionLayer\EntityDefinition;
use Shopware\Core\Framework\DataAbstractionLayer\Field\FkField;
use Shopware\Core\Framework\DataAbstractionLayer\Field\Flag\Inherited;
use Shopware\Core\Framework\DataAbstractionLayer\Field\Flag\PrimaryKey;
use Shopware\Core\Framework\DataAbstractionLayer\Field\Flag\Required;
use Shopware\Core\Framework\DataAbstractionLayer\Field\IdField;
use Shopware\Core\Framework\DataAbstractionLayer\Field\ManyToOneAssociationField;
use Shopware\Core\Framework\DataAbstractionLayer\Field\OneToOneAssociationField;
use Shopware\Core\Framework\DataAbstractionLayer\Field\ReferenceVersionField;
use Shopware\Core\Framework\DataAbstractionLayer\Field\TranslatedField;
use Shopware\Core\Framework\DataAbstractionLayer\Field\TranslationsAssociationField;
use Shopware\Core\Framework\DataAbstractionLayer\FieldCollection;

class SizeChartProductDefinition extends EntityDefinition
{
    public const ENTITY_NAME = 'flink_size_chart_product';

    public function getEntityName(): string
    {
        return self::ENTITY_NAME;
    }

    public function getCollectionClass(): string
    {
        return SizeChartProductCollection::class;
    }

    public function getEntityClass(): string
    {
        return SizeChartProductEntity::class;
    }

    protected function defineFields(): FieldCollection
    {
        return new FieldCollection([
            (new IdField('id', 'id'))->addFlags(new PrimaryKey(), new Required()),
            (new FkField('flink_size_chart_id', 'sizeChartId', SizeChartDefinition::class))->addFlags(new Required()),
            (new ManyToOneAssociationField('sizeChart', 'flink_size_chart_id', SizeChartDefinition::class)),
            (new FkField('product_id', 'productId', ProductDefinition::class))->addFlags(new Required()),
            (new OneToOneAssociationField('product', 'product_id', 'id', ProductDefinition::class)),
            (new ReferenceVersionField(ProductDefinition::class))->addFlags(new Required()),

            new TranslatedField('chartsOverride'),

            (new TranslationsAssociationField(SizeChartProductTranslationDefinition::class, 'flink_size_chart_product_id'))->addFlags(new Inherited(), new Required()),
        ]);
    }
}