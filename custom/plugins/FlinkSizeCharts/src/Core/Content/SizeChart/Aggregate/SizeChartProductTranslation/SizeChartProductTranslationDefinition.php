<?php declare(strict_types=1);

namespace Flink\SizeCharts\Core\Content\SizeChart\Aggregate\SizeChartProductTranslation;

use Flink\SizeCharts\Core\Content\SizeChart\Aggregate\SizeChartProduct\SizeChartProductDefinition;
use Shopware\Core\Framework\DataAbstractionLayer\EntityTranslationDefinition;
use Shopware\Core\Framework\DataAbstractionLayer\Field\JsonField;
use Shopware\Core\Framework\DataAbstractionLayer\FieldCollection;

class SizeChartProductTranslationDefinition extends EntityTranslationDefinition
{
    public const ENTITY_NAME = 'flink_size_chart_product_translation';

    public function getEntityName(): string
    {
        return self::ENTITY_NAME;
    }

    public function isVersionAware(): bool
    {
        return false;
    }

    public function getCollectionClass(): string
    {
        return SizeChartProductTranslationCollection::class;
    }

    public function getEntityClass(): string
    {
        return SizeChartProductTranslationEntity::class;
    }

    protected function getParentDefinitionClass(): string
    {
        return SizeChartProductDefinition::class;
    }

    protected function defineFields(): FieldCollection
    {
        return new FieldCollection([
            new JsonField('charts_override', 'chartsOverride'),
        ]);
    }
}
