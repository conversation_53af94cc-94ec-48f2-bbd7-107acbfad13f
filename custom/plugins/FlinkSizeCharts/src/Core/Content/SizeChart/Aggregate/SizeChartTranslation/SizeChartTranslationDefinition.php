<?php declare(strict_types=1);

namespace Flink\SizeCharts\Core\Content\SizeChart\Aggregate\SizeChartTranslation;

use Flink\SizeCharts\Core\Content\SizeChart\SizeChartDefinition;
use Shopware\Core\Content\Media\MediaDefinition;
use Shopware\Core\Framework\DataAbstractionLayer\EntityTranslationDefinition;
use Shopware\Core\Framework\DataAbstractionLayer\Field\FkField;
use Shopware\Core\Framework\DataAbstractionLayer\Field\Flag\AllowHtml;
use Shopware\Core\Framework\DataAbstractionLayer\Field\JsonField;
use Shopware\Core\Framework\DataAbstractionLayer\Field\LongTextField;
use Shopware\Core\Framework\DataAbstractionLayer\Field\ManyToOneAssociationField;
use Shopware\Core\Framework\DataAbstractionLayer\Field\StringField;
use Shopware\Core\Framework\DataAbstractionLayer\FieldCollection;

class SizeChartTranslationDefinition extends EntityTranslationDefinition
{
    public const ENTITY_NAME = 'flink_size_chart_translation';

    public function getEntityName(): string
    {
        return self::ENTITY_NAME;
    }

    public function isVersionAware(): bool
    {
        return false;
    }

    public function getCollectionClass(): string
    {
        return SizeChartTranslationCollection::class;
    }

    public function getEntityClass(): string
    {
        return SizeChartTranslationEntity::class;
    }

    protected function getParentDefinitionClass(): string
    {
        return SizeChartDefinition::class;
    }

    protected function defineFields(): FieldCollection
    {
        return new FieldCollection([
            new StringField('name', 'name'),
            new StringField('display_name', 'displayName'),
            new JsonField('charts', 'charts'),
            (new LongTextField('textBefore', 'textBefore'))->addFlags(new AllowHtml()),
            (new LongTextField('textAfter', 'textAfter'))->addFlags(new AllowHtml()),

            new FkField('media_before', 'mediaBeforeId', MediaDefinition::class),
            new FkField('media_after', 'mediaAfterId', MediaDefinition::class),

            new ManyToOneAssociationField('mediaBefore', 'media_before', MediaDefinition::class, 'id'),
            new ManyToOneAssociationField('mediaAfter', 'media_after', MediaDefinition::class, 'id'),
            // new CustomFields(),
        ]);
    }
}
