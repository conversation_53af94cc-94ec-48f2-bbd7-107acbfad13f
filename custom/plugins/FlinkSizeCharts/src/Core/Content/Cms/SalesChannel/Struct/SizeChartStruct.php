<?php declare(strict_types=1);

namespace Flink\SizeCharts\Core\Content\Cms\SalesChannel\Struct;

use Flink\SizeCharts\Core\Content\SizeChart\SizeChartEntity;
use Shopware\Core\Framework\Struct\Struct;

class SizeChartStruct extends Struct
{
    /**
     * @var SizeChartEntity|null
     */
    protected $sizeChart;

    /**
     * @var string|null
     */
    protected $sizeChartId;

    public function getSizeChart(): ?SizeChartEntity
    {
        return $this->sizeChart;
    }

    public function setSizeChart(SizeChartEntity $sizeChart): void
    {
        $this->sizeChart = $sizeChart;
    }

    public function getSizeChartId(): ?string
    {
        return $this->sizeChartId;
    }

    public function setSizeChartId(string $sizeChartId): void
    {
        $this->sizeChartId = $sizeChartId;
    }

    public function getApiAlias(): string
    {
        return 'cms_size_chart';
    }
}
