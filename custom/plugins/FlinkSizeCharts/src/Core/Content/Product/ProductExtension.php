<?php declare(strict_types=1);

namespace Flink\SizeCharts\Core\Content\Product;

use Flink\SizeCharts\Core\Content\SizeChart\Aggregate\SizeChartProduct\SizeChartProductDefinition;
use Shopware\Core\Content\Product\ProductDefinition;
use Shopware\Core\Framework\DataAbstractionLayer\Field\OneToOneAssociationField;
use Shopware\Core\Framework\DataAbstractionLayer\FieldCollection;
use Shopware\Core\Framework\DataAbstractionLayer\EntityExtension;

class ProductExtension extends EntityExtension
{
    public function getDefinitionClass(): string
    {
        return ProductDefinition::class;
    }

    public function extendFields(FieldCollection $collection): void
    {
        // $collection->add(
        //     (new OneToOneAssociationField(
        //         'flinkSizeChartProduct',
        //         'id',
        //         'product_id',
        //         SizeChartProductDefinition::class,
        //     ))
        // );
    }
}
