<?php declare(strict_types=1);

namespace Flink\SizeCharts\Core;

use Shopware\Core\Framework\Context;
use Shopware\Core\Framework\DataAbstractionLayer\EntityRepositoryInterface;
use Shopware\Core\Framework\DataAbstractionLayer\Exception\InconsistentCriteriaIdsException;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Criteria;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Filter\ContainsFilter;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Filter\EqualsAnyFilter;
use Shopware\Core\Framework\DataAbstractionLayer\Search\IdSearchResult;
use Symfony\Component\DependencyInjection\ContainerInterface;

class CustomFieldWizard
{

    /**
     * @var array|null
     */
    protected $customFieldDefinitions = null;

    /**
     * @var EntityRepositoryInterface|null
     */
    protected $customFieldRepository = null;

    /**
     * @var EntityRepositoryInterface|null
     */
    protected $customFieldSetRepository = null;

    /**
     * @var ContainerInterface|null
     */
    protected $container = null;

    /**
     * @var Context|null
     */
    protected $context = null;

    /**
     * @var IdSearchResult|null
     */
    protected $installedFieldSetIds = null;

    /**
     * @var IdSearchResult|null
     */
    protected $installedFieldIds = null;

    /**
     * CustomFieldWizard constructor.
     * @param array $customFieldDefinitions
     * @param ContainerInterface $container
     * @param Context $context
     * @throws InconsistentCriteriaIdsException
     */
    public function __construct(array $customFieldDefinitions, ContainerInterface $container, Context $context)
    {
        $this->customFieldDefinitions = $customFieldDefinitions;
        $this->container = $container;
        $this->context = $context;
        $this->customFieldRepository = $this->container->get('custom_field.repository');
        $this->customFieldSetRepository = $this->container->get('custom_field_set.repository');

        $this->loadCustomFieldSetIds();
        $this->loadCustomFieldIds();
    }

    /**
     * Installs all added fields and field sets
     * @return void
     */
    public function install(): void
    {
        if ($this->hasCustomFields() || $this->hasCustomFieldSets()) {
            $this->uninstall();
        }

        $this->customFieldSetRepository->create($this->customFieldDefinitions, $this->context);
    }

    /**
     * Removes all installed fields and field sets that use the common identifier.
     * @return void
     */
    public function uninstall(): void
    {
        $this->uninstallFields();
        $this->uninstallFieldSets();
    }

    /**
     * Get the search criteria for searching for fields and field sets based on the common identifier
     * @return Criteria
     * @throws InconsistentCriteriaIdsException
     */
    private function getSearchCriteriaFields(): Criteria
    {
        $criteria = new Criteria();
        $fieldNames = [];
        foreach ($this->customFieldDefinitions as $set) {
            $fieldNames = array_merge($fieldNames, array_column($set['customFields'], 'name'));
        }
        $criteria->addFilter(new EqualsAnyFilter('name', $fieldNames));
        return $criteria;
    }

    /**
     * Get the search criteria for searching for fields and field sets based on the common identifier
     * @return Criteria
     * @throws InconsistentCriteriaIdsException
     */
    private function getSearchCriteriaSets(): Criteria
    {
        $criteria = new Criteria();
        $setNames = array_column($this->customFieldDefinitions, 'name');
        $criteria->addFilter(new EqualsAnyFilter('name', $setNames));
        return $criteria;
    }

    /**
     * Loads all installed fields that use the common identifier.
     * @throws InconsistentCriteriaIdsException
     */
    private function loadCustomFieldIds(): void
    {
        $this->installedFieldIds = $this->customFieldRepository->searchIds($this->getSearchCriteriaFields(), $this->context);
    }

    /**
     * Loads all installed field sets that use the common identifier.
     * @throws InconsistentCriteriaIdsException
     */
    private function loadCustomFieldSetIds(): void
    {
        $this->installedFieldSetIds = $this->customFieldSetRepository->searchIds($this->getSearchCriteriaSets(), $this->context);
    }

    /**
     * Checks if there are already any fields installed.
     * @return bool
     */
    private function hasCustomFields(): bool
    {
        return $this->installedFieldIds && $this->installedFieldIds->getTotal() !== 0;
    }

    /**
     * Checks if there are already any field sets installed.
     * @return bool
     */
    private function hasCustomFieldSets(): bool
    {
        return $this->installedFieldIds && $this->installedFieldIds->getTotal() !== 0;
    }

    /**
     * Removes all installed fields that use the common identifier.
     * @return void
     */
    private function uninstallFields(): void
    {
        if ($this->hasCustomFields()) {

            $ids = array_map(function ($id) {
                return ['id' => $id];
            }, $this->installedFieldIds->getIds());

            $this->customFieldRepository->delete($ids, $this->context);

        }
    }

    /**
     * Removes all installed field sets that use the common identifier.
     * @return void
     */
    private function uninstallFieldSets(): void
    {
        if ($this->hasCustomFieldSets()) {

            $ids = array_map(function ($id) {
                return ['id' => $id];
            }, $this->installedFieldSetIds->getIds());

            $this->customFieldSetRepository->delete($ids, $this->context);

        }
    }
}
