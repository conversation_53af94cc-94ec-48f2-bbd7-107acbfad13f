import './preview';
import './component';
import './config';

Shopware.Service('cmsService').registerCmsElement({
    name: 'flink-size-chart',
    label: 'flink-size-chart.cms.elements.flinkSizeChart.label',
    component: 'sw-cms-el-flink-size-chart',
    configComponent: 'sw-cms-el-config-flink-size-chart',
    previewComponent: 'sw-cms-el-preview-flink-size-chart',

    defaultConfig: {
        sizeChart: {
            source: 'static',
            value: null,
            required: true,
            entity: {
                name: 'flink_size_chart',
                criteria: new Shopware.Data.Criteria()
            }
        },
    },

    defaultData: {
        sizeChart: {}
    }

});
