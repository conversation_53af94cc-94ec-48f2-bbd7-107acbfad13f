import template from './sw-cms-el-flink-size-chart.html.twig';
import './sw-cms-el-flink-size-chart.scss';

const { Component, Mixin } = Shopware;

Component.register('sw-cms-el-flink-size-chart', {
    template,

    inject: [
        'repositoryFactory'
    ],

    mixins: [
        Mixin.getByName('cms-element')
    ],

    data() {
        return {
        }
    },

    computed: {

        sizeChart() {
            if (!this.element.data || !this.element.data.sizeChart) {
                return {};
            }

            return this.element.data.sizeChart;
        },

    },

    created() {
        this.createdComponent();
    },

    methods: {
        createdComponent() {
            this.initElementConfig('flink-size-chart');
        }
    }

});
