import template from './sw-cms-el-config-flink-size-chart.html.twig';

const { Component, Mixin } = Shopware;
const { Criteria } = Shopware.Data;

Component.register('sw-cms-el-config-flink-size-chart', {
    template,

    inject: [
        'repositoryFactory'
    ],

    mixins: [
        Mixin.getByName('cms-element')
    ],

    data() {
        return {
        };
    },

    computed: {

        sizeChartRepository() {
            return this.repositoryFactory.create('flink_size_chart');
        },

        sizeChartSelectContext() {
            const context = Object.assign({}, Shopware.Context.api);
            context.inheritance = true;

            return context;
        },

    },

    created() {
        this.createdComponent();
    },

    methods: {
        createdComponent() {
            this.initElementConfig('flink-size-chart');
        },

        onSizeChartChange(sizeChartId) {
            if (!sizeChartId) {
                this.element.config.sizeChart.value = null;
                this.$set(this.element.data, 'sizeChartId', null);
                this.$set(this.element.data, 'sizeChart', null);
            } else {
                const criteria = new Criteria();
                this.sizeChartRepository.get(sizeChartId, this.sizeChartSelectContext, criteria).then((sizeChart) => {
                    this.element.config.sizeChart.value = sizeChartId;
                    this.$set(this.element.data, 'sizeChartId', sizeChartId);
                    this.$set(this.element.data, 'sizeChart', sizeChart);
                });
            }
            this.$emit('element-update', this.element);
        }
    }
});
