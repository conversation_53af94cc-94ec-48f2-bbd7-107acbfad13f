{% block sw_product_detail_specifications_measures_packaging %}
    {% parent() %}

    <sw-card
            class="sw-product-detail-specification__flink-size-chart"
            :title="$t('flink-size-chart.properties.sizeChart')"
            :isLoading="isLoading">
        {% block sw_product_detail_specifications_flink_size_chart_content %}
            <sw-inherit-wrapper v-if="!isLoading && product.customFields != null"
                                v-model="product.customFields.flinkSizeChart"
                                :inheritedValue="parentProduct.customFields ? parentProduct.customFields.flinkSizeChart : null"
                                :hasParent="!!parentProduct.id"
                                :label="$t('flink-size-chart.properties.sizeChart')"
                                @inheritance-remove="saveProduct"
                                @inheritance-restore="saveProduct">
                <template #content="{ currentValue, isInherited, updateCurrentValue }">
                    <sw-entity-single-select
                            v-model="product.customFields.flinkSizeChart"
                            entity="flink_size_chart"
                            :disabled="isInherited"
                            :key="isInherited">
                    </sw-entity-single-select>
                </template>
            </sw-inherit-wrapper>

            <div class="sw-product-detail-specification__flink-size-chart-buttons">
                <sw-button @click="onOpenOverrideSizeChartModal">
                    {{ $tc('flink-size-chart.extension.product.customizeSizeChart') }}
                </sw-button>

                <span v-if="flinkSizeChartProduct && !flinkSizeChartProduct._isNew">
                    {{ $tc('flink-size-chart.extension.product.sizeChartCustomized') }}
                </span>

                <sw-button @click="resetFlinkSizeChartProduct(null)"
                           variant="danger"
                           :disabled="!flinkSizeChartProduct || flinkSizeChartProduct._isNew"
                           size="small">
                    {{ $tc('flink-size-chart.extension.product.restoreTemplate') }}
                </sw-button>
            </div>

        {% endblock %}
        {% block sw_product_detail_specifications_flink_size_chart_override_modal %}
            {% block sw_product_detail_specifications_flink_size_chart_override_%}
                <sw-modal
                        v-if="overrideSizeChartModalOpen"
                        class="sw-product-detail-specifications__override-size-chart-modal"
                        title="Größentabelle anpassen"
                        @modal-close="onCloseOverrideSizeChartModal">

                    {% block sw_product_detail_specifications_flink_size_chart_override_body %}
                        <template>
                            {% block sw_product_detail_specifications_flink_size_chart_override_component %}
                                <flink-size-chart-component v-if="flinkSizeChartProduct && flinkSizeChartProduct.chartsOverride" :item="flinkSizeChartProduct" :fixed="true"></flink-size-chart-component>
                            {% endblock %}
                        </template>
                    {% endblock %}

                    {% block sw_product_detail_specifications_flink_size_chart_override_footer %}
                        <template #modal-footer>
                            {% block sw_product_detail_specifications_flink_size_chart_override_footer_close_button %}
                                <sw-button size="small" @click="onCloseOverrideSizeChartModal">
                                    {{ $tc('global.sw-modal.labelClose') }}
                                </sw-button>

                                <sw-button size="small" variant="primary" @click="onSaveOverrideSizeChartModal">
                                    {{ $tc('global.default.save') }}
                                </sw-button>
                            {% endblock %}
                        </template>
                    {% endblock %}
                </sw-modal>
            {% endblock %}
        {% endblock %}
    </sw-card>
{% endblock %}