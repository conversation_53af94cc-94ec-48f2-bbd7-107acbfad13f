import template from './sw-product-detail-specifications.html.twig';
import './sw-product-detail-specifications.scss';

const { Component } = Shopware;
const {Criteria} = Shopware.Data;

Component.override('sw-product-detail-specifications', {
    template,

    inject: ['repositoryFactory'],

    data() {
        return {
            flinkSizeChart: null,
            flinkSizeChartProduct: null,
            overrideSizeChartModalOpen: false,
            editingSizeCharts: []
        }
    },

    computed: {
        productRepository() {
            return this.repositoryFactory.create('product');
        },
        flinkSizeChartRepository() {
            return this.repositoryFactory.create('flink_size_chart');
        },
        flinkSizeChartProductRepository() {
            return this.repositoryFactory.create('flink_size_chart_product');
        },
        flinkSizeChartId() {
            return this.product?.customFields?.flinkSizeChart;
        }
    },

    created() {
        this.createdComponent();
    },

    watch: {
        flinkSizeChartId(val, oldVal){
            this.flinkSizeChart = null;
            if (this.flinkSizeChartProduct) {
                this.resetFlinkSizeChartProduct(oldVal);
            }
            this.loadFlinkSizeChart();
        }
    },

    methods: {
        createdComponent() {
            this.initializeCustomFields();
            this.loadFlinkSizeChart();
        },

        initializeCustomFields() {
            if (!this.product.customFields) {
                this.$set(this.product, 'customFields', {});
            }
        },

        saveProduct() {
            if (this.product) {
                this.productRepository.save(this.product, Shopware.Context.api);
            }
        },

        loadFlinkSizeChart() {
            if (this.flinkSizeChartId) {
                this.flinkSizeChartRepository
                    .get(this.flinkSizeChartId, Shopware.Context.api, new Criteria())
                    .then((entity) => {
                        this.flinkSizeChart = entity;
                        this.loadFlinkSizeChartProduct();
                    })
            }
        },

        loadFlinkSizeChartProduct() {
            if (this.flinkSizeChartId) {
                // Search for assignment in database
                let criteria = new Criteria();
                criteria.addFilter(Criteria.equals('sizeChartId', this.flinkSizeChartId))
                criteria.addFilter(Criteria.equals('productId', this.product.id));
                this.flinkSizeChartProductRepository
                    .search(criteria, Shopware.Context.api)
                    .then((result) => {
                        if (result.total) {
                            this.product.customFields.flinkSizeChartProduct = result.first().id;
                            this.flinkSizeChartProduct = result.first();
                        } else {
                            this.$set(this, 'flinkSizeChartProduct', this.flinkSizeChartProductRepository.create(Shopware.Context.api));
                            this.flinkSizeChartProduct.sizeChartId = this.flinkSizeChartId;
                            this.flinkSizeChartProduct.productId = this.product.id;
                            this.flinkSizeChartProduct.chartsOverride = null;
                            if (this.flinkSizeChart) {
                                this.flinkSizeChartProduct.chartsOverride = JSON.parse(JSON.stringify(this.flinkSizeChart.charts));
                            }
                        }
                    });
            }
        },

        onOpenOverrideSizeChartModal() {
            this.overrideSizeChartModalOpen = true;
        },

        onCloseOverrideSizeChartModal() {
            this.overrideSizeChartModalOpen = false;
        },

        onSaveOverrideSizeChartModal() {
            if (this.flinkSizeChartProduct) {
                this.flinkSizeChartProductRepository
                    .save(this.flinkSizeChartProduct, Shopware.Context.api)
                    .then(() => {
                        this.loadFlinkSizeChartProduct();
                    })
            }
            this.overrideSizeChartModalOpen = false;
        },

        resetFlinkSizeChartProduct(oldSizeChartId = null) {
            if (this.flinkSizeChartProduct) {
                // Search matching entries
                let criteria = new Criteria();
                criteria.addFilter(Criteria.equals('productId', this.product.id));
                console.log(oldSizeChartId, this.flinkSizeChartId);
                criteria.addFilter(Criteria.equals('sizeChartId', oldSizeChartId || this.flinkSizeChartId));
                this.flinkSizeChartProductRepository
                    .searchIds(criteria, Shopware.Context.api)
                    .then((result) => {
                        console.log(result.data);
                        if (result.data) {
                            this.flinkSizeChartProductRepository
                                .syncDeleted(result.data, Shopware.Context.api)
                                .finally(() => {
                                    this.clearFlinkSizeChartProduct();
                                });
                        } else {
                            this.clearFlinkSizeChartProduct();
                        }
                    });
            } else {
                this.clearFlinkSizeChartProduct();
            }
        },

        clearFlinkSizeChartProduct() {
            this.flinkSizeChartProduct = null;
            this.product.customFields.flinkSizeChartProduct = null;
            this.loadFlinkSizeChartProduct();
        }

    }
});
