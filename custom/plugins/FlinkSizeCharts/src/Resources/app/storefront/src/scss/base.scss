.flink-size-chart__expanded-tab-title {
    background: $sw-color-brand-primary;
    color: #fff;
    font-weight: bold;
    padding: 15px;

    &--sm {
        padding: 0.3rem;
    }
}

.flink-size-chart-container {
    table {
        .btn-sm {
            padding: 4px 8px;
            line-height: 1;
        }
        &.table-first-col-sticky {
            td, th {
                &:first-child {
                    position: sticky;
                    left: 0;
                    @if not $table-bg or $table-bg == transparent {
                        background: #fff;
                    } @else {
                        background: $table-bg;
                    }

                    &:before {
                        content: '';
                        position: absolute;
                        top: 0;
                        left: 0;
                        width: 100%;
                        height: 100%;
                        z-index: -1;
                    }
                }
            }
            &.table-striped {
                tbody tr:nth-of-type(#{$table-striped-order}) {
                    td, th {
                        &:first-child {
                            &:before {
                                background-color: $table-accent-bg;
                            }
                        }
                    }
                }
            }

            &.table-dark {
                td, th {
                    &:first-child {
                        background: $dark;
                    }
                }
                &.table-striped {
                    tbody tr:nth-of-type(#{$table-striped-order}) {
                        td, th {
                            &:first-child {
                                &:before {
                                    background-color: $dark;
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

.product-detail-configurator-group-title {
    .flink-size-chart-button-container {
        float: right;
        font-weight: normal;
    }
    .flink-size-chart-button-container {
        &, p {
            display: inline;
        }
    }
}