{% block flink_size_chart %}

    <style>
        {% if flinkSizeChart.tableHeadBackground or flinkSizeChart.tableHeadColor %}
        .flink-size-chart-container thead th {
        {% if flinkSizeChart.tableHeadBackground  %}
            background-color: {{ flinkSizeChart.tableHeadBackground }} !important;
        {% endif %}
        {% if flinkSizeChart.tableHeadColor  %}
            color: {{ flinkSizeChart.tableHeadColor }} !important;
        {% endif %}
        }
        {% endif %}

        {% if flinkSizeChart.expandedTabTitleBackground or flinkSizeChart.expandedTabTitleColor %}
        .flink-size-chart-container .flink-size-chart__expanded-tab-title {
        {% if flinkSizeChart.expandedTabTitleBackground  %}
            background-color: {{ flinkSizeChart.expandedTabTitleBackground }} !important;
        {% endif %}
        {% if flinkSizeChart.expandedTabTitleColor  %}
            color: {{ flinkSizeChart.expandedTabTitleColor }} !important;
        {% endif %}
        }
        {% endif %}
    </style>

    <div class="flink-size-chart-container">

    {% set flinkSizeChartTabs = "" %}
    {% set flinkSizeChartTabPanes = "" %}

    {% set charts = flinkSizeChart.translated.charts %}
    {% if flinkSizeChartProduct %}
        {% set charts = flinkSizeChartProduct.translated.chartsOverride %}
    {% endif %}

    {% for tabIdx, chart in charts %}

        {# Gather tabs #}
        {% if not flinkSizeChart.expandedTabs and charts|length > 1 %}
            {% set flinkSizeChartTabs %}{% apply spaceless %}
                {% if flinkSizeChartTabs %}{{ flinkSizeChartTabs }}{% endif %}
                {% if chart.tab %}
                    <li class="nav-item">
                        <a class="nav-link{% if loop.first %} active{% endif %}" href="#tab-{{ tabIdx }}" data-bs-toggle="tab">{{ chart.tab }}</a>
                    </li>
                {% endif %}
            {% endapply %}{% endset %}
        {% endif %}

        {# Gather tab contents #}
        {% set flinkSizeChartTabPanes %}{% apply spaceless %}
        {% if flinkSizeChartTabPanes %}{{ flinkSizeChartTabPanes }}{% endif %}

        {% if not flinkSizeChart.expandedTabs %}
        <div class="tab-pane fade{% if loop.first %} show active{% endif %}" id="tab-{{ tabIdx }}" role="tabpanel" aria-labelledby="tab-{{ tabIdx }}-tab">
        {% else %}
        <div class="mb-2">
            {% if chart.tab and charts|length > 1 %}
                <div class="flink-size-chart__expanded-tab-title{% if 'table-sm' in flinkSizeChart.tableClasses %} flink-size-chart__expanded-tab-title--sm{% endif %}">
                    {{ chart.tab }}
                </div>
            {% endif %}
            {% endif %}
            <div class="table-responsive">
                <table class="table {{ flinkSizeChart.tableClasses | join(' ') }}">
                    {% set noHead = true %}
                    {% for columnIdx, column in chart.cols %}
                        {% if column %}
                            {% set noHead = false %}
                        {% endif %}
                    {% endfor %}

                    {% if not noHead %}
                        <thead class="{{ flinkSizeChart.theadClasses | join(' ') }}">
                        <tr>
                            {% for columnIdx, column in chart.cols %}
                                <th>{{ column }}</th>
                            {% endfor %}
                        </tr>
                        </thead>
                    {% endif %}

                    <tbody>
                    {% for rowIdx, row in chart.rows %}
                        <tr>
                            {% for columnIdx, value in row %}
                                <td>
                                    {{ value | raw }}
                                </td>
                            {% endfor %}
                        </tr>
                    {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>

        {% endapply %}{% endset %}

    {% endfor %}

    {% set mediaIds = [] %}

    {% if flinkSizeChart.translated.mediaAfterId %}
        {% set mediaIds = mediaIds|merge([flinkSizeChart.translated.mediaAfterId]) %}
    {% endif %}

    {% if flinkSizeChart.translated.mediaBeforeId %}
        {% set mediaIds = mediaIds|merge([flinkSizeChart.translated.mediaBeforeId]) %}
    {% endif %}

    {% if mediaIds %}
        {% set mediaCollection = searchMedia(mediaIds, context.context) %}
    {% endif %}

    {% if flinkSizeChart.translated.textBefore %}
        <div class="mb-2">
            {{ flinkSizeChart.translated.textBefore | raw }}
        </div>
    {% elseif config('FlinkSizeCharts.config.showSnippetTextBefore') %}
        <div class="mb-2">
            {{ 'flink-size-chart.textBeforeTable' | trans | raw }}
        </div>
    {% endif %}

    {% if flinkSizeChart.translated.mediaBeforeId and mediaCollection %}
        {% set mediaBefore = mediaCollection.get(flinkSizeChart.translated.mediaBeforeId) %}
        {% if mediaBefore %}
            <div class="mb-2">
                {% sw_thumbnails 'flink-size-chart-media' with {
                    media: mediaBefore,
                    attributes: {
                        'class': 'img-fluid flink-size-chart-media',
                        'alt': mediaBefore.alt,
                        'title': mediaBefore.title
                    }
                } %}
            </div>
        {% endif %}
    {% endif %}

    <ul class="nav nav-pills mb-2">
        {{ flinkSizeChartTabs }}
    </ul>

    <div class="tab-content">
        {{ flinkSizeChartTabPanes }}
    </div>

    {% if flinkSizeChart.translated.textAfter %}
        <div>
            {{ flinkSizeChart.translated.textAfter | raw }}
        </div>
    {% elseif config('FlinkSizeCharts.config.showSnippetTextAfter') %}
        <div>
            {{ 'flink-size-chart.textAfterTable' | trans | raw }}
        </div>
    {% endif %}

    {% if flinkSizeChart.translated.mediaAfterId and mediaCollection %}
        {% set mediaAfter = mediaCollection.get(flinkSizeChart.translated.mediaAfterId) %}
        {% if mediaAfter %}
            <div class="mb-2">
                {% sw_thumbnails 'flink-size-chart-media' with {
                    media: mediaAfter,
                    attributes: {
                        'class': 'img-fluid flink-size-chart-media',
                        'alt': mediaAfter.alt,
                        'title': mediaAfter.title
                    }
                } %}
            </div>
        {% endif %}
    {% endif %}

    </div>

{% endblock %}