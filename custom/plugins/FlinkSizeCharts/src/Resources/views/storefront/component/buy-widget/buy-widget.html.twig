{% sw_extends '@Storefront/storefront/component/buy-widget/buy-widget.html.twig' %}

{% block buy_widget_price %}
    {% if config('FlinkSizeCharts.config.modalButtonPosition') == 'before__page_product_detail_price' %}
        {% sw_include '@FlinkSizeChart/flink/component/size-chart-button-position.html.twig' %}
    {% endif %}

    {{ parent() }}

    {% if config('FlinkSizeCharts.config.modalButtonPosition') == 'after__page_product_detail_price' %}
        {% sw_include '@FlinkSizeChart/flink/component/size-chart-button-position.html.twig' %}
    {% endif %}
{% endblock %}

{% block buy_widget_tax %}
    {% if config('FlinkSizeCharts.config.modalButtonPosition') == 'before__page_product_detail_tax' %}
        {% sw_include '@FlinkSizeChart/flink/component/size-chart-button-position.html.twig' %}
    {% endif %}

    {{ parent() }}

    {% if not config('FlinkSizeCharts.config.modalButtonPosition') or not config('FlinkSizeCharts.config.modalButtonPosition') or config('FlinkSizeCharts.config.modalButtonPosition') == 'after__page_product_detail_tax' %}
        {% sw_include '@FlinkSizeChart/flink/component/size-chart-button-position.html.twig' %}
    {% endif %}
{% endblock %}

{% block buy_widget_delivery_informations %}
    {% if config('FlinkSizeCharts.config.modalButtonPosition') == 'before__page_product_detail_delivery_informations' %}
        {% sw_include '@FlinkSizeChart/flink/component/size-chart-button-position.html.twig' %}
    {% endif %}

    {{ parent() }}

    {% if config('FlinkSizeCharts.config.modalButtonPosition') == 'after__page_product_detail_delivery_informations' %}
        {% sw_include '@FlinkSizeChart/flink/component/size-chart-button-position.html.twig' %}
    {% endif %}
{% endblock %}

{% block buy_widget_configurator_include %}
    {% if config('FlinkSizeCharts.config.modalButtonPosition') == 'before__page_product_detail_configurator_include' %}
        {% sw_include '@FlinkSizeChart/flink/component/size-chart-button-position.html.twig' %}
    {% endif %}

    {{ parent() }}

    {% if config('FlinkSizeCharts.config.modalButtonPosition') == 'after__page_product_detail_configurator_include' %}
        {% sw_include '@FlinkSizeChart/flink/component/size-chart-button-position.html.twig' %}
    {% endif %}
{% endblock %}

{% block buy_widget_buy_form %}
    {% if config('FlinkSizeCharts.config.modalButtonPosition') == 'before__page_product_detail_buy_form' %}
        {% sw_include '@FlinkSizeChart/flink/component/size-chart-button-position.html.twig' %}
    {% endif %}

    {{ parent() }}

    {% if config('FlinkSizeCharts.config.modalButtonPosition') == 'after__page_product_detail_buy_form' %}
        {% sw_include '@FlinkSizeChart/flink/component/size-chart-button-position.html.twig' %}
    {% endif %}
{% endblock %}

{% block buy_widget_ordernumber_container %}

    {% if config('FlinkSizeCharts.config.modalButtonPosition') == 'before__ordernumber_container' %}
        {% sw_include '@FlinkSizeChart/flink/component/size-chart-button-position.html.twig' %}
    {% endif %}

    {{ parent() }}

    {% if config('FlinkSizeCharts.config.modalButtonPosition') == 'after__ordernumber_container' %}
        {% sw_include '@FlinkSizeChart/flink/component/size-chart-button-position.html.twig' %}
    {% endif %}
{% endblock %}
