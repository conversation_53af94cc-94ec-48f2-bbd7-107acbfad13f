<?php declare(strict_types=1);

namespace Brainst\AutofillCity\Storefront\Controller;

use Brainst\AutofillCity\Core\Content\BrainstPostalCode\SalesChannel\AbstractBrainstPostalCodeRoute;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Criteria;
use Shopware\Core\System\SalesChannel\SalesChannelContext;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Attribute\Route;

#[Route(defaults: ['_routeScope' => ['storefront']])]
class BrainstPostalCodeController extends AbstractController
{

    private AbstractBrainstPostalCodeRoute $route;

    public function __construct(AbstractBrainstPostalCodeRoute $route)
    {
        $this->route = $route;
    }

    #[Route(path: '/brainst-postal-code/city/{postalCode}', name: 'storefront.brainst-postal-code.city', defaults: ['XmlHttpRequest' => 'true'], methods: ['GET'])]
    public function getCityByPostalCode(string $postalCode, Request $request, SalesChannelContext $context): JsonResponse
    {
        $countryId = $request->query->get('countryId');
        $result = $this->route->getCity(new Criteria(), $context, $postalCode, $countryId);
        return new JsonResponse(['city' => $result->getPostalCodes()->first()?->getTranslation('city')]);
    }
}
