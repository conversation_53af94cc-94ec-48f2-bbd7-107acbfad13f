import template from './brainst-postal-code-list.html.twig';

// const {Component, Mixin} = Shopware;
const Criteria = Shopware.Data.Criteria;

Shopware.Component.register('brainst-postal-code-list', {
    template,

    inject: ['repositoryFactory'],

    data() {
        return {
            postalCodeEntries: null,
            page: 1,
            limit: 25,
            total: 0,
            isLoading: true
        };
    },

    metaInfo() {
        return {
            title: this.$createTitle(),
        };
    },

    created() {
        this.getList();
    },

    computed: {
        postalCodeEntriesRepository() {
            return this.repositoryFactory.create('brainst_postal_code');
        },

        columns() {
            return [
                {
                    primary: true,
                    allowResize: true,
                    dataIndex: "code",
                    label: this.$tc('PostalCodePlugin.list.table.code'),
                    property: "code",
                    routerLink: 'brainst.postal.code.detail',
                },
                {
                    allowResize: true,
                    dataIndex: "city.translated.name",
                    inlineEdit: "string",
                    label: this.$tc('PostalCodePlugin.list.table.city'),
                    property: "city",
                },
                {
                    allowResize: true,
                    dataIndex: "country.name",
                    label: this.$tc('PostalCodePlugin.list.table.countryName'),
                    property: "countryName",
                    routerLink: 'sw.settings.country.detail',
                },
                {
                    property: 'active',
                    label: this.$tc('PostalCodePlugin.list.table.active'),
                    align: "center",
                    allowResize: true,
                    dataIndex: "active",
                    inlineEdit: "boolean"
                }
            ];
        },
    },

    methods: {
        changeLanguage(newLanguageId) {
            this.getList();
        },
        updateRecords(results) {
            this.total = results.total;
        },

        getList() {
            this.isLoading = true;
            const criteria = new Criteria(this.page, this.limit);
            criteria.addAssociation('country');
            criteria.addFields('code', 'city', 'active', 'country.name', 'name');
            criteria.addSorting(Criteria.sort('createdAt', 'DESC', false));

            return this.postalCodeEntriesRepository.search(criteria, Shopware.Context.api).then((results) => {
                this.postalCodeEntries = results;
                this.isLoading = false;
                this.updateRecords(results);
            });
        },
    },
});