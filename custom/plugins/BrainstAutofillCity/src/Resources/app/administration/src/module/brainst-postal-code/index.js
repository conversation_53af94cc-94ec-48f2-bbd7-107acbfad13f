import './page/brainst-postal-code-list';
import './page/brainst-postal-code-detail';
import './page/brainst-postal-code-create';
import deDE from './../../snippet/de-DE';
import enGB from './../../snippet/en-GB';

Shopware.Module.register('brainst-postal-code', {
    type: 'plugin',
    name: 'PostalCodePlugin.general.mainMenuItemGeneral',
    title: 'PostalCodePlugin.general.mainMenuItemGeneral',
    description: 'PostalCodePlugin.general.descriptionTextModule',
    color: '#57D9A3FF',
    icon: 'regular-map',

    snippets: {
        'de-DE': deDE,
        'en-GB': enGB
    },

    routes: {
        list: {
            component: 'brainst-postal-code-list',
            path: 'list'
        },
        detail: {
            component: 'brainst-postal-code-detail',
            path: 'detail/:id',
            meta: {
                parentPath: 'brainst.postal.code.list'
            }
        },
        create: {
            component: 'brainst-postal-code-create',
            path: 'create',
            meta: {
                parentPath: 'brainst.postal.code.list'
            }
        }
    },
    navigation: [{
        id: 'brainst-postal-code',
        label: 'PostalCodePlugin.general.mainMenuItemGeneral',
        color: '#ff3d58',
        path: 'brainst.postal.code.list',
        icon: 'regular-map-marker',
        parent: 'sw-content',
        position: 100
    }]
});
