"use strict";(self.webpackChunk=self.webpackChunk||[]).push([["postal-code-plugin"],{4149:(t,e,s)=>{var a=s(6285);class i extends a.Z{init(){const t=this.el.getAttribute("data-id"),e=this.el.closest("form");if(t){const s=e.querySelector("#"+t+"AddressZipcode"),a=e.querySelector("#"+t+"AddressCity");s&&s.addEventListener("blur",(async t=>{const e=t.target.value;e.length&&this.fetch(a,e)}))}}async fetch(t,e){const s=await fetch(`/postal-code/city/${e}`),a=await s.json();a.city&&(t.value=a.city)}}window.PluginManager.register("AutoFillCity",i,"[data-auto-fill-city]")}},t=>{t.O(0,["vendor-node","vendor-shared"],(()=>{return e=4149,t(t.s=e);var e}));t.O()}]);