import Plugin from 'src/plugin-system/plugin.class';

export default class AutoFillCity extends Plugin {
    init() {
        const prefix = this.el.getAttribute('data-id');
        const parentForm = this.el.closest('form');

        if (prefix) {
            const postalCodeInput = parentForm.querySelector('#' + prefix + 'AddressZipcode');
            const cityInput = parentForm.querySelector('#' + prefix + 'AddressCity');

            if (postalCodeInput) {
                postalCodeInput.addEventListener('blur', async (event) => {
                    const postalCode = event.target.value;

                    if (postalCode.length) {
                        this.fetch(cityInput, postalCode);
                    }
                });
            }
        }
    }

    async fetch(cityInput, postalCode) {
        const response = await fetch(`/brainst-postal-code/city/${postalCode}`);
        const data = await response.json()
        if (data.city) cityInput.value = data.city;
    }
}