<?xml version="1.0" ?>

<container xmlns="http://symfony.com/schema/dic/services"
           xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
           xsi:schemaLocation="http://symfony.com/schema/dic/services http://symfony.com/schema/dic/services/services-1.0.xsd">

    <services>
        <service id="Brainst\AutofillCity\Core\Content\BrainstPostalCode\BrainstPostalCodeDefinition">
            <tag name="shopware.entity.definition" entity="brainst_postal_code"/>
        </service>

        <service
                id="Brainst\AutofillCity\Core\Content\BrainstPostalCode\Aggregate\BrainstPostalCodeTranslation\BrainstPostalCodeTranslationDefinition">
            <tag name="shopware.entity.definition" entity="brainst_postal_code_translation"/>
        </service>

        <service id="Brainst\AutofillCity\Storefront\Controller\BrainstPostalCodeController" public="true">
            <argument type="service"
                      id="Brainst\AutofillCity\Core\Content\BrainstPostalCode\SalesChannel\BrainstPostalCodeRoute"/>
            <call method="setContainer">
                <argument type="service" id="service_container"/>
            </call>
        </service>

        <service id="Brainst\AutofillCity\Core\Content\BrainstPostalCode\SalesChannel\BrainstPostalCodeRoute"
                 public="true">
            <argument type="service" id="brainst_postal_code.repository"/>
        </service>

    </services>
</container>