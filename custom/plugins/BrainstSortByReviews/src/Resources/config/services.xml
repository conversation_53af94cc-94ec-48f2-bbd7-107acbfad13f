<?xml version="1.0" ?>

<container xmlns="http://symfony.com/schema/dic/services"
           xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
           xsi:schemaLocation="http://symfony.com/schema/dic/services http://symfony.com/schema/dic/services/services-1.0.xsd">

    <services>

        <service id="Brainst\SortBy\Reviews\Extension\Content\Product\ProductExtension" public="true">
            <tag name="shopware.entity.extension"/>
        </service>

        <service id="Brainst\SortBy\Reviews\Core\Content\Product\DataAbstractionLayer\ReviewCountUpdater" public="true">
            <argument type="service" id="Doctrine\DBAL\Connection"/>
        </service>

        <service id="Brainst\SortBy\Reviews\Subscriber\ListenToProductReviewChanges" public="false">
            <argument type="service" id="Doctrine\DBAL\Connection"/>
            <argument type="service"
                      id="Brainst\SortBy\Reviews\Core\Content\Product\DataAbstractionLayer\ReviewCountUpdater"/>
            <tag name="kernel.event_subscriber"/>
        </service>
    </services>
</container>