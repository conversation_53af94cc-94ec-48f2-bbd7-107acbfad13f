import Plugin from 'src/plugin-system/plugin.class';
import {append, classToggle, createElement, createParagraph, isString} from './helpers';

export default class BrainstCartMessage extends Plugin {
    /**
     * Static options for configuring the sliding banner.
     *
     * @static
     * @type {Object}
     * @property {boolean} displayCloseButton - Indicates whether to display the close button. Default is `false`.
     * @property {string} displayPosition - The position of the banner (e.g., 'right', 'left'). Default is `'right'`.
     * @property {number} offerZIndex - The z-index of the banner. Default is `1045`.
     * @property {number} animationSpeed - The speed of the banner animation, where higher values indicate faster animation. Default is `0.5`.
     * @property {boolean} internalLink - The type of link is  internal or extranal
     */
    static options = {
        displayCloseButton: false,
        displayPosition: 'right',
        offerZIndex: 1045,
        animationSpeed: 0.5,
        internalLink: false,
    };

    /**
     * Initializes the sliding banner by fetching media data, configuring options,
     * setting up the initial state, creating the banner, and adding a resize event listener.
     *
     * @returns {Promise<void>} A promise that resolves when the initialization is complete.
     */
    async init() {
        const offerData = await this.fetchMediaData();

        if (!offerData || !offerData.hasOffer) {
            return;
        }

        const options = this.configureOptions(offerData);

        this.initializeState();

        this.createSlidingBanner(options);
        window.addEventListener('resize', this.reCreateSlidingBanner.bind(this, options));
    }

    /**
     * Configures the options for the sliding banner by merging the provided options with default options.
     * It verifies and sets default values for various properties if they are not correctly provided.
     *
     * @param {Object} [options={}] - The options to configure.
     *
     * @returns {Object} The configured options with default values applied.
     */
    configureOptions(options) {
        options = Object.assign({}, this.options, options);
        options.innerWidth = window.innerWidth || "";

        // Verify display position
        if (!isString(options.displayPosition) || options.displayPosition.length === 0) {
            options.displayPosition = this.options.displayPosition;
        }

        // Verify show offer z-index
        if (typeof options.offerZIndex !== 'number' || options.offerZIndex < 0) {
            options.offerZIndex = this.options.offerZIndex;
        }

        // Verify show animation speed
        if (typeof options.animationSpeed !== 'number' || options.animationSpeed < 0) {
            options.animationSpeed = this.options.animationSpeed;
        }

        return options;
    }

    /**
     * Fetches media data for the sliding banner from the specified endpoint.
     * The endpoint includes an optional sales channel ID.
     *
     * @returns {Promise<Object>} A promise that resolves to the JSON response from the fetch request.
     */
    async fetchMediaData() {
        const url = this.getUrl();
        const response = await fetch(url, {
            method: 'GET',
            headers: {
                'X-Robots-Tag': 'noindex, nofollow'
            }
        });
        return await response.json();
    }

    /**
     * Get the URL for the API call based on sales channel configuration and url
     *
     * @returns {String} The url for API call.
     */
    getUrl(){
        const tokenUrl = window.router["frontend.app-system.generate-token"];

        const prefix = tokenUrl.replace('/app-system/Placeholder/generate-token', '')

        return `${prefix}/brainst-sliding-banner/offer/${window.salesChannelId || ""}`;
    }

    /**
     * Initializes the state properties of the sliding banner component.
     * This method sets up initial values for various state variables.
     *
     * @returns {void}
     */
    initializeState() {
        this.isDragging = false;
        this.startY = 0;
        this.startTop = 0;
        this.screenHeight = window.innerHeight;
        this.stopClickEvent = false;
        this.longPressTimeout = null;
        this.parentContainer = null;
        this.previousWidth = null;
        this.bannerDiv = null;
    }

    /**
     * Recreates the sliding banner if the window width has changed.
     * Removes the existing banner if it exists, updates the options with the current width, and creates a new banner.
     *
     * @param {Object} options - The options for recreating the sliding banner.
     * @param {number} options.innerWidth - The inner width of the viewport.
     * @param {boolean} options.internalLink - The type of link is  internal or extranal
     * @param {boolean} options.enableOnMobile - Flag indicating whether to enable features on mobile.
     * @param {number} options.OfferZIndex - The z-index for the banner.
     * @param {string} options.background - The background style for the banner.
     * @param {string} options.displayPosition - The position of the banner (e.g., 'right', 'left').
     * @param {boolean} options.displayCloseButton - Flag indicating whether to display a close button.
     * @param {string} options.title - The title to display on the banner handle.
     * @returns {void}
     */
    reCreateSlidingBanner(options) {
        const currentWidth = window.innerWidth;
        if (this.previousWidth !== currentWidth) {
            if (this.bannerDiv) {
                this.bannerDiv.remove();
            }
            options.innerWidth = currentWidth;
            this.createSlidingBanner(options);
        }
    }

    /**
     * Creates and displays a sliding banner with specified options.
     * The banner includes a handle, a close button, and an advertise anchor.
     *
     * @param {Object} options - The options for creating the sliding banner.
     * @returns {void}
     */
    createSlidingBanner(options) {
        this.previousWidth = window.innerWidth;

        if (options.innerWidth < this.getBreakpoints().sm && !options.enableOnMobile) {
            return;
        }

        const bannerDiv = this.createBannerDiv(options);
        const handleDiv = this.createHandleDiv(options.displayPosition);
        const closeButton = this.createCloseButton(options.displayCloseButton, bannerDiv)
        const handleIconDiv = createElement('div', `brainst-sliding-banner-handle-icon`, `brainst-sliding-banner-handle-icon__${options.displayPosition === 'right' ? 'open' : 'close'}`);
        const handleTitle = createParagraph('brainst-sliding-banner-handle__title')(options.title);

        append(handleDiv, closeButton, handleIconDiv, handleTitle);
        append(bannerDiv, handleDiv);
        append(document.body, bannerDiv);

        options.handleWidth = handleDiv.offsetWidth;

        const advertiseWidth = this.applyBannerStyles(bannerDiv, options);

        // Create advertise anchor
        const advertiseAnchor = this.createAdvertiseAnchor(options, advertiseWidth);

        // Append advertise anchor to banner
        append(bannerDiv, advertiseAnchor);

        this.addEvents(bannerDiv, handleDiv, handleIconDiv, options.displayPosition, advertiseWidth);
        this.bannerDiv = bannerDiv;
    }

    /**
     * Creates a banner div element with specified styles from the options.
     *
     * @param {Object} options - The options for creating the banner div.
     * @param {number} options.OfferZIndex - The z-index for the banner.
     * @param {string} options.background - The background style for the banner.
     * @returns {HTMLDivElement} The created banner div element.
     */
    createBannerDiv(options) {
        const bannerDiv = createElement('div', 'brainst-sliding-banner');
        bannerDiv.style.setProperty('--brainst-advertise-z-index', options.OfferZIndex);
        bannerDiv.style.setProperty('--brainst-advertise-background', options.background);
        return bannerDiv;
    }

    /**
     * Creates a handle `div` element for the sliding banner with specified styles based on the display position.
     *
     * @param {string} displayPosition - The position of the banner handle, which determines which corners to unset ('top', 'bottom', 'left', or 'right').
     * @returns {HTMLDivElement} The created `div` element with applied styles.
     */
    createHandleDiv(displayPosition) {
        const handleDiv = createElement('div', 'brainst-sliding-banner-handle', 'btn', 'btn-primary');
        handleDiv.style.setProperty(`border-top-${displayPosition}-radius`, 'unset');
        handleDiv.style.setProperty(`border-bottom-${displayPosition}-radius`, 'unset');
        return handleDiv;
    }

    /**
     * Creates a close button for the sliding banner if the `button` parameter is true.
     * The close button removes the banner from the DOM when clicked.
     *
     * @param {boolean} button - A flag indicating whether to create the close button.
     * @param {HTMLDivElement} bannerDiv - The `div` element representing the banner, which will be removed when the button is clicked.
     * @returns {HTMLButtonElement|boolean} The created close button if `button` is true; otherwise, `false`.
     */
    createCloseButton(button, bannerDiv) {
        if (button) {
            const closeButton = createElement('button', 'brainst-sliding-banner-handle__button');
            closeButton.innerText = 'X';

            // Remove offer click event listener
            closeButton.addEventListener('click', () => bannerDiv.remove());

            return closeButton;
        }
        return false;
    }

    /**
     * Applies CSS styles to the banner element based on the provided options.
     *
     * @param {HTMLDivElement} bannerDiv - The `div` element representing the banner to which styles will be applied.
     * @param {Object} options - The options used to style the banner.
     * @param {number} options.width - The width of the banner.
     * @param {number} options.innerWidth - The inner width of the viewport.
     * @param {number} options.handleWidth - The width of the handle element.
     * @param {number} options.animationSpeed - The speed of the banner animation in seconds.
     * @param {string} options.displayPosition - The position of the banner ('left' or 'right').
     * @returns {number} The calculated width of the banner element.
     */
    applyBannerStyles(bannerDiv, options) {
        let advertiseWidth = Math.min(options.width, options.innerWidth - options.handleWidth - 10);

        bannerDiv.style.setProperty('--brainst-advertise-width', `${advertiseWidth}px`);
        bannerDiv.style.transition = `right ${options.animationSpeed}s ease-out, left ${options.animationSpeed}s ease-out`;
        bannerDiv.style[options.displayPosition === 'left' ? 'left' : 'right'] = `-${advertiseWidth}px`;
        if (options.displayPosition === 'left') {
            bannerDiv.style.flexDirection = 'row-reverse';
        }
        return advertiseWidth;
    }

    /**
     * Creates an advertisement anchor element with specified attributes and content based on the provided options.
     *
     * @param {Object} options - The options used to configure the advertisement anchor.
     * @param {number} advertiseWidth - The width to be used for the responsive image.
     * @returns {HTMLAnchorElement} The created anchor element with applied attributes and content.
     */
    createAdvertiseAnchor(options, advertiseWidth) {
        const advertiseAnchor = createElement('a', 'brainst-sliding-banner-advertise');
        advertiseAnchor.href = options.link;
        advertiseAnchor.title = options.title;

        if (!options.internalLink) {
            advertiseAnchor.rel = 'noopener';
            advertiseAnchor.target = '_blank';
        }

        if (options.labelShow) {
            const labelSpan = this.createLabelSpan(options);
            append(advertiseAnchor, labelSpan);
        }
        append(advertiseAnchor, this.createResponsiveImage(options.media, advertiseWidth));

        return advertiseAnchor;
    }

    /**
     * Creates a label span element with styles and text based on the provided options.
     *
     * @param {Object} options - The options used to configure the label span.
     *
     * @returns {HTMLSpanElement} The created label span element with applied styles and text.
     */
    createLabelSpan(options) {
        const labelSpan = createElement('span', 'brainst-sliding-banner-advertise-label');
        labelSpan.innerText = options.label;
        labelSpan.style.setProperty('--brainst-advertise-label-background', options.labelColour);
        labelSpan.style.setProperty('--brainst-advertise-label-colour', options.labelTextColour);
        labelSpan.style.setProperty('--brainst-advertise-label-border-radios', `${options.labelBorderRadios}rem`);

        const labelPosition = this.getLabelPosition(options);
        labelSpan.style.setProperty(`--brainst-advertise-label-${labelPosition.column}`, labelPosition.columnValue);
        labelSpan.style.setProperty(`--brainst-advertise-label-${labelPosition.row}`, '10px');

        return labelSpan;
    }

    /**
     * Adds event listeners to the specified elements to handle user interactions and toggle the banner's visibility.
     *
     * @param {HTMLElement} container - The container element that will be adjusted based on user interactions.
     * @param {HTMLElement} handleEl - The handle element that triggers the banner's open/close actions.
     * @param {HTMLElement} openCloseIcon - The icon element that indicates the open or close state of the banner.
     * @param {string} displayPosition - The position where the banner is displayed ('left' or 'right').
     * @param {number} advertiseWidth - The width of the banner, used to adjust its position.
     */
    addEvents(container, handleEl, openCloseIcon, displayPosition, advertiseWidth) {
        this.parentContainer = handleEl.parentElement;
        this.handleEl = handleEl;

        const longPressHandlers = {
            start: this.handleLongPressStart.bind(this),
            end: this.handleLongPressEnd.bind(this),
        };

        handleEl.addEventListener('mousedown', longPressHandlers.start);
        handleEl.addEventListener('touchstart', longPressHandlers.start);
        handleEl.addEventListener('mouseup', longPressHandlers.end);
        handleEl.addEventListener('mouseleave', longPressHandlers.end);
        handleEl.addEventListener('touchend', longPressHandlers.end);
        handleEl.addEventListener('touchcancel', longPressHandlers.end);

        handleEl.addEventListener('click', () => {
            if (this.stopClickEvent) return;
            const isOpen = openCloseIcon.classList.contains('brainst-sliding-banner-handle-icon__open');
            const adjustedIsOpen = displayPosition === 'left' ? !isOpen : isOpen;

            container.style[displayPosition] = adjustedIsOpen ? '0' : `-${advertiseWidth}px`;
            classToggle(openCloseIcon, 'brainst-sliding-banner-handle-icon__open', 'brainst-sliding-banner-handle-icon__close');
        });
    }

    /**
     * Determines the label position based on the provided options.
     *
     * @param {Object} options - The options used to configure the label position.
     * @param {string} options.displayPosition - The position where the banner is displayed ('left' or 'right').
     * @param {number} options.handleWidth - The width of the handle element.
     * @param {string} options.labelPlacement - The desired placement of the label ('topLeft', 'topRight', 'bottomRight', 'bottomLeft').
     * @returns {Object} An object representing the label's position, including column, columnValue, and row.
     */
    getLabelPosition(options) {
        const leftSpace = options.displayPosition === 'right' ? options.handleWidth + 10 : 10;
        const rightSpace = options.displayPosition === 'left' ? options.handleWidth + 10 : 10;
        const positions = {
            topLeft: {column: 'left', columnValue: `${leftSpace}px`, row: 'top'},
            topRight: {column: 'right', columnValue: `${rightSpace}px`, row: 'top'},
            bottomRight: {column: 'right', columnValue: `${rightSpace}px`, row: 'bottom'},
            bottomLeft: {column: 'left', columnValue: `${leftSpace}px`, row: 'bottom'},
        };
        return positions[options.labelPlacement] || positions.topLeft;
    }

    /**
     * Creates a responsive image element with the provided media data.
     *
     * @param {Object} mediaData - The media data used to configure the image.
     * @param {string} mediaData.url - The URL of the default image.
     * @param {string} mediaData.alt - The alt text for the image.
     * @param {Array<Object>} mediaData.thumbnails - An array of thumbnail objects with width and URL properties.
     * @param {number} advertiseWidth - The width of the banner used to set responsive image sizes.
     * @returns {HTMLImageElement} The created image element with responsive attributes.
     */
    createResponsiveImage(mediaData, advertiseWidth) {
        const sizes = mediaData.thumbnails
            .map(({width}) => `(min-width: ${width}px) ${((advertiseWidth >= width) ? width : advertiseWidth) - 1}px`)
            .reverse()
            .join(', ');

        const thumbs = mediaData.thumbnails
            .map(({url, width}) => `${url} ${width}w`)
            .reverse()
            .join(', ');

        const img = createElement('img', 'brainst-sliding-banner-advertise-image');
        img.classList.add('cms-image');
        img.alt = mediaData.alt;
        img.setAttribute('loading', 'lazy');
        img.setAttribute('data-object-fit', 'cover');
        img.src = mediaData.url;
        img.srcset = thumbs;
        img.sizes = `${sizes} , (min-width: 0px) ${advertiseWidth - 1}px, ${this.pxToVw(advertiseWidth)}vw`;

        return img;
    }

    /**
     * Converts pixels to viewport width (vw) units.
     *
     * @param {number} px - The pixel value to be converted.
     * @returns {number} The equivalent value in viewport width (vw) units.
     */
    pxToVw(px) {
        return ((px / window.innerWidth) * 100);
    }

    /**
     * Handles the start of a long press event.
     *
     * @param {MouseEvent|TouchEvent} e - The event object representing the start of the drag.
     * @returns {void}
     */
    handleLongPressStart(e) {
        e.preventDefault();
        this.stopClickEvent = false;
        this.longPressTimeout = setTimeout(() => {
            if (navigator.vibrate) navigator.vibrate(50);
            this.startDrag(e);
        }, 500);
    }

    /**
     * Handles the end of a long press event.
     *
     * @param {MouseEvent|TouchEvent} e - The event object representing the start of the drag.
     * @returns {void}
     */
    handleLongPressEnd(e) {
        clearTimeout(this.longPressTimeout);
        if (!this.stopClickEvent && e.type === 'touchend') this.handleEl.click();
    }

    /**
     * Initializes the drag operation by setting up initial values and adding event listeners.
     *
     * @param {MouseEvent|TouchEvent} e - The event object representing the start of the drag.
     * @returns {void}
     */
    startDrag(e) {
        this.handleEl.style.boxShadow = '0 0 12px #000000';
        this.isDragging = true;
        this.startY = e.clientY || e.touches[0].clientY;
        this.startTop = this.parentContainer.offsetTop;
        this.stopClickEvent = true;

        document.addEventListener('mousemove', this.moveDrag.bind(this));
        document.addEventListener('touchmove', this.moveDrag.bind(this));
        document.addEventListener('mouseup', this.endDrag.bind(this));
        document.addEventListener('touchend', this.endDrag.bind(this));
    }

    /**
     * Handles the movement of the dragged element.
     *
     * @param {MouseEvent|TouchEvent} e - The event object representing the movement (mouse or touch).
     * @returns {void}
     */
    moveDrag(e) {
        if (!this.isDragging) return;

        const containerHeight = this.parentContainer.offsetHeight;
        const halfContainer = containerHeight / 2;
        const currentY = e.clientY !== undefined ? e.clientY : (e.touches && e.touches[0] ? e.touches[0].clientY : 0);
        const deltaY = currentY - this.startY;
        let newTop = this.startTop + deltaY;

        newTop = Math.max(halfContainer, Math.min(newTop, this.screenHeight - halfContainer));
        this.parentContainer.style.top = `${newTop}px`;
    }

    /**
     * Ends the drag operation by cleaning up event listeners and resetting styles.
     *
     * @returns {void}
     */
    endDrag() {
        this.isDragging = false;
        document.removeEventListener('mousemove', this.moveDrag.bind(this));
        document.removeEventListener('touchmove', this.moveDrag.bind(this));
        document.removeEventListener('mouseup', this.endDrag.bind(this));
        document.removeEventListener('touchend', this.endDrag.bind(this));
        this.handleEl.style.removeProperty('box-shadow');
    }

    /**
     * Retrieves the responsive design breakpoints.
     *
     * @returns {Object} An object containing the breakpoint names and their corresponding pixel values.
     *
     * @example
     * const breakpoints = getBreakpoints();
     * console.log(breakpoints.sm); // 576
     */
    getBreakpoints() {
        return {
            xs: 0,
            sm: 576,
            md: 768,
            lg: 992,
            xl: 1200,
            xxl: 1400,
            ...window.breakpoints
        };
    }
}
