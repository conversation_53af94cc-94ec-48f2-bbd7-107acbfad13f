(()=>{"use strict";var t={857:t=>{var e=function(t){var e;return!!t&&"object"==typeof t&&"[object RegExp]"!==(e=Object.prototype.toString.call(t))&&"[object Date]"!==e&&t.$$typeof!==n},n="function"==typeof Symbol&&Symbol.for?Symbol.for("react.element"):60103;function i(t,e){return!1!==e.clone&&e.isMergeableObject(t)?a(Array.isArray(t)?[]:{},t,e):t}function r(t,e,n){return t.concat(e).map(function(t){return i(t,n)})}function s(t){return Object.keys(t).concat(Object.getOwnPropertySymbols?Object.getOwnPropertySymbols(t).filter(function(e){return Object.propertyIsEnumerable.call(t,e)}):[])}function o(t,e){try{return e in t}catch(t){return!1}}function a(t,n,l){(l=l||{}).arrayMerge=l.arrayMerge||r,l.isMergeableObject=l.isMergeableObject||e,l.cloneUnlessOtherwiseSpecified=i;var d,c,h=Array.isArray(n);return h!==Array.isArray(t)?i(n,l):h?l.arrayMerge(t,n,l):(c={},(d=l).isMergeableObject(t)&&s(t).forEach(function(e){c[e]=i(t[e],d)}),s(n).forEach(function(e){(!o(t,e)||Object.hasOwnProperty.call(t,e)&&Object.propertyIsEnumerable.call(t,e))&&(o(t,e)&&d.isMergeableObject(n[e])?c[e]=(function(t,e){if(!e.customMerge)return a;var n=e.customMerge(t);return"function"==typeof n?n:a})(e,d)(t[e],n[e],d):c[e]=i(n[e],d))}),c)}a.all=function(t,e){if(!Array.isArray(t))throw Error("first argument should be an array");return t.reduce(function(t,n){return a(t,n,e)},{})},t.exports=a}},e={};function n(i){var r=e[i];if(void 0!==r)return r.exports;var s=e[i]={exports:{}};return t[i](s,s.exports,n),s.exports}(()=>{n.n=t=>{var e=t&&t.__esModule?()=>t.default:()=>t;return n.d(e,{a:e}),e}})(),(()=>{n.d=(t,e)=>{for(var i in e)n.o(e,i)&&!n.o(t,i)&&Object.defineProperty(t,i,{enumerable:!0,get:e[i]})}})(),(()=>{n.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e)})(),(()=>{var t=n(857),e=n.n(t);class i{static ucFirst(t){return t.charAt(0).toUpperCase()+t.slice(1)}static lcFirst(t){return t.charAt(0).toLowerCase()+t.slice(1)}static toDashCase(t){return t.replace(/([A-Z])/g,"-$1").replace(/^-/,"").toLowerCase()}static toLowerCamelCase(t,e){let n=i.toUpperCamelCase(t,e);return i.lcFirst(n)}static toUpperCamelCase(t,e){return e?t.split(e).map(t=>i.ucFirst(t.toLowerCase())).join(""):i.ucFirst(t.toLowerCase())}static parsePrimitive(t){try{return/^\d+(.|,)\d+$/.test(t)&&(t=t.replace(",",".")),JSON.parse(t)}catch(e){return t.toString()}}}class r{static isNode(t){return"object"==typeof t&&null!==t&&(t===document||t===window||t instanceof Node)}static hasAttribute(t,e){if(!r.isNode(t))throw Error("The element must be a valid HTML Node!");return"function"==typeof t.hasAttribute&&t.hasAttribute(e)}static getAttribute(t,e){let n=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(n&&!1===r.hasAttribute(t,e))throw Error('The required property "'.concat(e,'" does not exist!'));if("function"!=typeof t.getAttribute){if(n)throw Error("This node doesn't support the getAttribute function!");return}return t.getAttribute(e)}static getDataAttribute(t,e){let n=!(arguments.length>2)||void 0===arguments[2]||arguments[2],s=e.replace(/^data(|-)/,""),o=i.toLowerCamelCase(s,"-");if(!r.isNode(t)){if(n)throw Error("The passed node is not a valid HTML Node!");return}if(void 0===t.dataset){if(n)throw Error("This node doesn't support the dataset attribute!");return}let a=t.dataset[o];if(void 0===a){if(n)throw Error('The required data attribute "'.concat(e,'" does not exist on ').concat(t,"!"));return a}return i.parsePrimitive(a)}static querySelector(t,e){let n=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(n&&!r.isNode(t))throw Error("The parent node is not a valid HTML Node!");let i=t.querySelector(e)||!1;if(n&&!1===i)throw Error('The required element "'.concat(e,'" does not exist in parent node!'));return i}static querySelectorAll(t,e){let n=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(n&&!r.isNode(t))throw Error("The parent node is not a valid HTML Node!");let i=t.querySelectorAll(e);if(0===i.length&&(i=!1),n&&!1===i)throw Error('At least one item of "'.concat(e,'" must exist in parent node!'));return i}static getFocusableElements(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:document.body;return t.querySelectorAll('\n            input:not([tabindex^="-"]):not([disabled]):not([type="hidden"]),\n            select:not([tabindex^="-"]):not([disabled]),\n            textarea:not([tabindex^="-"]):not([disabled]),\n            button:not([tabindex^="-"]):not([disabled]),\n            a[href]:not([tabindex^="-"]):not([disabled]),\n            [tabindex]:not([tabindex^="-"]):not([disabled])\n        ')}static getFirstFocusableElement(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:document.body;return this.getFocusableElements(t)[0]}static getLastFocusableElement(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:document,e=this.getFocusableElements(t);return e[e.length-1]}}class s{publish(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],i=new CustomEvent(t,{detail:e,cancelable:n});return this.el.dispatchEvent(i),i}subscribe(t,e){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},i=this,r=t.split("."),s=n.scope?e.bind(n.scope):e;if(n.once&&!0===n.once){let e=s;s=function(n){i.unsubscribe(t),e(n)}}return this.el.addEventListener(r[0],s),this.listeners.push({splitEventName:r,opts:n,cb:s}),!0}unsubscribe(t){let e=t.split(".");return this.listeners=this.listeners.reduce((t,n)=>([...n.splitEventName].sort().toString()===e.sort().toString()?this.el.removeEventListener(n.splitEventName[0],n.cb):t.push(n),t),[]),!0}reset(){return this.listeners.forEach(t=>{this.el.removeEventListener(t.splitEventName[0],t.cb)}),this.listeners=[],!0}get el(){return this._el}set el(t){this._el=t}get listeners(){return this._listeners}set listeners(t){this._listeners=t}constructor(t=document){this._el=t,t.$emitter=this,this._listeners=[]}}class o{init(){throw Error('The "init" method for the plugin "'.concat(this._pluginName,'" is not defined.'))}update(){}_init(){this._initialized||(this.init(),this._initialized=!0)}_update(){this._initialized&&this.update()}_mergeOptions(t){let n=i.toDashCase(this._pluginName),s=r.getDataAttribute(this.el,"data-".concat(n,"-config"),!1),o=r.getAttribute(this.el,"data-".concat(n,"-options"),!1),a=[this.constructor.options,this.options,t];s&&a.push(window.PluginConfigManager.get(this._pluginName,s));try{o&&a.push(JSON.parse(o))}catch(t){throw console.error(this.el),Error('The data attribute "data-'.concat(n,'-options" could not be parsed to json: ').concat(t.message))}return e().all(a.filter(t=>t instanceof Object&&!(t instanceof Array)).map(t=>t||{}))}_registerInstance(){window.PluginManager.getPluginInstancesFromElement(this.el).set(this._pluginName,this),window.PluginManager.getPlugin(this._pluginName,!1).get("instances").push(this)}_getPluginName(t){return t||(t=this.constructor.name),t}constructor(t,e={},n=!1){if(!r.isNode(t))throw Error("There is no valid element given.");this.el=t,this.$emitter=new s(this.el),this._pluginName=this._getPluginName(n),this.options=this._mergeOptions(e),this._initialized=!1,this._registerInstance(),this._init()}}let a=function(t){for(var e=arguments.length,n=Array(e>1?e-1:0),i=1;i<e;i++)n[i-1]=arguments[i];return function(){for(var e=arguments.length,i=Array(e),r=0;r<e;r++)i[r]=arguments[r];return t(...n,...i)}},l=function(t){for(var e=arguments.length,n=Array(e>1?e-1:0),i=1;i<e;i++)n[i-1]=arguments[i];return n.forEach(e=>e&&t.appendChild(e))},d=t=>"string"==typeof t,c=function(t){for(var e=arguments.length,n=Array(e>1?e-1:0),i=1;i<e;i++)n[i-1]=arguments[i];let r=document.createElement(t);return n.length&&n.forEach(t=>r.classList.add(t)),r},h=(t,e)=>(t.innerText=e,t),u=function(t){for(var e=arguments.length,n=Array(e>1?e-1:0),i=1;i<e;i++)n[i-1]=arguments[i];return a(h,c(t,...n))},p=function(){for(var t=arguments.length,e=Array(t),n=0;n<t;n++)e[n]=arguments[n];return u("p",...e)},g=function(t){for(var e=arguments.length,n=Array(e>1?e-1:0),i=1;i<e;i++)n[i-1]=arguments[i];return n.map(e=>t.classList.toggle(e))};class b extends o{async init(){let t=await this.fetchMediaData();if(!t||!t.hasOffer)return;let e=this.configureOptions(t);this.initializeState(),this.createSlidingBanner(e),window.addEventListener("resize",this.reCreateSlidingBanner.bind(this,e))}configureOptions(t){return(t=Object.assign({},this.options,t)).innerWidth=window.innerWidth||"",d(t.displayPosition)&&0!==t.displayPosition.length||(t.displayPosition=this.options.displayPosition),("number"!=typeof t.offerZIndex||t.offerZIndex<0)&&(t.offerZIndex=this.options.offerZIndex),("number"!=typeof t.animationSpeed||t.animationSpeed<0)&&(t.animationSpeed=this.options.animationSpeed),t}async fetchMediaData(){let t=this.getUrl(),e=await fetch(t,{method:"GET",headers:{"X-Robots-Tag":"noindex, nofollow"}});return await e.json()}getUrl(){let t=window.router["frontend.app-system.generate-token"].replace("/app-system/Placeholder/generate-token","");return"".concat(t,"/brainst-sliding-banner/offer/").concat(window.salesChannelId||"")}initializeState(){this.isDragging=!1,this.startY=0,this.startTop=0,this.screenHeight=window.innerHeight,this.stopClickEvent=!1,this.longPressTimeout=null,this.parentContainer=null,this.previousWidth=null,this.bannerDiv=null}reCreateSlidingBanner(t){let e=window.innerWidth;this.previousWidth!==e&&(this.bannerDiv&&this.bannerDiv.remove(),t.innerWidth=e,this.createSlidingBanner(t))}createSlidingBanner(t){if(this.previousWidth=window.innerWidth,t.innerWidth<this.getBreakpoints().sm&&!t.enableOnMobile)return;let e=this.createBannerDiv(t),n=this.createHandleDiv(t.displayPosition),i=this.createCloseButton(t.displayCloseButton,e),r=c("div","brainst-sliding-banner-handle-icon","brainst-sliding-banner-handle-icon__".concat("right"===t.displayPosition?"open":"close"));l(n,i,r,p("brainst-sliding-banner-handle__title")(t.title)),l(e,n),l(document.body,e),t.handleWidth=n.offsetWidth;let s=this.applyBannerStyles(e,t);l(e,this.createAdvertiseAnchor(t,s)),this.addEvents(e,n,r,t.displayPosition,s),this.bannerDiv=e}createBannerDiv(t){let e=c("div","brainst-sliding-banner");return e.style.setProperty("--brainst-advertise-z-index",t.OfferZIndex),e.style.setProperty("--brainst-advertise-background",t.background),e}createHandleDiv(t){let e=c("div","brainst-sliding-banner-handle","btn","btn-primary");return e.style.setProperty("border-top-".concat(t,"-radius"),"unset"),e.style.setProperty("border-bottom-".concat(t,"-radius"),"unset"),e}createCloseButton(t,e){if(t){let t=c("button","brainst-sliding-banner-handle__button");return t.innerText="X",t.addEventListener("click",()=>e.remove()),t}return!1}applyBannerStyles(t,e){let n=Math.min(e.width,e.innerWidth-e.handleWidth-10);return t.style.setProperty("--brainst-advertise-width","".concat(n,"px")),t.style.transition="right ".concat(e.animationSpeed,"s ease-out, left ").concat(e.animationSpeed,"s ease-out"),t.style["left"===e.displayPosition?"left":"right"]="-".concat(n,"px"),"left"===e.displayPosition&&(t.style.flexDirection="row-reverse"),n}createAdvertiseAnchor(t,e){let n=c("a","brainst-sliding-banner-advertise");return n.href=t.link,n.title=t.title,t.internalLink||(n.rel="noopener",n.target="_blank"),t.labelShow&&l(n,this.createLabelSpan(t)),l(n,this.createResponsiveImage(t.media,e)),n}createLabelSpan(t){let e=c("span","brainst-sliding-banner-advertise-label");e.innerText=t.label,e.style.setProperty("--brainst-advertise-label-background",t.labelColour),e.style.setProperty("--brainst-advertise-label-colour",t.labelTextColour),e.style.setProperty("--brainst-advertise-label-border-radios","".concat(t.labelBorderRadios,"rem"));let n=this.getLabelPosition(t);return e.style.setProperty("--brainst-advertise-label-".concat(n.column),n.columnValue),e.style.setProperty("--brainst-advertise-label-".concat(n.row),"10px"),e}addEvents(t,e,n,i,r){this.parentContainer=e.parentElement,this.handleEl=e;let s={start:this.handleLongPressStart.bind(this),end:this.handleLongPressEnd.bind(this)};e.addEventListener("mousedown",s.start),e.addEventListener("touchstart",s.start),e.addEventListener("mouseup",s.end),e.addEventListener("mouseleave",s.end),e.addEventListener("touchend",s.end),e.addEventListener("touchcancel",s.end),e.addEventListener("click",()=>{if(this.stopClickEvent)return;let e=n.classList.contains("brainst-sliding-banner-handle-icon__open");t.style[i]=("left"===i?!e:e)?"0":"-".concat(r,"px"),g(n,"brainst-sliding-banner-handle-icon__open","brainst-sliding-banner-handle-icon__close")})}getLabelPosition(t){let e="right"===t.displayPosition?t.handleWidth+10:10,n="left"===t.displayPosition?t.handleWidth+10:10,i={topLeft:{column:"left",columnValue:"".concat(e,"px"),row:"top"},topRight:{column:"right",columnValue:"".concat(n,"px"),row:"top"},bottomRight:{column:"right",columnValue:"".concat(n,"px"),row:"bottom"},bottomLeft:{column:"left",columnValue:"".concat(e,"px"),row:"bottom"}};return i[t.labelPlacement]||i.topLeft}createResponsiveImage(t,e){let n=t.thumbnails.map(t=>{let{width:n}=t;return"(min-width: ".concat(n,"px) ").concat((e>=n?n:e)-1,"px")}).reverse().join(", "),i=t.thumbnails.map(t=>{let{url:e,width:n}=t;return"".concat(e," ").concat(n,"w")}).reverse().join(", "),r=c("img","brainst-sliding-banner-advertise-image");return r.classList.add("cms-image"),r.alt=t.alt,r.setAttribute("loading","lazy"),r.setAttribute("data-object-fit","cover"),r.src=t.url,r.srcset=i,r.sizes="".concat(n," , (min-width: 0px) ").concat(e-1,"px, ").concat(this.pxToVw(e),"vw"),r}pxToVw(t){return t/window.innerWidth*100}handleLongPressStart(t){t.preventDefault(),this.stopClickEvent=!1,this.longPressTimeout=setTimeout(()=>{navigator.vibrate&&navigator.vibrate(50),this.startDrag(t)},500)}handleLongPressEnd(t){clearTimeout(this.longPressTimeout),this.stopClickEvent||"touchend"!==t.type||this.handleEl.click()}startDrag(t){this.handleEl.style.boxShadow="0 0 12px #000000",this.isDragging=!0,this.startY=t.clientY||t.touches[0].clientY,this.startTop=this.parentContainer.offsetTop,this.stopClickEvent=!0,document.addEventListener("mousemove",this.moveDrag.bind(this)),document.addEventListener("touchmove",this.moveDrag.bind(this)),document.addEventListener("mouseup",this.endDrag.bind(this)),document.addEventListener("touchend",this.endDrag.bind(this))}moveDrag(t){if(!this.isDragging)return;let e=this.parentContainer.offsetHeight/2,n=(void 0!==t.clientY?t.clientY:t.touches&&t.touches[0]?t.touches[0].clientY:0)-this.startY,i=this.startTop+n;i=Math.max(e,Math.min(i,this.screenHeight-e)),this.parentContainer.style.top="".concat(i,"px")}endDrag(){this.isDragging=!1,document.removeEventListener("mousemove",this.moveDrag.bind(this)),document.removeEventListener("touchmove",this.moveDrag.bind(this)),document.removeEventListener("mouseup",this.endDrag.bind(this)),document.removeEventListener("touchend",this.endDrag.bind(this)),this.handleEl.style.removeProperty("box-shadow")}getBreakpoints(){return{xs:0,sm:576,md:768,lg:992,xl:1200,xxl:1400,...window.breakpoints}}}b.options={displayCloseButton:!1,displayPosition:"right",offerZIndex:1045,animationSpeed:.5,internalLink:!1},window.PluginManager.register("BrainstSlidingBanner",b)})()})();