<!-- eslint-disable-next-line sw-deprecation-rules/no-twigjs-blocks -->
{% block brainst_sliding_banner_list %}
    <!-- eslint-disable-next-line sw-deprecation-rules/no-twigjs-blocks -->
    {% block brainst_sliding_banner_list %}
        <sw-page class="sas-blog-list">

            <template #smart-bar-header>
                <h2>
                    <!-- eslint-disable-next-line sw-deprecation-rules/no-twigjs-blocks -->
                    {% block brainst_sliding_banner_list_smart_bar_header_title_text %}
                        {{ $tc('sliding.list.title') }}
                    {% endblock %}
                    <!-- eslint-disable-next-line sw-deprecation-rules/no-twigjs-blocks -->
                    {% block brainst_sliding_banner_list_smart_bar_header_amount %}
                        <span v-if="!isLoading" class="sw-page__smart-bar-amount">
                            ({{ total }})
                        </span>
                    {% endblock %}
                </h2>
            </template>

            <template #language-switch>
                <sw-language-switch @on-change="changeLanguage"></sw-language-switch>
            </template>

            <!-- eslint-disable-next-line sw-deprecation-rules/no-twigjs-blocks -->
            {% block brainst_sliding_banner_list_smart_bar_actions %}
                <template #smart-bar-actions>
                    <!-- eslint-disable-next-line sw-deprecation-rules/no-twigjs-blocks -->
                    {% block brainst_sliding_banner_list_smart_bar_actions_add %}
                        <sw-button :routerLink="{ name: 'brainst.sliding.banner.create' }" variant="primary">
                            {{ $tc('sliding.list.add') }}
                        </sw-button>
                    {% endblock %}
                </template>
            {% endblock %}

            <template #content>
                <!-- eslint-disable-next-line sw-deprecation-rules/no-twigjs-blocks -->
                {% block brainst_sliding_banner_list_content %}
                    <sw-entity-listing
                            v-if="slidingBannerEntries"
                            :items="slidingBannerEntries"
                            :repository="slidingBannerEntriesRepository"
                            :showSelection="false"
                            :columns="columns"
                            showSelection
                            detailRoute="brainst.sliding.banner.detail"
                            @update-records="updateRecords($event)"
                    >
                    </sw-entity-listing>
                {% endblock %}

                <!-- eslint-disable-next-line sw-deprecation-rules/no-twigjs-blocks -->
                {% block brainst_sliding_banner_list_empty_state %}
                    <sw-empty-state v-if="!isLoading && !total" :title="$tc('sliding.list.empty-message')">
                        {{ $tc('sliding.list.empty-message') }}
                    </sw-empty-state>
                {% endblock %}
            </template>
            
        </sw-page>
    {% endblock %}

{% endblock %}