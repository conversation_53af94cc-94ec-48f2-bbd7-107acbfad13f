<sw-page class="sas-blog-list">

    <template #smart-bar-header>
        <h2>
            <!-- eslint-disable-next-line sw-deprecation-rules/no-twigjs-blocks -->
            {% block brainst_sliding_banner_detail_smart_bar_header_title_text %}
                {{ $tc('sliding.details.title') }}
            {% endblock %}
        </h2>
    </template>
    <template #language-switch>
        <sw-language-switch @on-change="changeLanguage"
                            :abort-change-function="abortOnLanguageChange"
                            :disabled="!brainstSlidingBannerId"></sw-language-switch>
    </template>

    <!-- eslint-disable-next-line sw-deprecation-rules/no-twigjs-blocks -->
    {% block brainst_sliding_banner_detail_smart_bar_actions %}

        <template #smart-bar-actions>
            <!-- eslint-disable-next-line sw-deprecation-rules/no-twigjs-blocks -->
            {% block brainst_sliding_banner_detail_smart_bar_actions %}
                <sw-button class="sw-tooltip--wrapper" @click="saveSlidingBanner" variant="primary">
                    <sw-loader v-if="isLoading" size="25px">{{ $tc('sliding.details.save') }}</sw-loader>
                    <sw-icon v-else-if="isSaved" name="regular-checkmark-xs"
                             size="15px">{{ $tc('sliding.details.save') }}</sw-icon>
                    <span v-else>{{ $tc('sliding.details.save') }}</span>
                </sw-button>
            {% endblock %}
        </template>

    {% endblock %}

    <template #content>
        <!-- eslint-disable-next-line sw-deprecation-rules/no-twigjs-blocks -->
        {% block brainst_sliding_banner_detail_content %}
            <sw-card-view>
                <!-- eslint-disable-next-line sw-deprecation-rules/no-twigjs-blocks -->
                {% block brainst_sliding_banner_detail_language_info %}
                    <sw-language-info :entityDescription="translated.title"></sw-language-info>
                {% endblock %}
                <!-- eslint-disable-next-line sw-deprecation-rules/no-twigjs-blocks -->
                {% block brainst_sliding_banner_detail_banner_card %}
                    <sw-card :title="$tc('sliding.details.banner-title')"
                             positionIdentifier="brainst-sliding-banner-banner">

                        <!-- eslint-disable-next-line sw-deprecation-rules/no-twigjs-blocks -->
                        {% block brainst_sliding_banner_detail_title %}
                            <sw-text-field
                                    v-model:value="slidingBanner.title"
                                    :label="$tc('sliding.form.title.label')"
                                    :placeholder="translated.title || $tc('sliding.form.title.placeholder')"
                                    :error="slidingBannerTitleError"
                            ></sw-text-field>
                        {% endblock %}

                        <!-- eslint-disable-next-line sw-deprecation-rules/no-twigjs-blocks -->
                        {% block brainst_sliding_banner_detail_sales_channel %}
                            <sw-entity-single-select
                                    v-model:value="slidingBanner.salesChannelId"
                                    show-clearable-button
                                    value-property="id"
                                    entity="sales_channel"
                                    :label="$tc('sliding.form.sales-channel.label')"
                                    :placeholder="$tc('sliding.form.sales-channel.placeholder')"
                                    :error="slidingBannerSalesChannelIdError"
                            ></sw-entity-single-select>
                        {% endblock %}

                        <!-- eslint-disable-next-line sw-deprecation-rules/no-twigjs-blocks -->
                        {% block brainst_sliding_banner_detail_media %}
                            <div :class="slidingBannerMediaIdError ? 'sw-field has--error':'sw-field' ">
                                <sw-upload-listener
                                        upload-tag="brainst-sliding-banner-image"
                                        auto-upload
                                        @media-upload-finish="onSetMediaItem"
                                ></sw-upload-listener>
                                <sw-media-upload-v2
                                        :label="$tc('sw-category.base.menu.imageLabel')"
                                        variant="regular"
                                        :disabled="!acl.can('slidingBanner.editor')"
                                        :source="mediaItem"
                                        upload-tag="brainst-sliding-banner-image"
                                        :allow-multi-select="false"
                                        :default-folder="slidingBannerRepository.schema.entity"
                                        :fileAccept="fileAccept"
                                        @media-drop="onMediaDropped"
                                        @media-upload-sidebar-open="showMediaModal = true"
                                        @media-upload-remove-image="onRemoveMediaItem"
                                ></sw-media-upload-v2>
                                <sw-field-error :error="slidingBannerMediaIdError"/>
                            </div>
                        {% endblock %}

                        <!-- eslint-disable-next-line sw-deprecation-rules/no-twigjs-blocks -->
                        {% block brainst_sliding_banner_detail_media_modal %}
                            <sw-media-modal-v2
                                    v-if="showMediaModal"
                                    :allow-multi-select="false"
                                    :initial-folder-id="mediaDefaultFolderId"
                                    :entity-context="slidingBannerRepository.schema.entity"
                                    :fileAccept="fileAccept"
                                    @media-modal-selection-change="onMediaSelectionChange"
                                    @modal-close="showMediaModal = false"
                            ></sw-media-modal-v2>
                        {% endblock %}

                        <!-- eslint-disable-next-line sw-deprecation-rules/no-twigjs-blocks -->
                        {% block brainst_sliding_banner_detail_description %}
                            <sw-text-editor
                                    style="display: none"
                                    v-model:value="slidingBanner.description"
                                    :label="$tc('sliding.form.description.label')"
                                    :placeholder="translated.description || $tc('sliding.form.description.placeholder')"
                                    :error="slidingBannerDescriptionError"
                            ></sw-text-editor>
                        {% endblock %}

                        <!-- eslint-disable-next-line sw-deprecation-rules/no-twigjs-blocks -->
                        {% block brainst_sliding_banner_internal_link %}
                            <sw-switch-field
                                    v-model:value="slidingBanner.internalLink"
                                    :label="$tc('sliding.form.internnal-link.label')"
                                    :error="slidingBannerInternalLinkError">
                            </sw-switch-field>
                        {% endblock %}

                        <!-- eslint-disable-next-line sw-deprecation-rules/no-twigjs-blocks -->
                        {% block brainst_sliding_banner_detail_link %}
                            <sw-url-field
                                    v-model:value="slidingBanner.link"
                                    :label="$tc('sliding.form.link.label')"
                                    :placeholder="translated.link || $tc('sliding.form.link.placeholder')"
                                    :error="slidingBannerLinkError"
                            ></sw-url-field>
                        {% endblock %}

                        <!-- eslint-disable-next-line sw-deprecation-rules/no-twigjs-blocks -->
                        {% block brainst_sliding_banner_detail_priority %}
                            <sw-number-field
                                    numberType="int" :step="1" :min="1" :max="9999" :allowEmpty="true"
                                    v-model:value="slidingBanner.priority"
                                    :label="$tc('sliding.form.priority.label')"
                                    :placeholder="$tc('sliding.form.priority.placeholder')"
                                    :error="slidingBannerPriorityError">
                            </sw-number-field>
                        {% endblock %}

                        <!-- eslint-disable-next-line sw-deprecation-rules/no-twigjs-blocks -->
                        {% block brainst_sliding_banner_detail_background %}
                            <sw-colorpicker
                                    colorOutput="hex"
                                    :colorLabels="true"
                                    :zIndex="100"
                                    v-model:value="slidingBanner.background"
                                    :label="$tc('sliding.form.background.label')"
                                    :placeholder="$tc('sliding.form.background.placeholder')"
                                    :error="slidingBannerBackgroundError">sf
                            </sw-colorpicker>
                        {% endblock %}

                        <!-- eslint-disable-next-line sw-deprecation-rules/no-twigjs-blocks -->
                        {% block brainst_sliding_banner_detail_datepicker %}
                            <div class="brainst-sliding-datepicker">
                                <sw-datepicker dateType="datetime" size="small"
                                               v-model:value="slidingBanner.startAt"
                                               :label="$tc('sliding.form.start-at.label')"
                                               :error="slidingBannerStartAtError">
                                </sw-datepicker>

                                <sw-datepicker dateType="datetime" size="small"
                                               v-model:value="slidingBanner.endAt"
                                               :label="$tc('sliding.form.end-at.label')"
                                               :error="slidingBannerEndAtError">

                                </sw-datepicker>
                            </div>
                        {% endblock %}

                        <!-- eslint-disable-next-line sw-deprecation-rules/no-twigjs-blocks -->
                        {% block brainst_sliding_banner_detail_active %}
                            <sw-switch-field
                                    v-model:value="slidingBanner.active"
                                    :label="$tc('sliding.form.active.label')"
                                    :error="slidingBannerActiveError">

                            </sw-switch-field>
                        {% endblock %}

                        <!-- eslint-disable-next-line sw-deprecation-rules/no-twigjs-blocks -->
                        {% block brainst_sliding_banner_detail_priority %}
                            <sw-number-field
                                    numberType="int" :step="1" :min="50" :max="9999" :allowEmpty="true"
                                    v-model:value="slidingBanner.width"
                                    :label="$tc('sliding.form.width.label')"
                                    :placeholder="$tc('sliding.form.width.placeholder')"
                                    :error="slidingBannerWidthError">
                            </sw-number-field>
                        {% endblock %}
                    </sw-card>
                {% endblock %}
                <!-- eslint-disable-next-line sw-deprecation-rules/no-twigjs-blocks -->
                {% block brainst_sliding_banner_detail_label_card %}
                    <sw-card :title="$tc('sliding.details.label-title')"
                             positionIdentifier="brainst-sliding-banner-label">

                        <!-- eslint-disable-next-line sw-deprecation-rules/no-twigjs-blocks -->
                        {% block brainst_sliding_banner_detail_label %}
                            <sw-text-field
                                    v-model:value="slidingBanner.label"
                                    :label="$tc('sliding.form.label.label')"
                                    :placeholder="translated.label || $tc('sliding.form.label.placeholder')"
                                    :error="slidingBannerLabelError"
                            ></sw-text-field>
                        {% endblock %}


                        <!-- eslint-disable-next-line sw-deprecation-rules/no-twigjs-blocks -->
                        {% block brainst_sliding_banner_detail_label_show %}
                            <sw-switch-field v-model:value="slidingBanner.labelShow"
                                             :label="$tc('sliding.form.label-show.label')"
                                             :error="slidingBannerLabelShowError"></sw-switch-field>
                        {% endblock %}

                        <!-- eslint-disable-next-line sw-deprecation-rules/no-twigjs-blocks -->
                        {% block brainst_sliding_banner_detail_sales_label_placement %}
                            <sw-select-field
                                    v-model:value="slidingBanner.labelPlacement"
                                    :label="$tc('sliding.form.label-placement.label')"
                                    :error="slidingBannerLabelPlacementError"
                            >
                                <option value="topLeft">{{ $tc('sliding.form.label-placement.top-left') }}</option>
                                <option value="topRight">{{ $tc('sliding.form.label-placement.top-right') }}</option>
                                <option value="bottomLeft">{{ $tc('sliding.form.label-placement.bottom-left') }}</option>
                                <option value="bottomRight">{{ $tc('sliding.form.label-placement.bottom-right') }}</option>
                            </sw-select-field>
                        {% endblock %}

                        <!-- eslint-disable-next-line sw-deprecation-rules/no-twigjs-blocks -->
                        {% block brainst_sliding_banner_detail_label_colour %}
                            <sw-colorpicker
                                    colorOutput="hex"
                                    :colorLabels="true"
                                    :zIndex="100"
                                    v-model:value="slidingBanner.labelColour"
                                    :label="$tc('sliding.form.label-colour.label')"
                                    :placeholder="$tc('sliding.form.label-colour.placeholder')"
                                    :error="slidingBannerLabelColourError">
                            </sw-colorpicker>
                        {% endblock %}
                        <!-- eslint-disable-next-line sw-deprecation-rules/no-twigjs-blocks -->
                        {% block brainst_sliding_banner_detail_label_text_colour %}
                            <sw-colorpicker
                                    colorOutput="hex"
                                    :colorLabels="true"
                                    :zIndex="100"
                                    v-model:value="slidingBanner.labelTextColour"
                                    :label="$tc('sliding.form.label-text-colour.label')"
                                    :placeholder="$tc('sliding.form.label-text-colour.placeholder')"
                                    :error="slidingBannerLabelTextColourError">
                            </sw-colorpicker>
                        {% endblock %}
                        <!-- eslint-disable-next-line sw-deprecation-rules/no-twigjs-blocks -->
                        {% block brainst_sliding_banner_detail_label_border_radios %}
                            <sw-number-field
                                    numberType="float" :step="0.1" :min="0" :max="99.9" :allowEmpty="true"
                                    v-model:value="slidingBanner.labelBorderRadios"
                                    :label="$tc('sliding.form.label-border-radios.label')"
                                    :placeholder="$tc('sliding.form.label-border-radios.placeholder')"
                                    :error="slidingBannerLabelBorderRadiosError">
                            </sw-number-field>
                        {% endblock %}
                    </sw-card>
                {% endblock %}
            </sw-card-view>
        {% endblock %}
    </template>

</sw-page>
