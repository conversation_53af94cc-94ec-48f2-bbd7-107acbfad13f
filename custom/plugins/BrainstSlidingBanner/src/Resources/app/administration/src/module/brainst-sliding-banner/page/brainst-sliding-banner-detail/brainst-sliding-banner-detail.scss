@import "~scss/variables";

.sw-field {
  &.has--error {
    .sw-media-upload-v2 {
      .sw-media-upload-v2__content {
        .sw-media-upload-v2__header {
          .sw-media-upload-v2__label {
            color: $color-crimson-500;
          }
        }

        .sw-media-upload-v2__dropzone {
          background: $color-crimson-50;
          border-color: $color-crimson-500;
        }
      }
    }
  }
}

.brainst-sliding-datepicker {
  display: grid;

  .sw-field {
    grid-row: 6;

    &:first-child {
      padding-right: 10px;
    }

    &:nth-child(2) {
      padding-left: 10px;
    }
  }
}

.smart-bar__actions {
  .sw-button {
    .sw-button__content {
      height: 36px;
    }
  }
}


