import template from './brainst-sliding-banner-list.html.twig';

const Criteria = Shopware.Data.Criteria;

Shopware.Component.register('brainst-sliding-banner-list', {
    template,

    inject: ['repositoryFactory'],

    data() {
        return {
            slidingBannerEntries: null,
            page: 1,
            limit: 25,
            total: 0,
            isLoading: true
        };
    },

    metaInfo() {
        return {
            title: this.$createTitle(),
        };
    },

    created() {
        this.getList();
    },

    computed: {
        slidingBannerEntriesRepository() {
            return this.repositoryFactory.create('brainst_sliding_banner');
        },

        columns() {
            return [
                {
                    primary: true,
                    allowResize: true,
                    dataIndex: 'title',
                    label: this.$tc('sliding.list.table.title'),
                    property: 'translated.title',
                    routerLink: 'brainst.sliding.banner.detail',
                },
                {
                    property: 'priority',
                    label: this.$tc('sliding.list.table.priority'),
                    allowResize: true,
                    dataIndex: 'priority',
                    inlineEdit: 'number'
                },
                {
                    property: 'active',
                    label: this.$tc('sliding.list.table.active'),
                    align: 'center',
                    allowResize: true,
                    dataIndex: 'active',
                    inlineEdit: 'boolean'
                },
                {
                    property: 'width',
                    label: this.$tc('sliding.list.table.width'),
                    allowResize: true,
                    dataIndex: 'width',
                    inlineEdit: 'number'
                },
                {
                    property: 'startAt',
                    label: this.$tc('sliding.list.table.start-at'),
                    align: 'center',
                    allowResize: true,
                    dataIndex: 'startAt',
                },
                {
                    property: 'endAt',
                    label: this.$tc('sliding.list.table.end-at'),
                    align: 'center',
                    allowResize: true,
                    dataIndex: 'endAt',
                },
            ];
        },
    },

    methods: {
        changeLanguage(newLanguageId) {
            this.getList();
        },
        updateRecords(results) {
            this.total = results.total;
        },

        getList() {
            this.isLoading = true;
            
            const criteria = new Criteria(this.page, this.limit);
            criteria.addFields('title', 'active', 'priority', 'startAt', 'endAt', 'width');
            criteria.addSorting(Criteria.sort('createdAt', 'DESC', false));

            return this.slidingBannerEntriesRepository.search(criteria, Shopware.Context.api).then((results) => {
                this.slidingBannerEntries = results;
                this.isLoading = false;
                this.updateRecords(results);
            });
        },
    },
});