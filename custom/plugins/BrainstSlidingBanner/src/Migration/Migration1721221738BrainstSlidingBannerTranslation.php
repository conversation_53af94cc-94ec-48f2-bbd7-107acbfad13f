<?php declare(strict_types=1);

namespace Brainst\SlidingBanner\Migration;

use Doctrine\DBAL\Connection;
use Shopware\Core\Framework\Log\Package;
use Shopware\Core\Framework\Migration\MigrationStep;

/**
 * @internal
 */
#[Package('core')]
class Migration1721221738BrainstSlidingBannerTranslation extends MigrationStep
{
    public function getCreationTimestamp(): int
    {
        return 1721221738;
    }

    public function update(Connection $connection): void
    {
        $query = <<<SQL
        CREATE TABLE IF NOT EXISTS `brainst_sliding_banner_translation` (
            `brainst_sliding_banner_id` BINARY(16) NOT NULL,
            `language_id` BINARY(16) NOT NULL,
            `title` VARCHAR(50),
            `description` MEDIUMTEXT,
            `link` VARCHAR(255),
            `label` VARCHAR(50),
            `custom_fields` JSON,
            `created_at` DATETIME(3) NOT NULL,
            `updated_at` DATETIME(3) NULL,
            PRIMARY KEY (`brainst_sliding_banner_id`, `language_id`),
            CONSTRAINT `fk.brainst_sliding_banner_translation.brainst_sliding_banner_id` FOREIGN KEY (`brainst_sliding_banner_id`)
                REFERENCES `brainst_sliding_banner` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
            CONSTRAINT `fk.brainst_sliding_banner_translation.language_id` FOREIGN KEY (`language_id`)
                REFERENCES `language` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
        )
            ENGINE = InnoDB
            DEFAULT CHARSET = utf8mb4
            COLLATE = utf8mb4_unicode_ci;
        SQL;
        $connection->executeStatement($query);
    }
    
    public function updateDestructive(Connection $connection): void
    {
    }
}
