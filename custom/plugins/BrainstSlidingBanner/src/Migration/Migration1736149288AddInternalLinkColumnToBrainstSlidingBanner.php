<?php declare(strict_types=1);

namespace Brainst\SlidingBanner\Migration;

use Doctrine\DBAL\Connection;
use Shopware\Core\Framework\Migration\MigrationStep;

/**
 * @internal
 */
class Migration1736149288AddInternalLinkColumnToBrainstSlidingBanner extends MigrationStep
{
    public function getCreationTimestamp(): int
    {
        return 1736149288;
    }

    public function update(Connection $connection): void
    {
        $sql = <<<SQL
        ALTER TABLE `brainst_sliding_banner`
            ADD COLUMN `internal_link` TINYINT(1) NOT NULL DEFAULT 0 
            AFTER `active`;
        SQL;
        $connection->executeStatement($sql);
    }

    public function updateDestructive(Connection $connection): void
    {
    }
}
